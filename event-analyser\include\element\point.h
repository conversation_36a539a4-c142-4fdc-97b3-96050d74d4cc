/**
 * Project AI事件分析模块
 */

#ifndef _POINT_H
#define _POINT_H

#include "module_def.h"

/**
* 基本点
*/
namespace evt
{
	struct EVT_EXPORT Point {
		float x;
		float y;

		Point():x(0.0f), y(0.0f) {};
		Point(float _x, float _y):x(_x), y(_y) {};

		bool operator==(const Point &p)
		{
			return p.x == x && p.y == y;
		}

		bool operator!=(const Point &p)
		{
			return !operator==(p);
		}
	};
}
#endif //_POINT_H