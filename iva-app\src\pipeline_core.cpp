#include "pipeline_factory.h"
#include "ivaconfig.hpp"
#include "ivameta.h"
#include "version.h"

#ifdef ENABLE_IMAGE_ANALYSER
#include "detect_config.hpp"
#endif

#ifdef NVIDIA
#include "roiobject_config.hpp"
#endif

namespace iva::pipeline
{
	/**
	 * @brief 获取模型路径
	 */
	std::string getInferConfigPath(const std::string& modelName)
	{
		gchar str_buffer[256];
		std::string modelPath = SETTINGS->modelPath();
		g_snprintf(str_buffer, sizeof(str_buffer), "%s/%s/config_infer.txt", modelPath.c_str(), modelName.c_str());
		return str_buffer;
	}

	/**
	 * @brief 创建单个通道的pipeline的core bin (推理、跟踪 等插件配置)
	 * @param[in] 通道序号(从0开始)
	 */
	GstElement* createCoreBin(int index)
	{
		gchar elem_name[50];

		// core bin
		GST_ELEMENT_INDEX_NAME(elem_name, "core_bin", index);
		auto core_bin = gst_bin_new(elem_name);
		GST_ELEMENT_CHECK(core_bin, elem_name);
		ElementBin elementBin(core_bin);

		// core queue
		GST_ELEMENT_INDEX_NAME(elem_name, "core_queue", index);
		auto core_queue = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(core_queue, elem_name);
#ifndef NVIDIA
		g_object_set(G_OBJECT(core_queue), "leaky", 2, NULL);
		g_object_set(G_OBJECT(core_queue), "flush-on-eos", true, NULL);
		g_object_set(G_OBJECT(core_queue), "max-size-buffers", 40, NULL);
		g_object_set(G_OBJECT(core_queue), "max-size-bytes", 0, NULL);
#endif
		elementBin.linkNext(core_queue);

		// 主模型推理 (五分类模型 或 三分类车辆模型[usingPersonModel]) unique-id=1
		GST_ELEMENT_INDEX_NAME(elem_name, "core_infer", index);
		auto core_pgie = gst_element_factory_make(AI_INFER, elem_name);
		GST_ELEMENT_CHECK(core_pgie, elem_name);
        std::string infer_file_path = getInferConfigPath(VEHICLE_MODEL);
		g_object_set(G_OBJECT(core_pgie), "config-file-path", infer_file_path.c_str(), "batch-size", totalChannelSize, "interval", SETTINGS->vehicleInferInterval(),
			"gpu-id", deviceId, "unique-id", IVA_VEHICLE_MODEL_UNIQUE_ID, NULL);
		elementBin.linkNext(core_pgie);

		// 额外 使用行人模型  unique-id=2
#ifdef NVIDIA
		if (SETTINGS->usingPersonModel())
		{
			g_object_set(G_OBJECT(core_pgie), "class-ids-map", "0>1:1>2:2>3", NULL);

			GST_ELEMENT_INDEX_NAME(elem_name, "extra_pgie_queue", index);
			auto extra_pgie_queue = gst_element_factory_make("queue", elem_name);
			GST_ELEMENT_CHECK(extra_pgie_queue, elem_name);
			elementBin.linkNext(extra_pgie_queue);

			GST_ELEMENT_INDEX_NAME(elem_name, "extra_infer", index);
			auto extra_pgie = gst_element_factory_make(AI_INFER, elem_name);
			GST_ELEMENT_CHECK(extra_pgie, elem_name);
			infer_file_path = getInferConfigPath(NON_VEHICLE_MODEL);
			g_object_set(G_OBJECT(extra_pgie), "config-file-path", infer_file_path.c_str(), "batch-size", totalChannelSize,
				"interval", SETTINGS->nonVehicleInferInterval(), "class-ids-map", "0>0:1>4", "gpu-id", deviceId, NULL);
			elementBin.linkNext(extra_pgie);
		}
#endif

		// AI 图像模型推理
#if defined(ENABLE_IMAGE_ANALYSER)
		// imageanalyser queue1
		if (DETECT_CFG->detectRoadblock() || DETECT_CFG->detectFireSmoke())
		{
			GST_ELEMENT_INDEX_NAME(elem_name, "imageanalyser_queue1", index);
			auto imageanalyser_queue1 = gst_element_factory_make("queue", elem_name);
			GST_ELEMENT_CHECK(imageanalyser_queue1, elem_name);
			elementBin.linkNext(imageanalyser_queue1);
		}
		int gie_unique_id = 10;
		char class_ids_map[64] = {};

#if !defined(HUAWEI) && !defined(CAMBRICON_MLU370)
		// (路障)
		if (!SETTINGS->usingThrowawayRoadblockModel() && DETECT_CFG->detectRoadblock())
		{
			sprintf(class_ids_map, "0>%d", IVA_TARGET_TYPE_ROADBLOCK);
			GST_ELEMENT_INDEX_NAME(elem_name, "roadblock_infer", index);
			auto roadblock_infer = gst_element_factory_make(AI_INFER, elem_name);
			GST_ELEMENT_CHECK(roadblock_infer, elem_name);
            infer_file_path = getInferConfigPath(ROADBLOCK_MODEL);
			g_object_set(G_OBJECT(roadblock_infer), "config-file-path", infer_file_path.c_str(), "batch-size", totalChannelSize,
				"interval", SETTINGS->roadblockInterval(), "class-ids-map", class_ids_map, "unique-id", IVA_ROADBLOCK_MODEL_UNIQUE_ID, "gpu-id", deviceId, NULL);
			elementBin.linkNext(roadblock_infer);
		}
#endif

		//  (烟火)
		if (DETECT_CFG->detectFireSmoke())
		{
#ifndef NVIDIA
            GST_ELEMENT_INDEX_NAME(elem_name, "fire_smoke_queue", index);
            auto fire_smoke_queue = gst_element_factory_make("queue", elem_name);
            GST_ELEMENT_CHECK(fire_smoke_queue, elem_name);
            elementBin.linkNext(fire_smoke_queue);
#endif
			GST_ELEMENT_INDEX_NAME(elem_name, "firesmoke_infer", index);
			auto firesmoke_infer = gst_element_factory_make(AI_INFER, elem_name);
			GST_ELEMENT_CHECK(firesmoke_infer, elem_name);
			infer_file_path = getInferConfigPath(FIRESMOKE_MODEL);
			sprintf(class_ids_map, "0>%d:1>%d:2>%d", IVA_TARGET_TYPE_FIRE, IVA_TARGET_TYPE_SMOKE, IVA_TARGET_TYPE_LIGHT);
			g_object_set(G_OBJECT(firesmoke_infer), "config-file-path", infer_file_path.c_str(), "batch-size", totalChannelSize,
				"interval", SETTINGS->firesmokeInterval(), "class-ids-map", class_ids_map, "unique-id", IVA_FIRE_SMOKE_MODEL_UNIQUE_ID, "gpu-id", deviceId, NULL);
			elementBin.linkNext(firesmoke_infer);
		}

#if defined(CAMBRICON_MLU370) || defined(NVIDIA)
        GstElement* landslide_infer = nullptr;
        if (DETECT_CFG->detectLandslide())
        {
            g_snprintf(elem_name, sizeof(elem_name), "landslide_infer%d", index);
            landslide_infer = gst_element_factory_make(AI_INFER, elem_name);
            GST_ELEMENT_CHECK(landslide_infer, elem_name);

            sprintf(class_ids_map, "0>%d", IVA_TARGET_TYPE_LANDSLIDE);
            infer_file_path = getInferConfigPath(LANDSLIDE_MODEL);
            g_object_set(G_OBJECT(landslide_infer), "config-file-path", infer_file_path.c_str(), "batch-size", totalChannelSize,
                         "interval", SETTINGS->landslideInterval(), "class-ids-map", class_ids_map, "unique-id", gie_unique_id++, "gpu-id", deviceId, NULL);
        }
#endif

		// AI 图像模型推理 (偏移|车道线)
		if (DETECT_CFG->detectCamOffset())
		{
			// imageanalyser queue2
			GST_ELEMENT_INDEX_NAME(elem_name, "imageanalyser_queue2", index);
			auto imageanalyser_queue2 = gst_element_factory_make("queue", elem_name);
			GST_ELEMENT_CHECK(imageanalyser_queue2, elem_name);
			elementBin.linkNext(imageanalyser_queue2);

			GST_ELEMENT_INDEX_NAME(elem_name, "lane_infer", index);
			auto cameraoffset_infer = gst_element_factory_make(AI_INFER, elem_name);
			GST_ELEMENT_CHECK(cameraoffset_infer, elem_name);
			infer_file_path = getInferConfigPath(CAM_OFFSET_MODEL);
			g_object_set(G_OBJECT(cameraoffset_infer), "config-file-path", infer_file_path.c_str(), "batch-size", totalChannelSize,
				"interval", SETTINGS->laneInterval(), "unique-id", IVA_CAMERA_OFFSET_MODEL_UNIQUE_ID, "gpu-id", deviceId, NULL);
			elementBin.linkNext(cameraoffset_infer);
		}

#if defined(HUAWEI) || defined(CAMBRICON_MLU370)
        /// 抛洒物
        if (DETECT_CFG->detectThrowaway() || DETECT_CFG->detectRoadblock())
        {
            // imageanalyser queue2
            GST_ELEMENT_INDEX_NAME(elem_name, "throwaway_queue", index);
            auto throwaway_queue = gst_element_factory_make("queue", elem_name);
            GST_ELEMENT_CHECK(throwaway_queue, elem_name);
            elementBin.linkNext(throwaway_queue);

            GST_ELEMENT_INDEX_NAME(elem_name, "throwaway_infer", index);
            auto throwaway_infer = gst_element_factory_make(AI_INFER, elem_name);
            GST_ELEMENT_CHECK(throwaway_infer, elem_name);
            infer_file_path = getInferConfigPath(THROWAWAY_MODEL);
            sprintf(class_ids_map, "0>%d:1>%d", IVA_TARGET_TYPE_THROWAYAY, IVA_TARGET_TYPE_ROADBLOCK);
            g_object_set(G_OBJECT(throwaway_infer), "config-file-path", infer_file_path.c_str(), "batch-size", totalChannelSize,
                         "interval", SETTINGS->roiobjectInterval(), "class-ids-map", class_ids_map, "unique-id", IVA_THROWAYAY_MODEL_UNIQUE_ID, "gpu-id", deviceId, NULL);
            elementBin.linkNext(throwaway_infer);
        }
#elif defined(NVIDIA)
        // 抛洒物
        GstElement* throwaway_infer = nullptr;
        GstElement* throwaway_queue = nullptr;
        if (DETECT_CFG->detectThrowaway() && ROIOBJECT_CFG->detectThrowawayMode())
        {
            g_snprintf(elem_name, sizeof(elem_name), "throwaway_queue%d", index);
            throwaway_queue = gst_element_factory_make("queue", elem_name);
            GST_ELEMENT_CHECK(throwaway_queue, elem_name);

            g_snprintf(elem_name, sizeof(elem_name), "throwaway_infer%d", index);
            throwaway_infer = gst_element_factory_make("nvinfer", elem_name);
            GST_ELEMENT_CHECK(throwaway_infer, elem_name);
            infer_file_path = getInferConfigPath(THROWAWAY_MODEL);
            if (SETTINGS->usingThrowawayRoadblockModel())
            {
                sprintf(class_ids_map, "0>%d:1>%d", IVA_TARGET_TYPE_THROWAYAY, IVA_TARGET_TYPE_ROADBLOCK);
                g_object_set(G_OBJECT(throwaway_infer), "config-file-path", infer_file_path.c_str(), "batch-size", totalChannelSize,
                             "interval", SETTINGS->roadblockInterval(), "class-ids-map", class_ids_map, "unique-id", gie_unique_id++, "gpu-id", deviceId, NULL);
            }
            else
            {
                sprintf(class_ids_map, "0>%d", IVA_TARGET_TYPE_THROWAYAY);
                g_object_set(G_OBJECT(throwaway_infer), "config-file-path", infer_file_path.c_str(), "batch-size", totalChannelSize,
                             "interval", 25, "class-ids-map", class_ids_map, "unique-id", gie_unique_id++, "gpu-id", deviceId, NULL);
            }
        }
        if (throwaway_infer && SETTINGS->usingThrowawayRoadblockModel())
        {
            elementBin.linkNext(throwaway_queue);
            elementBin.linkNext(throwaway_infer);
        }
#endif


#endif

#ifdef NVIDIA
		// core queue2
		GST_ELEMENT_INDEX_NAME(elem_name, "core_queue2", index);
		auto core_queue2 = gst_element_factory_make("queue", elem_name);
		GST_ELEMENT_CHECK(core_queue2, elem_name);
		elementBin.linkNext(core_queue2);

		// tracker TODO 寒武纪tracker 暂不支持批量及时序跟踪 暂放至sink部分
		GST_ELEMENT_INDEX_NAME(elem_name, "core_tracker", index);
		auto core_tracker = gst_element_factory_make(AI_TRACKER, elem_name);
		GST_ELEMENT_CHECK(core_tracker, elem_name);
		elementBin.linkNext(core_tracker);
#endif

#ifdef NVIDIA
		unsigned int ds_ver_major, ds_ver_minor;
		nvds_version(&ds_ver_major, &ds_ver_minor);
		if (ds_ver_major > 5)
		{
			g_object_set(G_OBJECT(core_tracker), "ll-lib-file", "/opt/nvidia/deepstream/deepstream/lib/libnvds_nvmultiobjecttracker.so", NULL);
			g_object_set(G_OBJECT(core_tracker), "ll-config-file", "/data/opt/models/config_tracker_IOU.yml", NULL);
		}
		else
		{
			g_object_set(G_OBJECT(core_tracker), "ll-lib-file", "/opt/nvidia/deepstream/deepstream/lib/libnvds_mot_iou.so", NULL);
		}

		g_object_set(G_OBJECT(core_tracker), "enable-batch-process", 1, NULL);
		g_object_set(G_OBJECT(core_tracker), "tracker-width", 640, NULL); // 320
		g_object_set(G_OBJECT(core_tracker), "tracker-height", 368, NULL); // 184
		g_object_set(G_OBJECT(core_tracker), "gpu-id", deviceId, NULL);
#endif // !NVIDIA

        // 二级分类推理 （反光衣等）
#ifdef NVIDIA
        fs::path config_path(SETTINGS->modelPath() + "/secondary");
        if (fs::exists(config_path))
        {
            int element_index = 0;
            fs::directory_iterator entry_list(config_path);
            for (auto& it : entry_list)
            {
                std::string config_file_name = it.path().filename();
                if (config_file_name.find("config_infer") == std::string::npos)
                    continue;
                
                // 检查模型类型并决定是否加载
                bool should_load = true;
                std::string model_path = it.path().c_str();

                if (model_path.find("vehicletype") != std::string::npos)
                {
                    // 如果在事件中检测车辆类型，则不在pipeline中加载
                    should_load = !(SETTINGS->detectVehicleTypeOnStop() || SETTINGS->detectVehicleTypeOnOpposite());
                }
                else if (model_path.find("vehiclecolor") != std::string::npos)
                {
                    // 如果在事件中检测车辆颜色，则不在pipeline中加载
                    should_load = !(SETTINGS->detectVehicleColorOnStop() || SETTINGS->detectVehicleColorOnOpposite());
                }
                else if (model_path.find("personclothes") != std::string::npos)
                {
                    // 如果在事件中检测行人衣物，则不在pipeline中加载
                    should_load = !SETTINGS->detectPersonClothesOnPedstrain();
                }
                
                if (!should_load)
                    continue;

                g_snprintf(elem_name, sizeof(elem_name), "secondary_infer%d_%d", index, element_index++);
                auto secondary_infer = gst_element_factory_make("nvinfer", elem_name);
                GST_ELEMENT_CHECK(secondary_infer, elem_name);
                elementBin.linkNext(secondary_infer);

                g_object_set(G_OBJECT(secondary_infer), "config-file-path", it.path().c_str(), "process-mode", 2,
                             "gpu-id", deviceId, NULL);
            }
        }
#endif

#ifdef NVIDIA
        // 没有使用混合模型，抛洒物则只能接在跟踪后面
        if (throwaway_infer && !SETTINGS->usingThrowawayRoadblockModel())
        {
            elementBin.linkNext(throwaway_queue);
            elementBin.linkNext(throwaway_infer);
        }
#endif

#if defined(CAMBRICON_MLU370) || defined(NVIDIA)
        if (landslide_infer)
        {
            elementBin.linkNext(landslide_infer);
        }
#endif

		NVGSTDS_BIN_ADD_GHOST_PAD(core_bin, elementBin.first, "sink");
		NVGSTDS_BIN_ADD_GHOST_PAD(core_bin, elementBin.last, "src");

		return core_bin;
	}
}

