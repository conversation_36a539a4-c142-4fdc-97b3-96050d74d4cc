/**
 * Project AI事件分析模块
 */

#include "opposite_event_detector.h"
#include "util/scene_utility.h"

/**
 * OppositeEventDetector implementation
 * 
 * 逆行检测器
 */
namespace evt
{
	/**
	 * @param direction
	 */
	OppositeEventDetector::OppositeEventDetector(Vector2D dir) {
		this->direction = dir;
		this->checkFrameCount = OPPOSITE_MIN_CHECK_FRAME_COUNT;
	}

	/**
	 * @param type
	 * @return bool
	 */
	bool OppositeEventDetector::checkTargetType(TargetType type) {
		return type == TargetType::TargetType_Car
			|| type == TargetType::TargetType_Bus
			|| type == TargetType::TargetType_Truck;
	}

	/**
	 * 事件范围匹配标记检查
	 * @param target
	 */
	bool OppositeEventDetector::eventMatchSpace(TargetPtr target)
	{
		for (auto& evt : holdEvents)
		{
			if (evt->getState() == EventState_Released)
				return false;
		}
		return checkTargetType(target->getType());
	}

	/**
	 * 事件分析核心逻辑
	 */
	EventPtr OppositeEventDetector::process(TargetPtr target)
	{
		CHECK_TARGET_TYPE(target);

		// 跨越ROI 基本上是偏移
		if (target->IsPassedROI()) return NULL;

		// 检查车辆最小像素
		auto rect = target->getLatestRect();
		if (rect.width < vehicleMinSize)
			return NULL;
		if (rect.height < vehicleMinSize)
			return NULL;

		// 更新目标轨迹 折点情况
		target->updateTurnIndex(this->direction, OPPOSITE_TURN_CHECK_ANGLE);
		CHECK_TARGET_FRAME_COUNT(target);

		int turnIndex = target->getTurnIndex();
		int trackIndex = target->getTrackIndex();
		int turnPassedCount = trackIndex - turnIndex;

		//if (target->getOppositeCount() > 10)
		//{
		//	auto info = "p: " + std::to_string(target->getOppositeCount());
		//	info += " c: " + std::to_string(turnPassedCount);
		//	target->updateExtraData(info);
		//}
		if (turnPassedCount < OPPOSITE_MIN_TURN_FRAME_COUNT) return NULL;

		// 行车折点距离是否满足
		auto turnPos = target->getIndexPos(turnIndex, true);
		auto trackPos = target->getIndexPos(trackIndex, true);

		/*if (target->getOppositeCount() > 2)
		{
			auto info = "p: " + std::to_string(target->getOppositeCount());
			info += " c: " + std::to_string(turnPassedCount);
			info += " dis: " + std::to_string(calculateDistance(turnPos, trackPos));
			target->updateExtraData(info);
		}*/

		if (!checkIfDistanceGreaterThan(turnPos, trackPos, checkDistance)) return NULL;

		// 逆行计数检查
		float oppositeCount = (float)target->getOppositeCount();
		float oppositeRate = oppositeCount / (float)turnPassedCount;   
		if (oppositeRate < checkRate || oppositeCount < OPPOSITE_MIN_TURN_FRAME_COUNT)
			return NULL;

		// 检查 新事件 是否为最近解除事件
		int eventDistance = 0xffffff;
		EventPtr latestEvt = NULL;
		for (auto& evt : holdEvents)
		{
			if (evt->getState() == EventState_Released)
			{
				int dis = calculateDistanceSquare(trackPos, evt->getOccurRect().getCenterPosition());
				if (latestEvt == NULL || dis < eventDistance)
				{
					eventDistance = dis;
					latestEvt = evt;
				}
			}
		}

		// 最近解除事件 改为维持状态
		bool isNew = true;
		if (latestEvt != NULL)
		{
			isNew = false;
			latestEvt->setState(EventState_Maintaining);
			target->updateEvent(latestEvt.get());
		}
		return	isNew ? proposeNew(target, EventState_Confirming) :NULL;
	}

	/**
	 * 事件维持
	 * @param evt
	 */
	void OppositeEventDetector::onEventMaintaining(EventPtr evt, TargetPtr target ) 
	{
		TargetEventDetector::onEventMaintaining(evt, target);

		if (target == NULL || evt == NULL )
			return;

		if (evt->getTargetID() == target->getID())
		{
			evt->updateOccurRect(target->getLatestRect());
		}
	}

	/**
	 * 区域配置更新
	 */
	void OppositeEventDetector::onUpdateRegionConfig()
	{
		removeTime = 120.f;

		regionConfig.getValue<float>(removeTime, OPPOSITE_REMOVE_TIME);
		regionConfig.getValue<float>(checkDistance, OPPOSITE_DISTANCE);
		regionConfig.getValue<int>(checkAngle, OPPOSITE_ANGLE);
		regionConfig.getValue<int>(eventSpace, OPPOSITE_EVENT_SPACE);
		regionConfig.getValue<float>(checkRate, OPPOSITE_CHECK_RATE);
		regionConfig.getValue<int>(vehicleMinSize, OPPOSITE_MIN_SIZE);
		
	}

}
