/**
 * Project AI事件分析模块
 */

#ifndef _EVENTSTATE_H
#define _EVENTSTATE_H

namespace evt
{
	/**
	 * 事件状态
	 */
	enum EventState
	{
		EventState_None,
		// 候选
		EventState_Proposal, 
		// 确认
		EventState_Confirming, 
		// 维持
		EventState_Maintaining, 
		// 解除
		EventState_Released, 
		// 移除
		EventState_Removed 
	};

	/**
	 * 事件确认子状态
	 */
	enum EventConfirmState
	{
		EventConfirm_None,
		// 确认计数中
		EventConfirm_Counting,
		// 需要图像比对
		EventConfirm_ImageCompareNeeded, 
		// 图像比对中
		EventConfirm_ImageComparing 
	};
}
#endif //_EVENTSTATE_H