#include "opencv_frame_osd.h"
#include "log.h"
#include <sys/stat.h>

namespace ai
{
	CvFrameOsd::CvFrameOsd() {}
	CvFrameOsd::~CvFrameOsd()
	{
		deinit();
	}

	/**
	 * @brief      字体文件加载，创建freetype
	 * @param[in]  fontFile：字体文件所在路径
	 * @return     bool
	 */
	bool CvFrameOsd::init(std::string& fontFile)
	{
		struct stat buffer;
		bool isFileExist = (stat(fontFile.c_str(), &buffer) == 0);

		if (!isFileExist)
		{
			IVA_LOG_ERROR("[common] font file not found: {}", fontFile);
			return false;
		}

		freetype = cv::freetype::createFreeType2();
		freetype->loadFontData(fontFile, 0);
		return 	true;
	}

	void CvFrameOsd::deinit()
	{
	}

	/**
	 * @brief      在图片上绘制目标框，箭头，事件名称
	 * @param[in]  mat：需要叠加信息的图片
	 * @param[in]  rect：目标框
	 * @param[in]  eventName：事件名字
	 */
	void CvFrameOsd::drawTargets(cv::Mat& mat, cv::Rect rect, const std::string& eventName)
	{
		if (mat.empty())
			return;

		auto color = cv::Scalar(0, 0, 255);
		uint32_t penWidth = 1;
		double rate = 0.4;
		drawTargetRect(mat, rect, color, penWidth, rate);

		//在对应的目标,画箭头标识
		cv::Point ptText;
		int iArrowLen = penWidth * 25;                         ///< 箭头长度
		int iArrowSpace = 5;	                               ///< 箭头距离边框顶点距离
		//在斜上方画箭头
		int x1, x2, y1, y2;
		x1 = rect.x + rect.width + iArrowSpace + iArrowLen;	   ///< 箭头尾部x坐标
		y1 = rect.y - iArrowSpace - iArrowLen;				   ///< 箭头尾部y坐标
		y2 = rect.y - iArrowSpace;							   ///< 箭头顶点y坐标

		if (x1 > mat.cols)  //超出右边, 从左边画箭头
		{
			x1 = rect.x - iArrowSpace - iArrowLen;
			x2 = rect.x - iArrowSpace;
		}
		else
		{
			x2 = x1 - iArrowLen;
		}
		int fontHeight = 15;                                    ///< 字体高度
		ptText.x = x1 + 5;
		ptText.y = y1 - fontHeight;
		if (ptText.y < 0)  //说明字超出图片顶部，箭头和字都要向下移
		{
			y2 = rect.y + rect.height + iArrowSpace;
			y1 = y2 + iArrowLen;
			ptText.y = y1 + fontHeight;
		}
		cv::Point ptStart(x1, y1);
		cv::Point ptStop(x2, y2);

		try
		{
			freetype->putText(mat, eventName, ptText, fontHeight, cv::Scalar(0, 0, 255), -2, cv::LINE_AA, false);;
		}
		catch (cv::Exception e)
		{
			IVA_LOG_ERROR("[common] cv::freetype::putText: font file not found: {}", e.what());
		}

		drawArrow(mat, ptStart, ptStop, penWidth, color);

	}

	/**
	* @brief      在图片上绘制目标框并叠加半透明效果
	* @param[in]  mat：需要叠加信息的图片
	* @param[in]  nodeRect：目标框
	* @param[in]  color：绘笔颜色
	* @param[in]  iPenWidth：绘笔宽度
	* @param[in]  dRate：叠加第二张图时的数组元素权重
	*/
	void CvFrameOsd::drawTargetRect(cv::Mat& mat, cv::Rect nodeRect, cv::Scalar color, uint32_t iPenWidth, double dRate)
	{
		cv::rectangle(mat, nodeRect, color, iPenWidth);
		//边角加粗画线
		int x1 = nodeRect.x - iPenWidth;
		int y1 = nodeRect.y - iPenWidth;
		int x2 = nodeRect.x + nodeRect.width + iPenWidth - 1;
		int y2 = nodeRect.y + nodeRect.height + iPenWidth - 1;
		int len = 10; //像素边框
		cv::line(mat, cv::Point(x1, y1), cv::Point(x1, y1 + len), color);  //左上角竖线
		cv::line(mat, cv::Point(x1, y1), cv::Point(x1 + len, y1), color);  //左上角横线
		cv::line(mat, cv::Point(x2, y1), cv::Point(x2, y1 + len), color);  //右上角竖线
		cv::line(mat, cv::Point(x2, y1), cv::Point(x2 - len, y1), color);  //右上角横线
		cv::line(mat, cv::Point(x1, y2), cv::Point(x1, y2 - len), color);  //左下角竖线
		cv::line(mat, cv::Point(x1, y2), cv::Point(x1 + len, y2), color);  //左下角横线
		cv::line(mat, cv::Point(x2, y2), cv::Point(x2, y2 - len), color);  //右下角竖线
		cv::line(mat, cv::Point(x2, y2), cv::Point(x2 - len, y2), color);  //右下角横线
		drawFillRect(mat, nodeRect, dRate, color);
	}

	/**
	* @brief      在目标框上绘制箭头，指示目标
	* @param[in]  mat：需要叠加信息的图片
	* @param[in]  nodeRect：目标框
	* @param[in]  color：绘笔颜色
	* @param[in]  iPenWidth：绘笔宽度
	* @param[in]  dRate：叠加第二张图时的数组元素权重
	*/
	void CvFrameOsd::drawArrow(cv::Mat& mat, cv::Point ptStart, cv::Point ptStop, uint32_t iPenWidth, cv::Scalar color)
	{
		//   cv::Scalar color(0, 0, 255); //红色
		cv::line(mat, ptStart, ptStop, color, iPenWidth);

		double xx = ptStop.x - ptStart.x;
		double yy = ptStop.y - ptStart.y;
		double alpha_angle;//弧度   
		double pi = 3.1415926535;

		if (xx< 0 && xx> -0) //坐标的原点   
			alpha_angle = pi / 2;
		else
			alpha_angle = fabs(atan(yy / xx));
		//调整角度   
		if (xx < 0 && yy>0) alpha_angle = pi - alpha_angle;
		if (xx < 0 && yy < 0) alpha_angle = pi + alpha_angle;
		if (xx > 0 && yy < 0) alpha_angle = 2 * pi - alpha_angle;

		cv::Point p;  //pt2是箭头的尖角顶点，而p是箭头两边的点。
		//箭头是由p-pt2和pt2-p的两条线段（p赋了两次不同的值，前后不是一个点），以及pt1-pt2构成的。总共是三条线段。   
		p.x = (int)(ptStop.x + 6 * iPenWidth * cos(alpha_angle - pi / 4 * 3));
		p.y = (int)(ptStop.y + 6 * iPenWidth * sin(alpha_angle - pi / 4 * 3));
		cv::line(mat, p, ptStop, color, iPenWidth, CV_AA, 0);//p-pt2线段1   
		p.x = (int)(ptStop.x + 6 * iPenWidth * cos(alpha_angle + pi / 4 * 3));
		p.y = (int)(ptStop.y + 6 * iPenWidth * sin(alpha_angle + pi / 4 * 3));
		cv::line(mat, p, ptStop, color, iPenWidth, CV_AA, 0);//p-pt2线段2
	}

	/**
	* @brief      在目标框上绘制半透明填充区域
	* @param[in]  src：需要叠加信息的图片
	* @param[in]  rt：目标框
	* @param[in]  dRate：第二张图数组元素权重
	* @param[in]  color：绘笔颜色
	*/
	bool CvFrameOsd::drawFillRect(cv::Mat& src, cv::Rect rt, double dRate, cv::Scalar color)
	{
		if (rt.x < 0 || rt.y < 0)
			return false;
		//内部区域加透明色
		cv::Rect rtMask(rt.x + 1, rt.y + 1, rt.width - 2, rt.height - 2);
		if ((rtMask.x + rtMask.width) > src.cols)
			rtMask.width = src.cols - rtMask.x;
		if (rtMask.width <= 0)
			return false;
		if ((rtMask.y + rtMask.height) > src.rows)
			rtMask.height = src.rows - rtMask.y;
		if (rtMask.height <= 0)
			return false;
		//得到原图的要叠加透明色的区域
		cv::Mat imageROI;
		imageROI = src(rtMask);
		//生成一个新图，填充原始色
		cv::Size sz(rtMask.width, rtMask.height);
		cv::Mat mat = cv::Mat::zeros(sz, CV_8UC3);
		cv::rectangle(mat, cv::Rect(cv::Point(0, 0), sz), color, -1);
		//叠加透明度
		cv::addWeighted(imageROI, 1 - dRate, mat, dRate, 0., imageROI);
		mat.release();
		return true;
	}
}
