#include "gst_stable_queue.h"
#include <string>
#include "log.h"

using std::chrono::duration_cast;
using std::chrono::milliseconds;

/* Define our element type. Standard GObject/GStreamer boilerplate stuff */
#define gst_stablequeue_parent_class parent_class
G_DEFINE_TYPE(GstStableQueue, gst_stablequeue, GST_TYPE_BASE_TRANSFORM);
static void gst_stablequeue_set_property(GObject* object, guint prop_id, const GValue* value, GParamSpec* pspec);
static void gst_stablequeue_get_property(GObject* object, guint prop_id, GValue* value, GParamSpec* pspec);

static gboolean gst_stablequeue_start(GstBaseTransform * btrans);
static gboolean gst_stablequeue_stop(GstBaseTransform * btrans);
static GstFlowReturn gst_stablequeue_submit_input_buffer(GstBaseTransform* btrans, gboolean discont, GstBuffer* inbuf);
static GstFlowReturn gst_stablequeue_generate_output(GstBaseTransform* trans, GstBuffer** out_buf);
static void gst_stablequeue_finalize(GObject* object);
static gpointer gst_stablequeue_output_loop(gpointer data);


static void gst_stablequeue_class_init(GstStableQueueClass * klass)
{
	GObjectClass *gobject_class;
	GstElementClass *gstelement_class;
	GstBaseTransformClass *gstbasetransform_class;

	gobject_class = (GObjectClass *)klass;
	gstelement_class = (GstElementClass *)klass;
	gstbasetransform_class = (GstBaseTransformClass *)klass;

	/* Overide base class functions */
	gobject_class->set_property = GST_DEBUG_FUNCPTR(gst_stablequeue_set_property);
	gobject_class->get_property = GST_DEBUG_FUNCPTR(gst_stablequeue_get_property);
	gstbasetransform_class->start = GST_DEBUG_FUNCPTR(gst_stablequeue_start);
	gstbasetransform_class->stop = GST_DEBUG_FUNCPTR(gst_stablequeue_stop);
	gstbasetransform_class->submit_input_buffer = GST_DEBUG_FUNCPTR(gst_stablequeue_submit_input_buffer);
	gstbasetransform_class->generate_output = GST_DEBUG_FUNCPTR(gst_stablequeue_generate_output);

	gobject_class->finalize = gst_stablequeue_finalize;

	/* Install properties */
	g_object_class_install_property(gobject_class, PROP_MAX_QUEUE_CACHED_SIZE,
		g_param_spec_uint("max-cached-size", "Max queue cached size", "Max queue cache size", 0, G_MAXUINT, DEFAULT_MAX_QUEUE_CACHED_SIZE,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));
	g_object_class_install_property(gobject_class, PROP_SMOOTH_QUEUE_CACHED_SIZE,
		g_param_spec_uint("smooth-cached-size", "Smooth queue cached size", "Smooth queue cached size", 0, G_MAXUINT, DEFAULT_SMOOTH_QUEUE_CACHED_SIZE,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));
	g_object_class_install_property(gobject_class, PROP_MIN_SLEEP_MS_EVERY_FRAME,
		g_param_spec_uint("min-sleep-ms", "Min sleep ms every frame", "Min sleep ms every frame", 0, G_MAXUINT, DEFAULT_MIN_SLEEP_MS_EVERY_FRAME,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	/* Set sink and src pad capabilities */
	gst_element_class_add_pad_template(gstelement_class, gst_static_pad_template_get(&gst_stablequeue_src_template));
	gst_element_class_add_pad_template(gstelement_class, gst_static_pad_template_get(&gst_stablequeue_sink_template));
	gst_element_class_set_details_simple(gstelement_class, "IVA stable queue", "IVA stable queue", "IVA stable queue", "AI team @ wtoe@ https://wtoe.cn");
}

static void gst_stablequeue_init(GstStableQueue * self)
{
	GstBaseTransform *btrans = GST_BASE_TRANSFORM(self);
	gst_base_transform_set_in_place(GST_BASE_TRANSFORM(btrans), TRUE);
	gst_base_transform_set_passthrough(GST_BASE_TRANSFORM(btrans), TRUE);

	self->running = true;
	self->cached = new std::queue<GstBuffer*>();
	self->max_queue_cached_size = DEFAULT_MAX_QUEUE_CACHED_SIZE;
	self->smooth_queue_cached_size = DEFAULT_SMOOTH_QUEUE_CACHED_SIZE;
	self->min_sleep_ms_every_frame = DEFAULT_MIN_SLEEP_MS_EVERY_FRAME;
}

/* Function called when a property of the element is set. Standard boilerplate.
 */
static void gst_stablequeue_set_property(GObject* object, guint prop_id, const GValue* value, GParamSpec* pspec)
{
	auto self = GST_STABLEQUEUE(object);
	switch (prop_id) {
	case PROP_MAX_QUEUE_CACHED_SIZE:
		self->max_queue_cached_size = g_value_get_uint(value);
		break;
	case PROP_SMOOTH_QUEUE_CACHED_SIZE:
		self->smooth_queue_cached_size = g_value_get_uint(value);
		break;
	case PROP_MIN_SLEEP_MS_EVERY_FRAME:
		self->min_sleep_ms_every_frame = g_value_get_uint(value);
		break;
	default:
		G_OBJECT_WARN_INVALID_PROPERTY_ID(object, prop_id, pspec);
		break;
	}
}

/* Function called when a property of the element is requested. Standard
 * boilerplate.
 */
static void gst_stablequeue_get_property(GObject* object, guint prop_id, GValue* value, GParamSpec* pspec)
{
	auto self = GST_STABLEQUEUE(object);
	switch (prop_id) {
	case PROP_MAX_QUEUE_CACHED_SIZE:
		g_value_set_uint(value, self->max_queue_cached_size);
		break;
	case PROP_SMOOTH_QUEUE_CACHED_SIZE:
		g_value_set_uint(value, self->smooth_queue_cached_size);
		break;
	case PROP_MIN_SLEEP_MS_EVERY_FRAME:
		g_value_set_uint(value, self->min_sleep_ms_every_frame);
		break;
	default:
		G_OBJECT_WARN_INVALID_PROPERTY_ID(object, prop_id, pspec);
		break;
	}
}

static void gst_stablequeue_finalize(GObject* object)
{
	auto self = GST_STABLEQUEUE(object);
	G_OBJECT_CLASS(parent_class)->finalize(object);

	delete self->cached;
}

static gboolean gst_stablequeue_start(GstBaseTransform * btrans)
{
	GstStableQueue * self = GST_STABLEQUEUE(btrans);
	self->running = true;

	self->output_thread = g_thread_new("stablequeue-output", gst_stablequeue_output_loop, btrans);
	return TRUE;
}

static gboolean gst_stablequeue_stop(GstBaseTransform * btrans)
{
	GstStableQueue * self = GST_STABLEQUEUE(btrans);
	self->running = false;

	if (self->output_thread)
		g_thread_join(self->output_thread);

	while (!self->cached->empty())
	{
		auto buf = self->cached->front();
		self->cached->pop();
		gst_buffer_unref(buf);
	}

	return TRUE;
}

static GstFlowReturn gst_stablequeue_submit_input_buffer(GstBaseTransform* btrans, gboolean discont, GstBuffer* inbuf)
{
	auto self = GST_STABLEQUEUE(btrans);

	self->mutex_out.lock();
	if (self->cached->size() > self->max_queue_cached_size)
	{
		IVA_LOG_INFO("Stable queue size full ");
		while (self->cached->size() > self->smooth_queue_cached_size)
		{
			auto buf = self->cached->front();
			self->cached->pop();
			gst_buffer_unref(buf);
		}
	}

	self->cached->push(inbuf);
	gst_buffer_ref(inbuf);
	self->mutex_out.unlock();
	return GST_FLOW_OK;
}

static GstFlowReturn gst_stablequeue_generate_output(GstBaseTransform* trans, GstBuffer** out_buf)
{
	auto self = GST_STABLEQUEUE(trans);

	return GST_FLOW_OK;
}

static gpointer gst_stablequeue_output_loop(gpointer data)
{
	auto self = GST_STABLEQUEUE(data);
	while (self->running)
	{
		if (self->cached->empty())
		{
			std::this_thread::sleep_for(milliseconds(self->min_sleep_ms_every_frame));
			continue;
		}

		self->mutex_out.lock();
		GstBuffer* buf = self->cached->front();
		self->cached->pop();
		self->mutex_out.unlock();

		gst_buffer_unref(buf);

		gst_pad_push(GST_BASE_TRANSFORM_SRC_PAD(self), buf);

		if (self->cached->size() < self->smooth_queue_cached_size)
			std::this_thread::sleep_for(milliseconds(self->min_sleep_ms_every_frame));
	}
	return nullptr;
}

/**
 * Boiler plate for registering a plugin and an element.
 */
static gboolean stablequeue_plugin_init(GstPlugin * plugin)
{
	GST_DEBUG_CATEGORY_INIT(gst_stablequeue_debug, "stablequeue", 0,
		"stable queue");

	return gst_element_register(plugin, "stablequeue", GST_RANK_PRIMARY,
		GST_TYPE_STABLEQUEUE);
}
GST_PLUGIN_DEFINE(GST_VERSION_MAJOR, GST_VERSION_MINOR, stablequeue, DESCRIPTION, stablequeue_plugin_init, "3.3", LICENSE, BINARY_PACKAGE, URL)


