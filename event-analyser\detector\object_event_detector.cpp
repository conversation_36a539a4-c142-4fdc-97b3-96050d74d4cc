/**
 * Project AI事件分析模块
 */


#include "object_event_detector.h"
#include "util/scene_utility.h"
#include <iostream>

/**
* ObjectEventDetector implementation
*
* 物体事件检测器（基类）
*
* 按场景物体(图像模块检出)分类的事件类型检测器， 如抛洒物、烟火、路障等
*/
namespace evt
{
	ObjectEventDetector::ObjectEventDetector()
	{
		detectorType = DetectorType_Object;
	}
	/**
	 * @param target
	 */
	EventPtr ObjectEventDetector::process(EventObject& object) {

		if (holdEvents.empty())
		{
			return proposeNew(EventState::EventState_Confirming, object.rect);
		}
		else			 ///< 已有该事件类型，则不再判断新事件
		{
			for (auto& evt : holdEvents)
			{
			    if (evt->isIgnored() )
					continue;

				if (calculateIOU(object.rect, evt->getOccurRect()) > 0.001)
				{
					evt->setStateLife(0.0f);	 ///< IOU有重叠，重置 状态持续时间
				}
				if ( evt->getState() == EventState_Confirming)
					evt->insertExtraRect( object.rect);
			}
		}
		return NULL;
	}

	/**
	 * 持有事件更新
	 */
	void ObjectEventDetector::onUpdateEvent()
	{

		float delta = 1.0f / (float)frameRate;

		for (auto& evt : holdEvents)
		{
			evt->addStateLife(delta);

			if (evt->getStateLife() > removeTime)
			{
				evt->setState(EventState::EventState_Removed);
			}
		}
	}

	/**
	 * 区域配置更新
	 */
	void ObjectEventDetector::onUpdateRegionConfig()
	{
		switch (eventType)
		{
		case EventType_Obstacle:
			regionConfig.getValue<float>(removeTime, OBSTACLE_REMOVE_TIME);
			break;
		case EventType_RoadBlock:
			regionConfig.getValue<float>(removeTime, ROADBLOCK_REMOVE_TIME);
			break;
		case EventType_FireSmoke:
			regionConfig.getValue<float>(removeTime, FIRESMOKE_REMOVE_TIME);
			break;
       case EventType_Landslide:
           regionConfig.getValue<float>(removeTime, LANDSLIDE_REMOVE_TIME);
           break;
		default:
			break;
		}
	}
}

