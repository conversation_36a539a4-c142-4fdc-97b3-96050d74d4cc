#pragma once

#include <opencv2/freetype.hpp>
#include "opencv2/imgproc/imgproc.hpp"
#include "opencv2/imgproc/imgproc_c.h"

namespace ai
{
	/**
	 * @brief  基于opencv的目标框、信息绘制类
	 */
	class CvFrameOsd
	{
	public:
		CvFrameOsd();
		~CvFrameOsd();

		bool init(std::string& fontFile);

		void deinit();

		void drawTargets(cv::Mat& mat, cv::Rect rect, const std::string& eventName);

		void drawTargetRect(cv::Mat& mat, cv::Rect nodeRect, cv::Scalar color, uint32_t iPenWidth, double dRate);

		void drawArrow(cv::Mat& mat, cv::Point ptStart, cv::Point ptStop, uint32_t iPenWidth, cv::Scalar color = cv::Scalar(0, 0, 255));

		bool drawFillRect(cv::Mat& src, cv::Rect rt, double dRate, cv::Scalar color);

	private:
		cv::Ptr<cv::freetype::FreeType2> freetype;   ///< 字体加载模块 

	};
}


