#pragma once
#include <gst-nvdssr.h>
#include <chrono>
#include <deque>
#include <shared_mutex>
#include <atomic>
#include <optional>
#include "event_analyser.h"
#include "ivautils.h"
#include "protocol_utility.h"
#include "opencv2/core.hpp"

namespace record
{
    constexpr int RECORD_CACHE_SIZE_SEC = 15;                   //!< 录像预缓存大小，单位 秒
    constexpr int RECORD_DEFAULT_DURATION = 10;                 //!< 录像时长

    class VideoRecordBase
    {
    public:
        explicit VideoRecordBase(guint ch, guint before, guint duration) : channel(ch), beforeTime(before), duration(duration){};
        virtual ~VideoRecordBase() {destroy();};
        /**
         * 创建录像context, 设置录像参数
         * @param params
         */
        void create(NvDsSRInitParams params, const std::string& relativePath);

        /**
         * 销毁录像context
         */
        void destroy();

        /**
         * 开启录像
         * @param[in]  before     相对于当前时刻之前的时间
         * @param[in]  duration   录制持续时长
         * @param[in]  videoPath       过线录像任务，如果只是单纯录像，该参数可忽略
         * @return     成功开启录像任务，则返回true, 否则 false
         */
        bool start(guint before, guint duration, gpointer userData);

        /**
         * 停止录像
         */
        bool stop();

        /**
         * 返回录像是否完成，即是否空闲，可开启新录像
         * @note 录像接口不支持并发，必须等到录像完成且调用完成回调后，才能开启新录像
         */
        bool isCompleted();

        /**
         * 设置录像完成标志位，并调用用户的录像完成回调
         */
        virtual void setCompleted(guint realDuration);

        /**
         * 录像完成回调，写日志，调用用户注册的回调函数
         */
        static gpointer onComplete(NvDsSRRecordingInfo *info, gpointer userData);


    public:
        inline guint getBeforeTime(){ return beforeTime; };
        inline  void setBeforeTime(guint time){  beforeTime = time; };
        inline  void setDuration(guint time){  duration = time; };
        inline std::string getFileName(){ return fileName; };

        inline GstElement *getRecordBin(){ return (recordContext != nullptr) ? recordContext->recordbin : nullptr; };
    protected:
        guint channel = 0;                                  //!< 通道号
        guint beforeTime = 2;                               //!< 相对于当前时刻之前的时间
        guint afterTime = 3;                                //!< 当前目标时刻后继续录制时间
        guint duration = 10;                                //!< 录制持续时长 （录像文件的总时长 = beforeTime + duration）
        std::string fileName;                               //!< 视频录制的文件名，包括路径


        std::mutex recordLock;                              //!< 录像锁
        std::atomic_bool completed = true;                  //!< 录像完成回调

        std::string videoRelativePath;

        NvDsSRContext *recordContext = nullptr;             //!< 视频录制Context
        NvDsSRSessionId sessId = UINT32_MAX;
        guint keyFrameIndex = 0;                            //!< 关键帧在before time中的位置

    };
}



