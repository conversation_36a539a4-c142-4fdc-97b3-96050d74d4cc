﻿cmake_minimum_required (VERSION 3.5.2)
## 目标生成
set(TARGET_LIBRARY "iva_protocol")
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/out/lib)
set(LIB_IVA ${PROJECT_SOURCE_DIR}/out/lib)

add_compile_options(-g -std=c++17 -fPIC -fstack-protector-all -Wno-unknown-pragmas -Wno-unused-variable -Werror=return-type -Wall )

# boost
set(BOOST_HOME "/opt/boost")
set(BOOST_LIB
        boost_context
        boost_filesystem
        boost_thread
        boost_serialization
        )


# 头文件
include_directories(
        ${BOOST_HOME}/include/
        ${PROJECT_SOURCE_DIR}/iva-log
        ${PROJECT_SOURCE_DIR}/event-analyser/include
        ${PROJECT_SOURCE_DIR}/network
        ${PROJECT_SOURCE_DIR}/network/include
        ${PROJECT_SOURCE_DIR}/iva-app/includes/
        ${PROJECT_SOURCE_DIR}/common/
        includes
)


# 库路径
link_directories(
    ${BOOST_HOME}/lib/
    ${LIB_IVA}
)


# 当前文件夹cpp
FILE(GLOB src "*.cpp")
SET(ALL_SRC ${include} ${src})

# 生成动态库
ADD_LIBRARY(${TARGET_LIBRARY} SHARED ${ALL_SRC})
target_link_libraries(${TARGET_LIBRARY} pthread stdc++fs ${BOOST_LIB} event_analyser ai_network ivacommon ivalog)

