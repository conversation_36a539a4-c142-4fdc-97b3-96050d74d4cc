/**
 * Project AI事件分析模块
 */

#include "scene_state.h"
#include "util/scene_utility.h"
#include "log.h"

 /**
   * SceneState implementation
   *
   * 通道场景状态
   */
namespace evt
{
	SceneState::SceneState() : currentIndex(0)
	{
	}

	/**
	 * 初始化ROI相关信息
	 */
	void SceneState::init(std::vector<ROI*>& roiList)
	{
		currentIndex = 0;
		roiFlows.clear();
		roiInJam.clear();
		for (auto roi : roiList)
		{
			auto& trafficFlow = roiFlows[roi->getID()];
			trafficFlow.cfgDirection = roi->getDirection();
		}
	}

	/**
	 * 当前场景是否疑似偏移
	 */
	bool SceneState::isShifted()
	{
		bool shifted = false;
		for (auto& f : roiFlows)
		{
			auto roiId = f.first;
			auto& trafficFlow = f.second;
			if (trafficFlow.passRoi)//如果一个roi判定穿越，则判定场景偏移，优先
			{
				return true;
			}
			//一个roi交通流偏移就判定偏移，排除另一个roi横跨车道或者无交通流
			shifted = shifted || trafficFlow.shifted;

			if (shifted)
			{
				break;
			}
		}
		return shifted;
	}

	/**
	 * 当前场景是否刚发生偏移
	 */
	bool SceneState::isShifting()
	{
		bool shifted = false;
		bool curIndexShifted = false;
		for (auto& f : roiFlows)
		{
			auto roiId = f.first;
			auto& trafficFlow = f.second;
			if (trafficFlow.passRoi && trafficFlow.shiftedIndex == currentIndex)//优先根据roi穿越
			{
				return true;
			}
			if (trafficFlow.shifted)
			{
				shifted = trafficFlow.shifted;
				curIndexShifted = trafficFlow.shiftedIndex == currentIndex;
			}
			if (shifted && curIndexShifted)
			{
				break;
			}
		}
		return shifted && curIndexShifted;
	}

	/**
	 * 当前场景ROI是否拥堵
	 */
	bool SceneState::inJam(int roiID)
	{
		return roiInJam[roiID];
	}

	/**
	 * 更新场景状态开始
	 */
	void SceneState::onUpdateStart()
	{
		currentIndex++;
		if (currentIndex % 25 == 0)
		{
			for (auto& f : roiFlows)
			{
				auto& trafficFlow = f.second;
				uint curIndex = currentIndex;

				//更新交通流大小
				auto& flowSum = trafficFlow.flowSum;
				auto iter = flowSum.begin();
				for (; iter != flowSum.end();)
				{
					if (currentIndex - iter->second > TRAFFIC_FLOW_CHECK_PERIOD)
					{
						iter = flowSum.erase(iter);
					}
					else
					{
						iter++;
					}
				}
				//更新flow缓存目标存在时长
				uint frameDiff = TARGET_FLOW_FRAME_DIFF;
				if (flowSum.size() < 5)
				{
					frameDiff = TARGET_FLOW_FRAME_DIFF * 6;
				}
				else if (flowSum.size() < 6)
				{
					frameDiff = TARGET_FLOW_FRAME_DIFF * 4;
				}
				else if (flowSum.size() < 9)
				{
					frameDiff = TARGET_FLOW_FRAME_DIFF * 3;
				}
				else if (flowSum.size() < 18)
				{
					frameDiff = TARGET_FLOW_FRAME_DIFF * 2;
				}

				//更新flow目标缓存
				auto& flowListFast = trafficFlow.flowListFast;
				auto& flowListSlow = trafficFlow.flowListSlow;
				//IVA_LOG_WARN("roi:{},flow size:{},fast size:{},slow size:{}, frameDiff : {}",
				//	f.first, flowSum.size(), flowListFast.size(), flowListSlow.size(), frameDiff);

				flowListFast.remove_if([curIndex, frameDiff](const TargetFlowInfo& info) {
					return curIndex - info.index > frameDiff;
				});
				flowListSlow.remove_if([curIndex, frameDiff](const TargetFlowInfo& info) {
					return curIndex - info.index > frameDiff;
				});
				if (flowListFast.size() < trafficFlow.trafficFlowMinCount)
				{
					trafficFlow.fastActive = false;
				}
				if (flowListSlow.size() < trafficFlow.trafficFlowMinCount)
				{
					trafficFlow.slowActive = false;
				}
			}
		}
	}

	/**
	 * 更新场景状态
	 * @param target
	 * @param roi
	 */
	void SceneState::onUpdate(TargetPtr target, ROI* roi)
	{
		if (!target->isVehicleType())
			return;

		auto& trafficFlow = roiFlows[roi->getID()];

		auto& flowSum = trafficFlow.flowSum;
		auto& flowListFast = trafficFlow.flowListFast;
		auto& flowListSlow = trafficFlow.flowListSlow;
		//if (!trafficFlow.passRoi)
		//{
		//	if (target->IsPassedROI())
		//	{
		//		trafficFlow.passCount++;
		//		if (trafficFlow.passCount>=5)
		//		{
		//			trafficFlow.passRoi = true;
		//			trafficFlow.shiftedIndex = currentIndex;
		//			IVA_LOG_WARN("target :{}, pass ROI:{} !", target->getID(), roi->getID());
		//		}
		//	}
		//}

		auto targetVelocity = target->getVelocity();

		if (target->getTrackCount() > 10)
		{
			float length = targetVelocity.length();
			auto targetId = target->getID();
			float angle = targetVelocity.angle(trafficFlow.cfgDirection);

			flowSum.emplace(targetId, currentIndex);

			if (length > TRAFFIC_FLOW_NORMAL_MIN_VELOCITY)
			{
				auto findIt = std::find_if(flowListFast.begin(), flowListFast.end(), [targetId](const TargetFlowInfo& info) {
					return info.targetId == targetId;
				});
				if (findIt == flowListFast.end())
				{//防止同一目标多次贡献
					flowListFast.emplace_back(targetId, targetVelocity, currentIndex, angle);
				}
				else
				{//更新flow中目标最新状态
					findIt->targetId = targetId;
					findIt->direction = targetVelocity;
					findIt->index = currentIndex;
					findIt->angle = angle;
				}

				//IVA_LOG_WARN("id:{},roi id:{},type:{},speed:{},target angle:{},unitCount:{},fast size:{}\n", target->info.id,
				//	target->info.roiID, target->getType(), targetVelocity.length(), target->getVelocity().angle(trafficFlow.cfgDirection),
				//	trafficFlow.unitCount.size(), flowListFast.size());
				target->sceneflowChecked = true;

				if (targetVelocity.angle(trafficFlow.cfgDirection) > TRAFFIC_FLOW_ANGLE_SHIFTED)
				{
					trafficFlow.unitCount.insert(targetId);
				}
				if (flowListFast.size() > trafficFlow.trafficFlowMinCount)
				{
					trafficFlow.fastActive = true;
					auto info = flowListFast.front();
					trafficFlow.unitCount.erase(info.targetId);
					flowListFast.pop_front();
				}
			}
			else if (length > TRAFFIC_FLOW_SLOW_MIN_VELOCITY
				&& length < TRAFFIC_FLOW_NORMAL_MIN_VELOCITY)
			{
				auto findIt = std::find_if(flowListSlow.begin(), flowListSlow.end(), [targetId](const TargetFlowInfo& info) {
					return info.targetId == targetId;
				});
				if (findIt == flowListSlow.end())
				{//防止同一目标多次贡献
					flowListSlow.emplace_back(targetId, targetVelocity, currentIndex, angle);
				}
				else
				{//更新flow中目标最新状态
					findIt->targetId = targetId;
					findIt->direction = targetVelocity;
					findIt->index = currentIndex;
					findIt->angle = angle;
				}
				target->sceneflowChecked = true;
				if (flowListSlow.size() > trafficFlow.trafficFlowMinCount)
				{
					trafficFlow.slowActive = true;
					flowListSlow.pop_front();
				}
			}
		}
	}

	/**
	 * 更新场景拥堵情况
	 * @param evt
	 * @param roi
	 */
	void SceneState::onUpdateJam(EventPtr evt)
	{
		int roiID = evt->getROIID();
		if (evt->getState() == EventState_Confirming || evt->getState() == EventState_Maintaining)
			roiInJam[roiID] = true;
		else
			roiInJam[roiID] = false;
	}

	/**
	 * 更新场景状态结束
	 */
	void SceneState::onUpdateEnd()
	{
		for (auto& f : roiFlows)
		{
			int roiID = f.first;
			auto& trafficFlow = f.second;

			if (trafficFlow.fastActive)
			{
				// 更新 ROI 交通流方向,所有速度矢量和
				//auto& flowList = trafficFlow.flowListGreater;
				//Vector2D direction;
				//for (auto& flow : flowList)
				//{
				//	direction += flow.direction;
				//}

				//取所有速度偏角中值，过滤异常点
				auto& flowList = trafficFlow.flowListFast;
				std::vector<TargetFlowInfo> tempVec(flowList.begin(), flowList.end());
				std::sort(tempVec.begin(), tempVec.end(), [](const TargetFlowInfo& infoFirst, const TargetFlowInfo& infoSecond) {
					return infoFirst.angle < infoSecond.angle;
				});
				Vector2D direction = tempVec.at(tempVec.size() / 2).direction;

				direction.normalize();
				trafficFlow.direction = direction;

				// 判断 场景偏移状态
				if (trafficFlow.shiftedIndex != 0)//过滤初始情况
				{
					uint passedIndex = currentIndex - trafficFlow.shiftedIndex;
					if (passedIndex < TRAFFIC_FLOW_SHIFTED_PERIOD)
						continue;
				}

				float flowDiffAngle = trafficFlow.direction.angle(trafficFlow.cfgDirection);
				//IVA_LOG_WARN("roi:{},trafficFlow diff angle:{},unitCount:{}\n", roiID, flowDiffAngle, trafficFlow.unitCount.size());
				if (flowDiffAngle > TRAFFIC_FLOW_ANGLE_SHIFTED)
				{
					if (trafficFlow.unitCount.size() > TRAFFIC_FLOW_MIN_UNIT_COUNT)
					{
						if (trafficFlow.shifted == false) // 偏移开始
						{
							trafficFlow.shiftedIndex = currentIndex;
							IVA_LOG_WARN("[Scene state] ROI ID :{} shifted !", roiID);
						}
						trafficFlow.resetCount = 0;
						trafficFlow.shifted = true;
					}
				}
				else if (flowDiffAngle < TRAFFIC_FLOW_ANGLE_TOLERANCE)
				{
					if (trafficFlow.shifted == true) // 偏移恢复
					{
						trafficFlow.resetCount++;
						if (trafficFlow.resetCount > TRAFFIC_FLOW_RESET_COUNT)
						{
							trafficFlow.shifted = false;
							trafficFlow.passRoi = false;
							trafficFlow.passCount = 0;
							IVA_LOG_WARN("[Scene state] ROI ID :{} reseted !", roiID);
						}
					}
				}
			}
			else if (trafficFlow.slowActive)
			{
				//TODO
			}
		}

		// TODO
		// 1. 车流非常少， 短时间无法贡献实时的累计车流方向的情况
		// 2. 多个ROI的联动投票确认
		// 3. 瞬间相机旋转，而且短期车流无法贡献计算值
	}
}
