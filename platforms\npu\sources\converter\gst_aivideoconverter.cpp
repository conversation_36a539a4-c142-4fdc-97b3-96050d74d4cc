#include "gst_aivideoconverter.h"
#include "gst_aivideoconverter_prop.ixx"
#include "gst_aivideoconverter_init.ixx"
#include "gst_deviceframe_allocator.h"

#include <string>
#include <iostream>

static gboolean gst_plugin_init(GstBaseTransform* trans)
{
	auto self = PLUGIN_CAST(trans);
	self->allocator = nullptr;
	self->upstream_use_mempool = 1;

	return TRUE;
}

static gboolean gst_plugin_start(GstBaseTransform* trans)
{
	auto self = PLUGIN_CAST(trans);

	GST_DEBUG_OBJECT(self, "gst plugin start \n");
	return TRUE;
}

static gboolean gst_plugin_stop(GstBaseTransform* trans)
{
	auto self = PLUGIN_CAST(trans);
	if (self->allocator)
	{
		gst_object_unref(self->allocator);
		self->allocator = NULL;
	}
	//if(self->transform)
	//{
	//	delete self->transform;
	//	self->transform = nullptr;
	//}
	//if(self->transParam)
	//{
	//	if(self->transParam->src_rect)
	//	{
	//		delete self->transParam->src_rect;
	//		self->transParam->src_rect = nullptr;
	//	}
	//	if(self->transParam->dst_rect)
	//	{
	//		delete self->transParam->dst_rect;
	//		self->transParam->dst_rect = nullptr;
	//	}
	//	
	//	delete self->transParam;
	//	self->transParam = nullptr;
	//}

	GST_DEBUG_OBJECT(self, "gst plugin stop \n");
	return TRUE;
}


static GstCaps* gst_plugin_transform_caps(GstBaseTransform* trans, GstPadDirection direction, GstCaps* caps, GstCaps* filter)
{
	GstCaps* other_caps;
	if (direction == GST_PAD_SINK)
	{
		other_caps = gst_caps_copy(gst_pad_get_pad_template_caps(trans->srcpad));
	}
	else
	{
		other_caps = gst_caps_copy(gst_pad_get_pad_template_caps(trans->sinkpad));
	}

	if (filter)
	{
		GstCaps* tmp = gst_caps_intersect_full(filter, other_caps, GST_CAPS_INTERSECT_FIRST);
		gst_caps_replace(&other_caps, tmp);
		gst_caps_unref(tmp);
	}

	GST_LOG_OBJECT(trans, "returning caps: %" GST_PTR_FORMAT, other_caps);
	return other_caps;
}

static GstCaps* gst_plugin_fixate_caps(GstBaseTransform* trans, GstPadDirection direction, GstCaps* caps, GstCaps* othercaps)
{
	auto self = PLUGIN_CAST(trans);
	GstStructure* s;
	GstStructure* t;
	const GValue* framerate, * width, * height;
	GstCaps* intersection, * temp;

	temp = gst_pad_get_pad_template_caps(trans->srcpad);
	intersection = gst_caps_intersect_full(temp, othercaps, GST_CAPS_INTERSECT_FIRST);

	s = gst_caps_get_structure(caps, 0);
	int sHeight = 0, sWidth = 0;
	gst_structure_get_int(s, "height", &sHeight);
	gst_structure_get_int(s, "width", &sWidth);
	const gchar* in_format = gst_structure_get_string(s, "format");

	GstStructure* sintersection = gst_caps_get_structure(intersection, 0);
	const GValue* widthVal = gst_structure_get_value(sintersection, "width");
	intersection = gst_caps_make_writable(intersection);
	if (GST_VALUE_HOLDS_INT_RANGE(widthVal)) {
		gst_structure_set(sintersection, "width", G_TYPE_INT, sWidth, NULL);
	}
	const GValue* heightVal = gst_structure_get_value(sintersection, "height");
	if (GST_VALUE_HOLDS_INT_RANGE(heightVal)) {
		gst_structure_set(sintersection, "height", G_TYPE_INT, sHeight, NULL);
	}

	gint num = 25, den = 1;
	gint incapframerate_num = 0, incapframerate_den = 1;
	bool gettmpcapsFraction = gst_structure_get_fraction(sintersection, "framerate", &num, &den);
	bool getIncapsFraction = gst_structure_get_fraction(s, "framerate", &incapframerate_num, &incapframerate_den);
	if (getIncapsFraction && !gettmpcapsFraction)
	{
		gst_caps_set_simple(intersection, "framerate", GST_TYPE_FRACTION, incapframerate_num, incapframerate_den, NULL);
	}

	if (!gst_caps_is_fixed(intersection) && gst_caps_is_fixed(caps))
	{
		const GValue* val = gst_structure_get_value(sintersection, "format");
		if ((val && GST_VALUE_HOLDS_LIST(val)) || (gst_caps_get_size(intersection) > 1)) {
			gst_caps_set_simple(intersection, "format", G_TYPE_STRING, in_format, NULL);
		}
	}

	gst_caps_unref(othercaps);
	othercaps = intersection;
	othercaps = GST_BASE_TRANSFORM_CLASS(parent_class)->fixate_caps(trans, direction, caps, othercaps);
	
	GST_LOG_OBJECT(trans, "Fixated caps %" GST_PTR_FORMAT " --> %" GST_PTR_FORMAT, caps, othercaps);

	return othercaps;
}

static gboolean gst_plugin_set_caps(GstBaseTransform* trans, GstCaps* incaps, GstCaps* outcaps)
{
	auto self = PLUGIN_CAST(trans);

	/* input caps */
	if (!gst_video_info_from_caps(&self->in_info, incaps)) {
		GST_ERROR_OBJECT(self, "invalid incaps %" GST_PTR_FORMAT, incaps);
		return FALSE;
	}

	/* output caps */
	if (!gst_video_info_from_caps(&self->out_info, outcaps)) {
		GST_ERROR_OBJECT(self, "invalid outcaps %" GST_PTR_FORMAT, incaps);
		return FALSE;
	}

	auto ins = gst_caps_get_structure(incaps, 0);
	auto outs = gst_caps_get_structure(outcaps, 0);

	auto in_format = gst_structure_get_string(ins, "format");
	auto out_format = gst_structure_get_string(outs, "format");
	self->in_fmt = gst_video_format_from_string(in_format);
	self->out_fmt = gst_video_format_from_string(out_format);

	auto in_feature = gst_caps_get_features(incaps, 0);
	auto out_feature = gst_caps_get_features(outcaps, 0);

	if (gst_caps_features_contains(in_feature, GST_CAPS_FEATURE_DEVICE_MEMORY))
	{
		self->inbuf_from_device = true;
	}
	if (gst_caps_features_contains(out_feature, GST_CAPS_FEATURE_DEVICE_MEMORY))
	{
		self->outbuf_to_device = true;
	}

    GST_LOG_OBJECT(trans, "set_caps incaps %" GST_PTR_FORMAT ", outcaps %" GST_PTR_FORMAT, incaps, outcaps);
    if (self->transParam) {
        if ( self->transParam->src_rect->top + self->transParam->src_rect->height > self->in_info.height ||
            self->transParam->src_rect->left + self->transParam->src_rect->width > self->in_info.width) {
                g_print("reset transparam\n");
                self->transParam->src_rect->top = 0;
                self->transParam->src_rect->left = 0;
                self->transParam->src_rect->width = self->in_info.width;
                self->transParam->src_rect->height = self->in_info.height;
        }
    }

    devSetDevId(self->npuID);
    return true;
}

static GSList* gst_buffer_get_detection_meta(GstBuffer* buffer)
{
	g_return_val_if_fail(buffer != NULL, NULL);

	gpointer state = NULL;
	GstMeta* meta = NULL;
	GSList* meta_list = NULL;
	while ((meta = gst_buffer_iterate_meta(buffer, &state))) {
		meta_list = g_slist_prepend(meta_list, meta);
	}
	return meta_list;
}

static bool gst_buffer_add_video_meta(GstBaseTransform* trans, GstBuffer* buf, ai::DeviceFrame* frame)
{
	auto self = PLUGIN_CAST(trans);

	self->out_info.offset[0] = 0;
	self->out_info.offset[1] = frame->frame_size;
	gst_buffer_add_video_meta_full(buf, GST_VIDEO_FRAME_FLAG_NONE, self->out_fmt,
		 frame->width, frame->height, frame->n_planes,
		 self->out_info.offset, (gint*)frame->strides);


	return true;
}

BufSurfaceColorFormat convert_surface_format(uint32_t format)
{
	if(format == GST_VIDEO_FORMAT_RGB)
		return BUF_COLOR_FORMAT_RGB;
	if(format == GST_VIDEO_FORMAT_RGBA)
		return BUF_COLOR_FORMAT_RGBA;
	return BUF_COLOR_FORMAT_NV12;
}

static bool need_trans(GstBaseTransform* trans, ai::DeviceFrame* frame)
{
	auto self = PLUGIN_CAST(trans);

	// 不需要做转换 format size crop
	if (convert_surface_format(self->out_info.finfo->format) == getSurfaceFmt(frame->pformat)
		&& frame->width == (uint32_t)self->out_info.width && frame->height == (uint32_t)self->out_info.height
		&& self->src_rect.width == 0 && self->src_rect.height == 0) {
		return false;
	}

	return true;
}

static bool trans_frame(GstBaseTransform* trans, ai::DeviceFrame* frame, ai::DeviceFrame* dst)
{
	auto self = PLUGIN_CAST(trans);
	if(!self->transform)
	{
		self->transform = std::make_shared<BufSurfaceTransform>();
		self->transParam = std::shared_ptr<BufSurfTransformParams>(new BufSurfTransformParams(),
																	[](BufSurfTransformParams* transParam) {
																		if(transParam->src_rect)
																			delete transParam->src_rect;
																		if(transParam->dst_rect)
																			delete transParam->dst_rect;
																		delete transParam;});

		self->transParam = std::make_shared<BufSurfTransformParams>();
		self->transParam->src_rect = new BufSurfTransformRect();
		self->transParam->src_rect->top = self->src_rect.top;
		self->transParam->src_rect->left = self->src_rect.left;
		self->transParam->src_rect->width = self->src_rect.width > 0 ? self->src_rect.width : frame->width - self->src_rect.top;
		self->transParam->src_rect->height = self->src_rect.height > 0 ? self->src_rect.height : frame->height - self->src_rect.left;
		self->transParam->dst_rect = new BufSurfTransformRect();
		self->transParam->dst_rect->top = self->dst_rect.top;
		self->transParam->dst_rect->left = self->dst_rect.left;
		self->transParam->dst_rect->width = self->dst_rect.width > 0 ? self->dst_rect.width : self->out_info.width - self->dst_rect.top;
		self->transParam->dst_rect->height = self->dst_rect.height > 0 ? self->dst_rect.height : self->out_info.height - self->dst_rect.left;
		self->transParam->gpu_id = frame->device_id;

		self->transform->init(1, *self->transParam);
	}

	if (!need_trans(trans, frame))
	{
		memcpy(dst, frame, sizeof(ai::DeviceFrame));
		/*dst->handle = self;
		if(self->outbuf_to_device)
		{
			frame->handle = self;
		}*/
		if (self->outbuf_to_device)
		{
			frame->handle = self;
			dst->handle = self;
		}
		else {
			frame->handle = self;
			if (self->upstream_use_mempool == 0) {
				dst->handle = nullptr;
			}
		}
		return true;
	}

	BufSurface* bufsurface = transDevFrameToBufsurfaces(frame);
			
	BufSurfaceCreateParams createParams;
	createParams.width = self->out_info.width;
	createParams.height = self->out_info.height;
	createParams.colorFormat = convert_surface_format(self->out_info.finfo->format);
	createParams.memType = BUF_MEM_DEVICE;
	createParams.gpuId = frame->device_id;
	BufSurface* dstbufsurface;
	BufSurfaceCreate(&dstbufsurface, 1, &createParams);
	
	self->transform->transform(*bufsurface, *dstbufsurface);

	transBufsurfacesToDevFrame(dstbufsurface, dst);

    if (self->outbuf_to_device) {
        frame->handle = nullptr;
        dst->handle = self;
    }
    else {
        frame->handle = nullptr;
        if (self->upstream_use_mempool == 0) {
            dst->handle = nullptr;
        }
    }

	BufSurfaceDestroy(bufsurface, true);
	BufSurfaceDestroy(dstbufsurface, true);
	return true;
}

static GstFlowReturn gst_plugin_transform(GstBaseTransform* trans, GstBuffer* inbuf, GstBuffer* outbuf)
{
	auto self = PLUGIN_CAST(trans);

	GstFlowReturn flow_ret = GST_FLOW_ERROR;
	BufSurface* buf_surface = nullptr;
	ai::DeviceFrame* frame = nullptr;

	if (!self->allocator)
		self->allocator = gst_deviceframe_allocator_new();

	GstMapInfo in_map_info;
	memset(&in_map_info, 0, sizeof(in_map_info));
	if (!gst_buffer_map(inbuf, &in_map_info, GST_MAP_READ))
	{
		g_print("Error: Failed to map gst buffer \n");
		goto error;
	}

	// TODO 后续统一定义引入 mem_type
	if (!strcmp(in_map_info.memory->allocator->mem_type, "BufSurfaceMemory"))
	{
		buf_surface = (BufSurface*)in_map_info.data;
		auto& surface = buf_surface->surfaceList[0]; 
		frame = new ai::DeviceFrame();
		frame->handle = buf_surface;
		frame->buf_id = surface.buf_id;
		frame->pts = surface.pts;
		frame->frame_num = surface.frame_num;
		frame->device_id = buf_surface->gpuId;
		frame->frame_size = surface.frame_size;
		frame->width = surface.width;
		frame->height = surface.height;
		frame->n_planes = surface.n_planes;
		//frame->pformat = surface.colorFormat == BUF_COLOR_FORMAT_RGB ? ai::PixelFmt::RGB24 : ai::PixelFmt::RGBA;
		frame->pformat = self->in_fmt == GST_VIDEO_FORMAT_RGB ? ai::PixelFmt::RGB24 : ai::PixelFmt::RGBA;
		memcpy(frame->strides, surface.strides, sizeof(surface.strides));
		memcpy(frame->ptrs, surface.ptrs, sizeof(surface.ptrs));
	}
	else
	{
		frame = (ai::DeviceFrame*)in_map_info.data;
	}

	GstMapInfo out_map_info;
	if (!gst_buffer_map(outbuf, &out_map_info, (GstMapFlags)GST_MAP_WRITE)) {
		g_print("Error: Failed to map gst out buffer\n");
		goto error;
	}

	if (self->outbuf_to_device)
	{
		ai::DeviceFrame dst;
		dst.device_id = frame->device_id;
		trans_frame(trans, frame, &dst);

		auto mem = gst_deviceframe_allocator_alloc(self->allocator, &dst);
		//dst.handle = self; // 不自动释放
		gst_buffer_replace_all_memory(outbuf, mem);
		gst_buffer_add_video_meta(trans, outbuf, &dst);
	}
	else
	{
		if (self->out_info.finfo->format == GST_VIDEO_FORMAT_NV12)
		{
			auto mem = gst_deviceframe_allocator_alloc_host(self->allocator, frame);
			gst_buffer_replace_all_memory(outbuf, mem);

			// 改进handle设置逻辑，确保内存管理的一致性
			if (buf_surface == nullptr) {
				// 对于非BufSurface类型的输入（如网络流解码输出）
				if (self->upstream_use_mempool == 0) {
					// 上游不使用内存池，设置handle为nullptr让析构函数释放内存
					frame->handle = nullptr;
				}
				else {
					// 上游使用内存池，保持handle指向内存池
					// 注意：这里不修改handle，保持原有的内存池管理
					GST_DEBUG_OBJECT(self, "Upstream uses mempool, keeping handle for pool management");
				}
			}
			else {
				// 对于BufSurface类型的输入，handle已经指向BufSurface，保持不变
				GST_DEBUG_OBJECT(self, "BufSurface input, keeping original handle");
			}
		}
		else if(self->out_info.finfo->format == GST_VIDEO_FORMAT_RGB || self->out_info.finfo->format == GST_VIDEO_FORMAT_RGBA)
		{
			ai::DeviceFrame dst;
			dst.device_id = self->npuID;
			trans_frame(trans, frame, &dst);
			
			auto mem = gst_deviceframe_allocator_alloc_host(self->allocator, &dst);
			gst_buffer_replace_all_memory(outbuf, mem);
			gst_buffer_add_video_meta(trans, outbuf, &dst);
		}
		else
		{
			g_print("not support format\n");
		}
	}

	gst_buffer_unmap(outbuf, &out_map_info);

	flow_ret = GST_FLOW_OK;
error:
	if (buf_surface)
	{
		delete frame;
	}

	gst_buffer_unmap(inbuf, &in_map_info);
	return flow_ret;
}
