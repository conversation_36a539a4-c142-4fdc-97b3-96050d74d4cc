#pragma once
#include "ini/inibase.h"

/*
 * 特征模型 检测配置 (INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(ia::feature_model, "/data/opt/models/feature/feature_model.ini",

(string,   featureModelPath,		  Model,     "feature/veri_0524_no_form.trt",       u8"特征模型相对路径") // TODO 模型名称 待规范
(string,   plateLocateModelPath,	  Model,     "",									u8"车牌模型1相对路径")
(string,   plateRecognizeModelPath,	  Model,     "",					                u8"车牌模型2相对路径")
(string,   plateLabelsPath,	          Model,     "feature/labels.txt",					u8"车牌号、颜色标签路径")

(double,   plateLocateScoreThresh,     Model,     0.3,									u8"车牌定位目标框阈值")
(double,   plateLocateIouThresh,       Model,     0.1,									u8"车牌定位IOU阈值")
(double,   plateRecognizeScoreThresh,  Model,     0.5,									u8"车牌识别目标框阈值")
(double,   plateRecognizeIouThresh,	   Model,     0.25,									u8"车牌识别IOU阈值")

)
#define FEATURE_MODEL_CFG ia::feature_model::Config::instance()
