#pragma once
#include "base_video_record.h"

namespace record
{
    /**
     * 过线目标信息
     */
    struct PassedTargetInfo
    {
        std::chrono::system_clock::time_point startTime;   //!< 目标过线时间
        evt::TargetInfoList targets;                       //!< 过线目标
        cv::Mat frame;                                     //!< 过线目标所在的帧
    };

    using VehicleRecordCB = std::function<void(std::string, int, const PassedTargetInfo& targetInfo)>;

    /**
     * 过线目标视频录制任务
     */
    struct VehicleVideoRecordTask
    {
        PassedTargetInfo start;                            //!< 开启一段新录像时的过线目标
        std::deque<PassedTargetInfo> passedTargets;        //!< “只上报”过线目标，即在该录制视频时间区间内的过线目标，这些目标不用录像，只记录在录像的时间偏移点，并上报
    };

    /**
     * @brief 事件录像
     */
    class VehicleVideoRecord : public VideoRecordBase
    {

    public:
        explicit VehicleVideoRecord(guint channel, guint before, guint duration) : VideoRecordBase(channel, before, duration){};
        ~VehicleVideoRecord() override = default;

        /**
         * @brief 录像完成回调
         */
        void setCompleted(guint realDuration) override;

        /**
         * @brief 注册录像开始回调，主要用于返回录像的文件名，并带上EvtVideoRecordTask参数，执行相关用户相关业务逻辑
         */
        void registerVehicleRecordCallback(VehicleRecordCB func);

        /**
         * @brief 提交录像任务
         */
        bool submit(const PassedTargetInfo& info);


    private:
        VehicleRecordCB onRecordCallback;                                    //!< 录像开始回调
        VehicleVideoRecordTask currentRecord;                               //!< 该通道当前正在录制的任务
        std::vector<VehicleVideoRecordTask> videoRecordTasks;            //!< 通道对应的录像任务
    };

    /**
     * 创建对应通道的录像context
     * @param[in] index   pipeline序号
     * @param[in] params  录像配置参数
     */
    void createVehicleRecord(guint index, guint before, guint after, const std::string& relativePath, NvDsSRInitParams params);
    /**
     * 获取对应通道的录像context
     * @param[in] index       通道号
     */
    std::shared_ptr<VehicleVideoRecord> getVehicleVideoRecord(guint index);
}

