#pragma once

#include "roi_object_parms.h"
#include "util/algorithm_util.h"
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

using namespace algorithm_util;

namespace roi_object_detect{

    class TraceObject{
        public:
            //Add the first rectangle and initialize the traceobject object.
            TraceObject(ROIObjectParam* roiObjectParam,int i,int id); 

            //Returns the current rectangle
            ObjBox getRect();

            //compared the rectangles calculated from the new frame with the tracking rectangle.
            //determine whether to add rectangles to the tracking chain.
            void compare(std::vector<ObjBox>& rects,std::vector<bool>& rectFlags);
            bool compareStatic(std::vector<ObjBox>& rects,std::vector<bool>& rectFlags);

            //is it the first time to alarm.
            bool isFirstAlarm();

            //determine whether to destroy this traceObject.
            bool judgeDelete();

            //judge whether the conditions are met
            bool judgeIsStatic();

        private:
            // trace rect
            ObjBox m_rect;

            //roi seg form binary img
            cv::Mat m_roiImg;

            //threshold used to determine whether the condition is met
            int m_traceTimes;
            int m_staticFrontTimesThres;
            int m_maxDecreaseTimes;
            int m_decreaseTimes;
            //trace id 
            int m_id;

            //is meet destroy conditions
            bool m_destroyStatus = false;
            //is firse alarm
            bool m_isFirstAlarm = true;
            //curr rect is having alarm flag
            bool m_Alarmflag;
            //During the tracking process, whether the main rect needs to meet the m_Alarmflag conditions if need to replacing main rect
            bool m_isUseRectFlag;

            //threshold used to calculate tracking condition
            float m_compareIouThres1;
            float m_compareIouThres2;
            float m_compareStaticThres;
    };

    class StaticObjGenerator{
        public:
            StaticObjGenerator();

            void reset();

            //tracing:add the rectangle calculated from the new frame to the calculation
            void appendRects(ROIObjectParam* roiObjectParam);

            //returns rectangles that meets the tracking condition
            void getStaticRegions(ROIObjectParam* roiObjectParam);
        private:
            //Used to store each tracking chain
            std::vector<TraceObject> m_traceObjectList;

            //Used to store each tracking chain which meet the multi frames conditions
            std::vector<TraceObject> m_staticObjectList;

            //trace id 
            int m_id;

    };
}
