/**
 * Project AI事件分析模块
 */

#include "feature_roi.h"

namespace evt
{
    FeatureRoi::FeatureRoi(FeatureRoiInfo info, int roiId)
    {
        featureRoiInfo = info;
        roiID = roiId;
        polygon = info.polygon;
    }

    /**
     * 设置检测尺寸
     */
    void FeatureRoi::setFrameSize(int w, int h)
    {
        polygon.scale(w,h);
    }

    /**
     * @brief       判断目标所在区域，当前时间是否可进行特征提取
     * @param rect  目标框
     */
    bool FeatureRoi::isEnableRetrieveFeature(Rect rect)
    {
        return (isContainPoint(rect) && isInTimePeriod());
    }

    /**
     * @brief       判断当前时间段是否可进行特征提取
     */
    bool FeatureRoi::isInTimePeriod()
    {
        //! 仅有一个时间配置为空 就不检测
        if ((featureRoiInfo.beginTime.empty() && !featureRoiInfo.endTime.empty()) ||
            (!featureRoiInfo.beginTime.empty() && featureRoiInfo.endTime.empty()))
            return false;

        //! 所有时间配置为空的话，全天检测
        if (featureRoiInfo.beginTime.empty() && featureRoiInfo.endTime.empty())
            return true;

        time_t time_t_now = time(nullptr);
        tm* tm_now = std::localtime(&time_t_now);

        tm tm_begin = *tm_now;
        tm_begin.tm_hour  = featureRoiInfo.beginHour;
        tm_begin.tm_min   = featureRoiInfo.beginMinute;
        tm_begin.tm_sec   = featureRoiInfo.beginSecond;

        tm tm_end = *tm_now;
        tm_end.tm_hour  = featureRoiInfo.endHour;
        tm_end.tm_min   = featureRoiInfo.endMinute;
        tm_end.tm_sec   = featureRoiInfo.endSecond;

        //! 跨天检测
        time_t time_t_begin = mktime(&tm_begin);
        time_t time_t_end = mktime(&tm_end);
        if (time_t_begin > time_t_end)  //! 起始时间大于结束时间，则需要进行跨天检测
        {
            if (time_t_now >= time_t_begin)
            {
                tm_end.tm_mday += 1;
                time_t_end = mktime(&tm_end);
            }
            else if (time_t_now < time_t_end)
            {
                tm_begin.tm_mday -= 1;
                time_t_begin = mktime(&tm_begin);
            }
        }

        return ((time_t_now >= time_t_begin) && (time_t_now <= time_t_end));
    }

}