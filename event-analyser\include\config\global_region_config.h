/**
 * Project AI事件分析模块
 */


#ifndef _GLOBALREGIONCONFIG_H
#define _GLOBALREGIONCONFIG_H

#include "module_def.h"
#include <string>
#include <map>

 /**
  * 全局配置 (单例)
  */
namespace evt
{
	#define G_REGION_CFG GlobalRegionConfig::getInstance()

	using std::map;
	using std::string;

	class EVT_EXPORT GlobalRegionConfig {
	public:

		/**
		 * 获取（单例）实例对象
		 */
		static GlobalRegionConfig* getInstance();

		GlobalRegionConfig();

		/**
		 * 初始化配置
		 * @param level 灵敏度等级
		 * @param cfg 参数配置
		 */
		void initConfig(int level, map<string, string>& cfg);

		/**
		 * 获取参数值
		 * @param val 配置值
		 * @param key 配置项
		 * @param level 灵敏度等级
		 * @return bool 是否包含配置
		 */
		template<typename T>
		bool getValue(T& val, string key, int level);

	private:
		map<int, map<string, string>> configs;
	};
}
#endif //_GLOBALREGIONCONFIG_H
