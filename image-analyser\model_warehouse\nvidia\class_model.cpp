#include "class_model.h"

#include <utility>

namespace model_warehouse{

    ClassModel::ClassModel(std::string modelFile, int deviceID, bool isVehicle, std::string meanFile)
				:BaseModel(std::move(modelFile), deviceID, isVehicle ? NetworkInputType::CHW : NetworkInputType::HWC, std::move(meanFile))
    {
		isVehicleModel = isVehicle;
    }

    /**
     * 模型加载，资源初始化
     */
    void ClassModel::initResource()
    {
        if (!modelInited)
        {
            predictor.init(std::move(modelPath), deviceId);

            initInputDimensions();
            initOutputDimensions();

            if (!scoreDataHost)
                scoreDataHost = std::shared_ptr<float[]>(new float[batchSize * outputChannel]);
            if (!scoreDataDevice)
                CHECK_CUDA_STATUS(cudaMalloc(&scoreDataDevice, batchSize * outputChannel * sizeof(float)), "cudaMalloc failed", true);
            if (!imageDataDevice)
                CHECK_CUDA_STATUS(cudaMalloc(&imageDataDevice, batchSize * inputWidth * inputHeight * inputChannel * sizeof(float)), "cudaMalloc failed", true);

            modelInited = true;
        }
    }

    /**
     * @brief 初始化模型输入维度信息
     */
    void ClassModel::initInputDimensions()
    {
        if (isVehicleModel)
        {
            auto dims0 = predictor.getBindingDimensions(0);
            batchSize = 1;
			inputChannel = dims0.d[1];
			inputHeight = dims0.d[2];
			inputWidth = dims0.d[3];
        }
        else
        {
            BaseModel::initInputDimensions();
        }
    }

    /**
     * @brief 初始化模型输出维度信息
     */
    void ClassModel::initOutputDimensions()
    {
        auto dims1 = predictor.getBindingDimensions(1);
        //outputChannel = isVehicleModel ? dims1.d[0] : dims1.d[1];
		outputChannel = dims1.d[1];
    }

    /**
     * @brief                  执行一次图像推理流程：预处理、推理、后处理输出
     * @param[in] images       原始输入图像
     * @param[in]  padding     是否进行图像padding
     * @return                 推理后处理输出结果
     */
    CLASS_RESULT ClassModel::infer(std::vector<cv::Mat> images, bool padding)
    {
        vector<float> imageData;
        if (preprocessFunc)
            preprocessFunc(images, inputWidth, inputHeight, imageData);
        else
            preprocess(images, false, !isVehicleModel, imageData);

        CHECK_CUDA_STATUS(cudaMemcpy(imageDataDevice, imageData.data(), imageData.size() * sizeof(float), cudaMemcpyHostToDevice),"Failed to copy image data to device",false);
        vector<void *> buffers = {imageDataDevice, scoreDataDevice};
        if (!predictor.infer(buffers, (int)images.size()))
            return {};

        return postprocess((int)images.size());
    }

    /**
     * @brief                  推理后处理，处理推理输出结果
     * @param imageSize        推理图像数量
     * @return                 推理后处理输出结果
     */
    CLASS_RESULT ClassModel::postprocess(int imageSize)
    {
        std::vector<std::vector<float>> scores;
        CHECK_CUDA_STATUS(cudaMemcpy(scoreDataHost.get(), scoreDataDevice, batchSize * outputChannel * sizeof(float), cudaMemcpyDeviceToHost), "Failed to copy score data to host", false);

        for(int i=0; i< imageSize; i++)
        {
            const float* row = scoreDataHost.get() + i * outputChannel;

            // 1. 找最大值（数值稳定）
            float maxScore = *std::max_element(row, row + outputChannel);

            // 2. 计算 softmax分子（exp(x - max)）并累计和
            std::vector<float> prob(outputChannel);
            float sumExp = 0.0f;

            for (int j = 0; j < outputChannel; ++j)
            {
                prob[j] = std::exp(row[j] - maxScore);
                sumExp +=  prob[j];
            }

            // 3. 归一化输出
            for (int j = 0; j < outputChannel; ++j)
                prob[j] /= sumExp;

            scores.push_back(prob);
        }

        return scores;
    }


    std::optional<int> ClassModel::getMaxResult(std::vector<float>& results, float threshold)
    {
        auto itMax = std::max_element(results.begin(), results.end());
        if ((*itMax) > threshold)
        {
            int klass =(int)(itMax - results.begin());
            return klass;
        }
        return std::nullopt;
    }
}
