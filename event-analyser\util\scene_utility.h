/**
 * Project AI事件分析模块
 */


#ifndef _SCENEUTILITY_H
#define _SCENEUTILITY_H

#include <vector>
#include <string>
#include "element/point.h"
#include "element/rect.h"

namespace evt
{
	#define PI 3.1415926

	// 默认ID 长度
	#define DEFAULT_ID_LENGTH 8

	/**
	 * IOU计算
	 * @param r1
	 * @param r2
	 */
	float calculateIOU(Rect r1, Rect r2);

	/**
	 * 距离计算
	 * @param p1
	 * @param p2
	 */
	float calculateDistance(Point p1, Point p2);

	/**
	 * 距离计算（平方），避免开根号消耗
	 * @param p1
	 * @param p2
	 */
	float calculateDistanceSquare(Point p1, Point p2);

	/**
	 * 距离检查
	 * @param p1
	 * @param p2
	 * @param dis
	 */
	bool checkIfDistanceGreaterThan(Point p1, Point p2, float dis);

	/**
	 * 获取当前系统时间戳
	 */
	long long systemTimestamp();

	/**
	 * 判断点是否在直线左边
	 * @param p
	 * @param p1
	 * @param p2
	 * @param w
	 * @param h 
	 */
	bool leftOfLine(Point p, Point p1, Point p2, int w = 1, int h = 1);

	/**
	 * 生成UUID
	 * @param len 字符长度
	 */
	std::string generateUUID(const unsigned int len = DEFAULT_ID_LENGTH);
}
#endif //_SCENEUTILITY_H