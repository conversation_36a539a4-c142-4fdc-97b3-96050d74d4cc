/**
 * Project AI事件分析模块
 */

#include "area.h"
#include "roi.h"
#include "detector/drive_across_event_detector.h"
#include "detector/drive_in_event_detector.h"
#include "detector/drive_away_event_detector.h"
#include "detector/jam_event_detector.h"
#include "detector/opposite_event_detector.h"
#include "detector/pedestrain_event_detector.h"
#include "detector/stop_event_detector.h"
#include "detector/two_wheels_event_detector.h"
#include "detector/object_event_detector.h"
#include "detector/road_construction_event_detector.h"
#include "detector/accident_event_detector.h"
#include "config/event_config.hpp"
#include "channel/scene_state.h"

/**
 * Area implementation
 * 
 * 区域（基类）
 */

namespace evt {

	Area::Area() :id(0), roiID(0), eventMask(0xfffffff),targetMask(0xfffffff),
		eventMap(NULL), targetMap(NULL), ignored(false), areaType(AreaType_None), jamState(false)
	{

	}

	Area:: ~Area()
	{
		for (auto d: detectors)
		{
			delete d;
		}
		detectors.clear();
		targetList.clear();
		roiEventTargets.clear();
	}

	/**
	 * 预处理已有事件
	 * 标记 ROI内 事件范围影响处理
	 */
	void Area::preprocessEvents()
	{
		if (eventMap == NULL || targetMap == NULL) return;
		for (auto detector : detectors)
		{
			if (detector && detector->getDetectorType() == DetectorType::DetectorType_Target)
			{
				auto targetDetector = dynamic_cast<TargetEventDetector*>(detector);
				if (targetDetector != NULL)
				{
					onTargetEventUpdate(targetDetector);
				}
			}
		}
	}

	/**
	 * 检测事件
	 */
	void Area::detectEvents() {

		if (eventMap == NULL || targetMap == NULL) 
			return;

		for (auto detector : detectors)
		{
			auto evtType = detector->getEventType();
			// 区域检测开始
			detector->onUpdateRegionStart();

			// 图像事件 检测
			if (detector->getDetectorType() == DetectorType::DetectorType_Object)
			{
				onObjectEventUpdate(dynamic_cast<ObjectEventDetector*>(detector));
			}
			else if (detector->getDetectorType() == DetectorType::DetectorType_Class)
            {
                onClassEventUpdate(dynamic_cast<ClassEventDetector*>(detector));
            }
            else
			{
				auto targetDetector = dynamic_cast<TargetEventDetector*>(detector);
				if (isROIEventType(evtType))// ROI 类型事件检测
				{
					if (evtType == EventType_RoadConstruction|| evtType == EventType_Accident) //处理路障目标
					{
						for (auto& evtObj : eventObjList)
						{
							targetDetector->process(evtObj);
						}
					}
					for (auto& target : roiEventTargets[evtType])
					{
						targetDetector->process(target);
					}
				}
				else
				{
					// 普通事件检测
					for (auto& target : targetList)
					{
						// 拥堵 停车
						if (evtType == EventType_Stop && jamState) 
						{
							if (EVENT_CFG->ignoreEmergencyStopWhenJam() || target->getLaneType() != LaneType_Emergency)
							{
								continue;
							}
						}

						// 目标事件已匹配
						if (target->matched.contains(evtType))
							break;

						// 目标已有事件类型
						if (!target->getEventID().empty() && target->getEventType() == detector->eventType)
							break;

						auto newEvent = targetDetector->process(target);
						if (newEvent != NULL)
						{
							newEvent->info.laneID = target->getLaneID();
							newEvent->info.laneType = target->getLaneType();
							newEvent->info.roiID = target->getROIID();
							newEvent->info.regionID = target->getRegionID();
                            newEvent->info.targetClassTypes = target->getClassTypes();
							newEvent->info.inConstruction = this->constructionState;
							newEvent->info.roiArea = target->getROIArea();
							if (!newEvent->info.occurArea.isValid())
								newEvent->updateOccurArea(framePolygon);

							eventMap->addEvent(newEvent);
							if (evtType == EventType_Pedstrain)
							{
								for (auto& tmpTarget : targetList)
								{
									if (tmpTarget == target || tmpTarget->getType() != TargetType_Pedstrain)
										continue;
									newEvent->info.extraRects.emplace_back(tmpTarget->bound());
								}
							}
							break;
						}
					}
				}
			}
			// 区域检测结束
			auto newEvent = detector->onUpdateRegionEnd();
			if (newEvent != NULL)
			{
				newEvent->info.laneID = 0;
				newEvent->info.roiID = roiID;
				newEvent->info.regionID = 0;
				newEvent->info.inConstruction = this->constructionState;
				if (!newEvent->info.occurArea.isValid())
					newEvent->updateOccurArea(framePolygon);

				eventMap->addEvent(newEvent);
			}
		}
	}

	/**
	 * 更新已有事件匹配
	 */
	void Area::onTargetEventUpdate(TargetEventDetector* detector)
	{
		auto evtType = detector->getEventType();
		// 检查 已有事件 周围匹配情况
		for (auto evt : detector->holdEvents)
		{
			bool originMatch = false;  // 是否是原生目标
			TargetPtr matchedTarget = evt->getTarget();
			if (matchedTarget != NULL ) // 事件存在关联目标
			{
				matchedTarget->matched.set(evtType, true);
				if (matchedTarget->removed || !matchedTarget->info.roiID)
				{
					matchedTarget = NULL;
					evt->setTarget(NULL);
				}
				else
				{
					originMatch = true;
					// 针对停车事件 原生目标 是否有漂移ID情况
					if (detector->eventType == EventType_Stop)
					{
						if (!detector->matchCheck(evt, matchedTarget))
						{
							originMatch = false;
						}
					}
				}
			}

			// 标记事件范围内 影响的目标
			auto evtMatchedTargets = targetMap->getTargetsByRect(evt->bound());
			for (auto target : evtMatchedTargets)
			{
				if (target->getROIID() != roiID)
					continue;

				if (target->matched.contains(evtType))
					continue;

				bool targetMatched = detector->eventMatchSpace(target);
				target->matched.set(evtType, targetMatched);
				if (!targetMatched)
					continue;

				if (matchedTarget == NULL || !originMatch) // 事件目标已丢失 或原生目标跳框 重新匹配更好的目标
				{
					if (detector->matchCheck(evt, target))
					{
						matchedTarget = target;
					}
				}
			}

			if (matchedTarget && !matchedTarget->getShowable())
				matchedTarget = NULL;

			if (matchedTarget != NULL)
			{
				Area* curArea = matchedTarget->getCurrentArea();
				//若目标当前所在区域为空，或区域不检当前事件(驶离事件除外)，或区域不检当前目标类型，则清除事件状态
				if (!curArea || (!curArea->hasEventType(evtType) && EventType::EventType_DriveAway != evtType) || !curArea->hasTargetType(matchedTarget->getType()))
				{
					//若目标当前事件为检测器类型，清空事件状态
					if ( matchedTarget->getEventType() == evtType)
						matchedTarget->updateEvent(NULL);
					matchedTarget = NULL;
					evt->setTarget(NULL);
				}
				else
				{
					matchedTarget->matched.set(evtType, true);
					evt->setTarget(matchedTarget);
				}
			}
			detector->onUpdateEvent(evt, matchedTarget);
		}
	}

	/**
	 * 更新物体事件匹配
	 */
	void Area::onObjectEventUpdate(ObjectEventDetector * detector)
	{
		detector->onUpdateEvent();

		for (auto& evtObj : eventObjList)
		{
			if (evtObj.type == detector->getEventType() && !evtObj.matched)
			{
				if (evtObj.type == EventType_Obstacle 
					|| (evtObj.type == EventType_RoadBlock && EVENT_CFG->checkEventObjectIOU()))
				{
					//发现周围的目标是否和图像事件的iou重叠 ，则忽略
					auto baseRect = evtObj.rect;
					float xPadding = evtObj.type == EventType_Obstacle? baseRect.width/ 8.f : 0.f;
					float yPadding = evtObj.type == EventType_Obstacle? baseRect.height/ 8.f : 0.f;
					auto checkRect = Rect(baseRect.x - xPadding, baseRect.y - yPadding, baseRect.width + 2 * xPadding, baseRect.height + 2 * yPadding);
					auto surroundedObjs = targetMap->getTargetsByRect(checkRect);
					if (!surroundedObjs.empty())
					{
						//IVA_LOG_INFO("roi {} ignore Obstacle for has objs {} x {} y {} width {} height {}", roiID, surroundedObjs.size(), baseRect.x, baseRect.y, baseRect.width, baseRect.height);
						continue;
					}
				}

				auto newEvent = detector->process(evtObj);
				if (newEvent != NULL)
				{
					newEvent->info.laneID = 0;
					newEvent->info.roiID = roiID;
					newEvent->info.regionID = id;
					newEvent->info.inConstruction = this->constructionState;
					if (!newEvent->info.occurArea.isValid())
						newEvent->updateOccurArea(framePolygon);

					eventMap->addEvent(newEvent);
//					break;
				}
			}
		}
	}

    /**
 * 更新物体事件匹配
 */
    void Area::onClassEventUpdate(evt::ClassEventDetector *detector)
    {
        detector->onUpdateEvent();

        for (auto& evtObj : eventObjList)
        {
            if (evtObj.type == detector->getEventType())
            {
                auto newEvent = detector->process(evtObj);
                if (newEvent != nullptr)
                {
                    newEvent->info.laneID = 0;
                    newEvent->info.roiID = roiID;
                    newEvent->info.regionID = id;
                    eventMap->addEvent(newEvent);
//					break;
                }
            }
        }
    }
	/**
	 * 初始化检测器
	 */
	void Area::initDetectors(std::vector<EventType>& globalTypes) {

		if (isIgnoreArea()) return;

		for (const auto evtType : allEventTypes)
		{
			bool flagDetectEvent = hasEventType(evtType);
			if (isROIEventType(evtType))// 检测是否 ROI专有事件
			{
				if(areaType != AreaType_ROI)
					continue; 
				
				if (!flagDetectEvent)
					flagDetectEvent = hasROIEventType(evtType);
			}

			if (flagDetectEvent || std::find(globalTypes.begin(), globalTypes.end(), evtType) != globalTypes.end())
			{
				EventDetector* detector = NULL;
				switch (evtType)
				{
				case EventType_Stop:
					detector = new StopEventDetector();
					break;
				case EventType_Opposite:
					if(direction.isValid())
						detector = new OppositeEventDetector(direction);
					break;
				case EventType_Jam:
					detector = new JamEventDetector(this);
					break;
				case EventType_DriveIn:
					detector = new DriveInEventDetector();
					break;
				case EventType_DriveAcross:
					detector = new DriveAcrossEventDetector();
					break;
				case EventType_DriveAway:
					detector = new DriveAwayEventDetector();
					break;
				case EventType_Pedstrain:
					detector = new PedestrainEventDetector();
					break;
				case EventType_TwoWheels:
					detector = new TwoWheelsEventDetector();
					break;
				case EventType_Obstacle:
				case EventType_RoadBlock:
                case EventType_Landslide:
				case EventType_FireSmoke:
					detector = new ObjectEventDetector();
					break;
                case EventType_Weather_Fog:
                case EventType_Weather_Rain:
                case EventType_Weather_Snow:
                    detector = new ClassEventDetector();
                    break;
				case EventType_RoadConstruction:
					detector = new RoadConstructionEventDetector(this);
					break;
				case EventType_Accident: 
					detector = new AccidentEventDetector(this);
					break;
				default:
					break;
				}

				if (detector != NULL)
				{
					detector->init(evtType);
					detector->setRegionConfig(config);
					detectors.push_back(detector);
				}
			}
		}
	}

	/**
	 * 清除目标
	 */
	void Area::clearTargets() {
		targetList.clear();
		roiEventTargets.clear();
	}

	/**
	 * 清除事件物体 (抛洒物等)
	 */
	void Area::clearEventObjects()
	{
		eventObjList.clear();
	}

	/**
	 * 增加目标
	 * @param target
	 */
	bool Area::addTarget(TargetPtr target) {
		target->matched = 0;
		if (isIgnoreArea())
		{			
			target->info.showable = false;
			return false;
		}
		// 当前区域检测此类型 才实际加到列表
		if (hasTargetType(target->getType()))
		{
			target->info.registX = this->targetRegistX;
			target->info.registY = this->targetRegistY;
			if (checkTargetSize(target))
			{
				target->info.showable = true;
				targetList.push_back(target);
				return true;
			}
		}
		return false;
	}

	/**
	 * 增加事件目标
	 * @param target
	 */
	bool Area::addEventObject(EventObject& object) 
	{
		// 关联 事件物体 类型列表
		static const std::map<EventType, std::initializer_list<EventType>> relatedEventTypes =
		{
			{EventType_RoadBlock, {EventType_RoadConstruction, EventType_Accident} }   //施工/事故 事件 依赖路障
		};

		if (framePolygon.containPoint(object.rect.getCenterPosition()))
		{
			bool hasEventObjectType = hasEventType(object.type);
			if (!hasEventObjectType)
			{
				if (relatedEventTypes.find(object.type) != relatedEventTypes.end())
				{
					auto& checkList = relatedEventTypes.at(object.type);
					for (auto t : checkList)
					{
						if (hasEventType(t) || hasROIEventType(t))
						{
							eventObjList.push_back(object);
							return false;
						}
					}
				}
			}
			else
			{
				eventObjList.push_back(object);
				return true;
			}
		}

        auto it = std::find_if( detectors.begin(), detectors.end(), [](EventDetector* d) {return isWeatherEvent(d->getEventType()); });
        if (it != detectors.end() && isWeatherEvent(object.type))
        {
            if (hasEventType(object.type))
            {
                eventObjList.push_back(object);
                return true;
            }
        }
		return false;
	}

	/**
	 * 匹配目标
	 */
	bool Area::matchTarget(TargetPtr target)
	{
		return framePolygon.containPoint(target->getLatestRect().getPosition(this->targetRegistX, this->targetRegistY));
	}

	/**
	 * 检查目标尺寸是否满足
	 * @param target
	 */
	bool Area::checkTargetSize(TargetPtr target)
	{
		auto rect = target->getLatestRect();

		auto targetType = target->getType();
		float maxSize, minSize;
		if (targetType == TargetType::TargetType_Pedstrain || targetType == TargetType_TwoWheels)
		{
			maxSize = maxSizePedstrain;
			minSize = minSizePedstrain;
		}
		else
		{
			maxSize = maxSizeVehicle;
			minSize = minSizeVehicle;
		}
		if (rect.width * rect.height > std::pow(maxSize, 2) * frameWidth * frameHeight)
			return false;
		if (rect.width * rect.height < std::pow(minSize, 2) * frameWidth * frameHeight)
			return false;

		return true;
	}

	/**
	 * 当前区域目标数量
	 * @return int
	 */
	int Area::targetCount() {
		return static_cast<int>(targetList.size());
	}

	/**
	 * 设置事件地图
	 * @param map
	 */
	void Area::setEventMap(EventMap * map)
	{
		this->eventMap = map;
	}

	/**
	 * 设置目标地图
	 * @param map
	 */
	void Area::setTargetMap(TargetMap * map)
	{
		this->targetMap = map;
		if (this->targetMap)
		{
			this->targetMap->initAreaHeatMapIndexs(framePolygon, heatmapIndexs);
		}
	}

	/**
	 * 设置检测尺寸
	 */
	void Area::setFrameSize(int w, int h)
	{
		this->frameWidth = w;
		this->frameHeight = h;

		framePolygon = polygon;
		framePolygon.scale(w, h);

		if (this->targetMap)
		{
			this->targetMap->initAreaHeatMapIndexs(framePolygon, heatmapIndexs);
		}
	}

	/**
	 * 设置开始检测时间
	 */
	void Area::setStartDetectTime(steady_clock::time_point time)
	{
		this->startTime = time;
	}

	/**
	 * 获取区域热力值
	 */
	float Area::getHeatValue()
	{
		if (this->targetMap)
		{
			return this->targetMap->getHeatValue(heatmapIndexs);
		}
		return 0.f;
	}

	/**
	 * 获取区域占比
	 */
	float Area::getOccupiedValue()
	{
		if (this->targetMap)
		{
			return this->targetMap->getOccupiedValue(heatmapIndexs);
		}
		return 0.f;
	}

	/**
	 * 是否是免检区
	 */
	bool Area::isIgnoreArea()
	{
		return ignored;
	}

    /**
     * 是否是图像子检测区
     */
    bool Area::isImageDetectAreaOnly()
    {
        return eventMask.containsOnly({EventType_Obstacle, EventType_RoadBlock, EventType_FireSmoke, EventType_Weather_Fog,EventType_Weather_Rain, EventType_Weather_Snow, EventType_Landslide}, 1);
    }

    /**
	 * 获取ID
	 * @return int
	 */
	int Area::getID() {
		return id;
	}

	/**
	 * 是否检测事件类型
	 * @param type
	 * @return bool
	 */
	bool Area::hasEventType(EventType type) {
		return this->eventMask.contains(type, 1);
	}

	/**
	 * 是否检测目标类型
	 * @param type
	 * @return bool
	 */
	bool Area::hasTargetType(TargetType type) {
		return !this->targetMask.contains(type, 0);
	}

	/**
	 * 是否检测ROI事件类型
	 * @param type
	 */
	bool Area::hasROIEventType(EventType type) {
		return this->roiEventMask.contains(type, 1);
	}

	/**
	 * 更新ROI事件掩码
	 * 如果子区域勾选了 ROI事件类型 则更新ROI事件掩码
	 * 【即】：子区域仅决定目标是否用于检测、事件由ROI上报
	 * @param roiMask
	 */
	void Area::updateROIEventMask(MaskValue& roiMask) {
		for (auto evtType : roiEventTypes)
		{
			if (evtType == EventType_Jam && areaType == AreaType_Lane)
				continue; // (行车道必检拥堵，兼容性考虑 拥堵由ROI或子区域勾选决定)

			if (this->hasEventType(evtType))
				roiMask.set(evtType, true, 1);
		}
	}

	/**
	 * 是否包含目标
	 * @param target
	 */
	bool Area::containTarget(TargetPtr target)
	{
		for (auto& t: targetList)
		{
			if (t->getID() == target->getID())
				return true;
		}
		return false;
	}

	/**
	 * 是否包含坐标
	 * @param point
	 */
	bool Area::containPosition(Point point)
	{
		return framePolygon.containPoint(point);
	}

	/**
	 * 是否包含热力图序号
	 * @param index
	 */
	 bool Area::containHeatmapIndex(int index)
	 {
		 return std::find(heatmapIndexs.begin(), heatmapIndexs.end(), index) != heatmapIndexs.end();
	 }

	 /**
	  * 获取周围目标
	  * @param pos 目标位置
	  * @param padding 查找范围
	  */
	 TargetList Area::getAdjacentTargets(Point pos, int padding)
	 {
		 Rect rect{ pos.x- padding, pos.y- padding, 2.f* padding, 2.f* padding };
		 TargetList adjacentTargets = targetMap->getTargetsByRect(rect, roiID);
		 return adjacentTargets;
	 }

	/**
	 * 初始化通用参数
	 */
	void Area::onConfigUpdated()
	{
		config.getValue<float>(maxSizeVehicle, MAX_SIZE_VEHICLE);
		config.getValue<float>(minSizeVehicle, MIN_SIZE_VEHICLE);
		config.getValue<float>(maxSizePedstrain, MAX_SIZE_PEDSTRAIN);
		config.getValue<float>(minSizePedstrain, MIN_SIZE_PEDSTRAIN);
		config.getValue<float>(targetRegistX, TARGET_REGIST_X);
		config.getValue<float>(targetRegistY, TARGET_REGIST_Y);
	}

	/**
	 * 场景是否偏移 (交通流)
	 */
	bool Area::inShifting()
	{
		return sceneState->isShifted() || sceneState->isShifting();
	}

}
