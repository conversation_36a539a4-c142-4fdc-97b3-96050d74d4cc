/**
 * Project AI事件分析模块
 */

#include "channel.h"
#include "scene.h"

/**
 * Channel implementation
 * 
 * 分析通道
 *
 * 每个通道至少包含一个分析场景（对应一个预置位）
 */
namespace evt
{
	Channel::Channel(int id):
		frameRate(DEFAULT_FRAME_RATE),
		frameWidth(DEFAULT_FRAME_WIDTH),
		frameHeight(DEFAULT_FRAME_HEIGHT)
	{
		this->channelID = id;
	}

	Channel::~Channel() {
		for (auto& s : scenes)
		{
			delete s.second;
		}
	}

	/**
	 * 初始化感兴趣区配置
	 * @param roicfg
	 * @param evtWidth,evtHight 事件分析处理宽高
	 * @param presetID 预置位ID
	 * @param planLevel 参数预案等级
	 */
	void Channel::init(ROIInfoList& roicfgs, int evtWidth, int evtHight, int presetID, int planLevel)
    {
        {
            std::lock_guard<std::mutex> m(sceneMutex);
            // 场景已经存在
            // 【下发配置操作】先删除后新建
            if (scenes.find(presetID) != scenes.end())
            {
                delete scenes[presetID];
            }

            auto scene = new Scene(channelID);
            scene->init(roicfgs);
            // 设置回调
            if (newEvtCallback)
            {
                scene->setNewEvtCallback(newEvtCallback);
            }
            if (targetsPassedCallback)
            {
                scene->setTargetsPassedCallback(targetsPassedCallback);
            }
            if (vehiclesPassedCallback)
            {
                scene->setVehiclesPassedCallback(vehiclesPassedCallback);
            }

            if (withdrawEvtCallback)
            {
                scene->setWithdrawEvtCallback(withdrawEvtCallback);
            }
            scenes[presetID] = scene;
        }
		setFrameSize(evtWidth, evtHight);
	}

	/**
	 * 更新轨迹（新增或更新目标）
	 * @param tracks 轨迹框列表
	 * @param frameIndex 检测帧序号
	 * @presetID 预置位ID
	 */
	bool Channel::update(TrackList& tracks, long frameIndex, int presetID) {
		std::lock_guard<std::mutex> m(sceneMutex);
		if (scenes.find(presetID) != scenes.end())
		{
			return scenes[presetID]->update(tracks, frameIndex);
		}
		return false;
	}

	/**
	 * 更新场景事件物体信息 （抛洒物、烟火、路障等）
	 * @param eventObjects 事件物体列表
	 * @presetID 预置位ID
	 */
	void Channel::update(EventObjectList& eventObjects, int presetID)
	{
		std::lock_guard<std::mutex> m(sceneMutex);
		if (scenes.find(presetID) != scenes.end())
		{
			scenes[presetID]->update(eventObjects);
		}
	}

	/**
	 * 暂停检测
	 * @presetID 预置位ID
	 * @camOffset 是否摄像机偏移暂停
	 */
	void Channel::pause(int presetID, bool camOffset)
	{
		std::lock_guard<std::mutex> m(sceneMutex);
		if (presetID <= 0)
		{
			for (auto& pair : scenes)
			{
				pair.second->pause(camOffset);
			}
		}
		else if (scenes.find(presetID) != scenes.end())
		{
			scenes[presetID]->pause(camOffset);
		}
	}

	/**
	 * 恢复检测
	 * @presetID 预置位ID
	 */
	void Channel::resume(int presetID)
	{
		std::lock_guard<std::mutex> m(sceneMutex);
		if (presetID <= 0)
		{
			for (auto& pair : scenes)
			{
				pair.second->resume();
			}
		}
		else if (scenes.find(presetID) != scenes.end())
		{
			scenes[presetID]->resume();
		}
	}

	/**
	 * 设置画面尺寸
	 * @param w 宽
	 * @param h 高
	 */
	void Channel::setFrameSize(int w, int h)
	{
		frameWidth = w;
		frameHeight = h;
		std::lock_guard<std::mutex> m(sceneMutex);
		for (auto& s : scenes)
		{
			s.second->setFrameSize(w, h);
		}
	}

	/**
	 * 设置画面帧率
	 * @param rate 帧率
	 */
	void Channel::setFrameRate(int rate)
	{
		frameRate = rate;
		std::lock_guard<std::mutex> m(sceneMutex);
		for (auto& s : scenes)
		{
			s.second->setFrameRate(rate);
		}
	}

	/**
	 * 获取当前所有的目标集合
	 * @presetID 预置位ID
	 */
	void Channel::getCurrentTargets(TargetInfoList& outputTargets, int presetID)
	{
		std::lock_guard<std::mutex> m(sceneMutex);
		if (scenes.find(presetID) != scenes.end())
		{
			scenes[presetID]->getCurrentTargets(outputTargets);
		}
	}

	/**
	 * 获取当前事件信息
	 * @outputEvents 输出事件信息集合
	 * @evtType 查找类型
	 * @presetID 预置位ID
	 */
	void Channel::getCurrentEventInfos(EventInfoList& outputEvents, EventType evtType, int presetID)
	{
		std::lock_guard<std::mutex> m(sceneMutex);
		if (scenes.find(presetID) != scenes.end())
		{
			scenes[presetID]->getCurrentEventInfos(outputEvents, evtType);
		}
	}

	/**
	 * 设置发生新事件回调
	 * @param cb
	 */
	void Channel::setNewEvtCallback(EventCallback cb) {
		newEvtCallback = cb;
		std::lock_guard<std::mutex> m(sceneMutex);
		for (auto& s : scenes)
		{
			s.second->setNewEvtCallback(cb);
		}
	}

	/**
	 * 设置事件撤回回调
	 * @param cb
	 */
	void Channel::setWithdrawEvtCallback(EventCallback cb)
	{
		withdrawEvtCallback = cb;
		std::lock_guard<std::mutex> m(sceneMutex);
		for (auto& s : scenes)
		{
			s.second->setWithdrawEvtCallback(cb);
		}
	}

	/**
	 * 设置过线目标回调
	 * @param cb
	 */
	void Channel::setTargetsPassedCallback(TargetsPassedCallback cb) {
		targetsPassedCallback = cb;
		std::lock_guard<std::mutex> m(sceneMutex);
		for (auto& s : scenes)
		{
			s.second->setTargetsPassedCallback(cb);
		}
	}

    /**
     * 设置车辆过线回调
     * @param cb
     */
    void Channel::setVehiclesPassedCallback(TargetsPassedCallback cb) {
        vehiclesPassedCallback = cb;
        std::lock_guard<std::mutex> m(sceneMutex);
        for (auto& s : scenes)
        {
            s.second->setVehiclesPassedCallback(cb);
        }
    }

	void Channel::setVehiclesInfoCallback(const VehiclesInfoCallback &cb)
	{
		vehiclesInfoCallback = cb;
		std::lock_guard m(sceneMutex);
		for (const auto&[preset, scene] : scenes)
		{
			scene->setVehiclesInfoCallback(cb);
		}
	}

    std::vector<Polygon> Channel::getRoiList(int presetId)
    {
        std::lock_guard<std::mutex> m(sceneMutex);
        if (scenes.find(presetId) == scenes.end())
        {
            IVA_LOG_WARN("Failed to find the scene corresponding to preset {} of channel {}.", presetId, channelID)
            return {};
        }

        return scenes[presetId]->getRoiList();
    }
}