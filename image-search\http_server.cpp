#include "http_server.h"
#include "feature_task.h"
#include "util/json_util.h"
#include "opencv2/imgcodecs.hpp"
#include "config/detect_config.hpp"
#include "config/feature_model_config.hpp"
#include "cereal/archives/json.hpp"
#include "asio2/http/http_server.hpp"
#include "log.h"

namespace iss::server
{
	using namespace ia;
	using namespace cereal;
	using namespace model_warehouse;

	// json 打印最多显示字符
	constexpr auto MAX_RESULT_DEBUG = 200;
	// 最大小数点位数
	constexpr auto MAX_DECIMAL_PLACES = 324;

	// 检测目标最低 宽高像素
	constexpr auto MIN_OBJECT_WIDTH = 64;
	constexpr auto MIN_OBJECT_HEIGHT = 64;

	std::shared_ptr<asio2::http_server> server;

	// 特征相关模型
	FeatureAction featureAction;
	DetectModel* vehicleDetectModel = nullptr;
	ClassModel* vehicleColorModel = nullptr;
	ClassModel* vehicleTypeModel = nullptr;

	// 分类模型阈值
	float vehicleColorThreshold = 0.5f;
	float vehicleTypeThreshold = 0.5f;

	// 分类标签
	std::vector<std::string> vehicleTypeLabels;
	std::vector<std::string> vehicleColorLabels;

	std::map<int, std::string> errorMsg = { { Code_Success , "success"},
											{ Code_Error_NoInputImage , "no input image"},
											{ Code_Error_NoInputRect , "no input rect"},
											{ Code_Error_InputError , "input parse error"},
											{ Code_CV_Error , "image parse error"},
											{ Code_Infer_Error , "model infer error"},
											{ Code_Inner_Error , "inner error"} };

	void DetectMsg::setMsgCode(int msgcode) { this->code = msgcode; this->msg = errorMsg[msgcode]; }
	void FeatureMsg::setMsgCode(int msgcode) { this->code = msgcode; this->msg = errorMsg[msgcode]; }
	bool parseLabelsFile(const std::string& labelsFilePath, std::vector<std::string>& labels);

	struct aop_check
	{
		bool before(std::shared_ptr<asio2::http_session>& session_ptr, http::request& req, http::response& rep)
		{
			asio2::detail::ignore_unused(session_ptr, req, rep);
			//printf("aop_check before\n");
			return true;
		}
		bool after(http::request& req, http::response& rep)
		{
			asio2::detail::ignore_unused(req, rep);
			//printf("aop_check after\n");
			return true;
		}
	};

	/*
	* 初始化HTTP服务
	* @param port 监听端口
	* @param deviceId 设备ID
	*/
	void init(int port, int deviceId)
	{
		server = std::make_shared<asio2::http_server>();
		//server->bind_recv([](auto& session_ptr, http::request& req, http::response& rep)
		//	{
		//		std::string data(req.query());
		//		std::cout << data << std::endl;
		//	});
		server->bind_start([&](asio::error_code ec)
			{
				if (ec)
					std::cerr << "\n server started failed! port: " << port << " " << asio2::last_error_msg() << "\n -----------------------------------" << std::endl;
				else
					std::cout << "\n server started successfully! port: " << port << "\n -----------------------------------" << std::endl;
			});

		server->bind<http::verb::get>("/feature", [](http::request& req, http::response& rep)
			{
				asio2::detail::ignore_unused(req, rep);
				rep.fill_html(u8R"(
					<!doctype html>
					<meta charset="utf-8">
					<style type="text/css">
					#inputtext {width: 800px;}
					</style>
					<title>目标特征</title>
					<h1>目标特征提取</h1>
					<form action = "" method = post enctype = multipart/form-data>
					<p>img: <input type=file name=img value='选择图片'>
					<p>rect: <input type=text name=rect value='[{"x":0,"y":0},{"x":1,"y":1}]' id=inputtext>
					<p><p><input type=submit value='提取特征'></form>
					<p><br></p>)");
				rep.chunked(true);

			}, aop_check{});

		server->bind<http::verb::get>("/detect", [](http::request& req, http::response& rep)
			{
				asio2::detail::ignore_unused(req, rep);
				rep.fill_html(u8R"(
					<!doctype html>
					<meta charset="utf-8">
					<title>目标检测</title>
					<h1>目标检测</h1>
					<form action = "" method = post enctype = multipart/form-data>
					<p>img: <input type=file name=img value='选择图片'>
					<input type=submit value='检测目标'></form>
					<p><br></p>)");
				rep.chunked(true);
			}, aop_check{});

		server->bind<http::verb::post>("/feature", [](std::shared_ptr<asio2::http_session>& session_ptr, http::request& req, http::response& rep)
			{
				asio2::detail::ignore_unused(req, rep);
				auto remoteIP = session_ptr->remote_address();
				auto remotePort = session_ptr->remote_port();

				FeatureMsg msg;
				msg.setMsgCode(Code_Success);
				std::string_view rawimg;

				auto mpbody = req.multipart();
				//for (auto it = mpbody.begin(); it != mpbody.end(); ++it)
				//{
				//	std::cout << "filename:" << it->filename() << " name:" << it->name() << " content_type:" << it->content_type() << std::endl;
				//}
				auto it = mpbody.find("img");
				if (it == mpbody.end() || it->value().empty())
				{
					msg.setMsgCode(Code_Error_NoInputImage);
					goto response;
				}
				rawimg = it->value();

				{
					it = mpbody.find("rect");
					if (it == mpbody.end())
					{
						msg.setMsgCode(Code_Error_NoInputRect);
						goto response;
					}

					std::string rectStr{ it->value() };
					rectStr = network::util::addJsonArrayName(rectStr, "rect");
					DetectInfo detectInfo;
					try
					{
						std::stringstream os(rectStr);
						cereal::JSONInputArchive archive(os);
						detectInfo.serialize(archive);
					}
					catch (std::exception& e)
					{
						std::cerr << "cereal: " << e.what() << std::endl;
						msg.setMsgCode(Code_Error_InputError);
						goto response;
					};

					if (detectInfo.rect.size() != 2)
					{
						if (it == mpbody.end())
						{
							msg.setMsgCode(Code_Error_InputError);
							goto response;
						}
					}

					try
					{
						std::vector<uchar> rawimgData(rawimg.begin(), rawimg.end());
						cv::Mat image = cv::imdecode(rawimgData, cv::ImreadModes::IMREAD_COLOR);
						int imageWidth = image.cols;
						int imageHeight = image.rows;

						float x = detectInfo.rect.front().x;
						float y = detectInfo.rect.front().y;
						float width = detectInfo.rect.back().x - detectInfo.rect.front().x;
						float height = detectInfo.rect.back().y - detectInfo.rect.front().y;

						cv::Rect rect(x * imageWidth, y * imageHeight, width * imageWidth, height * imageHeight);
						cv::Mat roiImage(image, rect);

						// 特征提取
						ModelInputData inputData;
						inputData.image = roiImage;
						FeatureOutputData output = featureAction.process(inputData);
						FeatureInfo& featureInfo = msg.vehicle;
						featureInfo.plateColor = output.plateColor;
						featureInfo.plateNum = output.plateNum;
						featureInfo.vehicleFeature = std::move(output.feature);


						// 车型信息
						auto classResult = vehicleTypeModel->infer(roiImage, false).front();
                        auto klass = vehicleTypeModel->getMaxResult(classResult, vehicleTypeThreshold);
                        if (klass.has_value())
                            featureInfo.vehicleType = vehicleTypeLabels[klass.value()];


						if (featureInfo.vehicleType != "bus")	// TODO 按算法要求  bus 颜色暂不处理
						{
							// 颜色信息
							auto classResult = vehicleColorModel->infer(roiImage , false).front();
                            auto klass = vehicleColorModel->getMaxResult(classResult, vehicleTypeThreshold);
                            if (klass.has_value())
                                featureInfo.vehicleColor = vehicleColorLabels[klass.value()];
						}

					}
					catch (cv::Exception& e)
					{
						std::cerr << "error: " << e.what() << std::endl;
						msg.setMsgCode(Code_CV_Error);
						goto response;
					}
					catch (std::exception& e)
					{
						std::cerr << "error: " << e.what() << std::endl;
						msg.setMsgCode(Code_Infer_Error);
						goto response;
					}
				}
			response:
				std::string result = "";
				{
					std::stringstream os;
					try
					{
						cereal::JSONOutputArchive archive(os, cereal::JSONOutputArchive::Options(MAX_DECIMAL_PLACES, cereal::JSONOutputArchive::Options::IndentChar::space, 0));
						msg.serialize(archive);
					}
					catch (std::exception& e)
					{
						msg.setMsgCode(Code_Inner_Error);
						std::cerr << "cereal: " << e.what() << std::endl;
					};
					result = os.str();
				}
				if (msg.code != Code_Success)
				{
					ISS_LOG_WARN("{}:{} [feature] [{}] {}", remoteIP, remotePort, msg.code, msg.msg);
				}
				else
				{
					ISS_LOG_INFO("{}:{} [feature] [{}] {} ", remoteIP, remotePort, msg.code, msg.msg);
				}

				if(result.length() > MAX_RESULT_DEBUG)
					std::cout << result.substr(0, MAX_RESULT_DEBUG) << "  ..." << std::endl;
				else
					std::cout << result << std::endl;

				rep.fill_json(result);
				rep.chunked(true);

			}, aop_check{});


		server->bind<http::verb::post>("/detect", [](std::shared_ptr<asio2::http_session>& session_ptr, http::request& req, http::response& rep)
			{
				asio2::detail::ignore_unused(req, rep);

				auto remoteIP = session_ptr->remote_address();
				auto remotePort = session_ptr->remote_port();

				DetectMsg msg;
				msg.setMsgCode(Code_Success);
				auto mpbody = req.multipart();
				//for (auto it = mpbody.begin(); it != mpbody.end(); ++it)
				//{
				//	std::cout << "filename:" << it->filename() << " name:" << it->name() << " content_type:" << it->content_type() << std::endl;
				//}
				auto it = mpbody.find("img");
				if (it == mpbody.end() || it->value().empty())
				{
					msg.setMsgCode(Code_Error_NoInputImage);
					goto response;
				}

				try
				{
					std::vector<std::vector<ObjBox>> outBoxes;
					auto& rawimg = it->value();
					std::vector<uchar> rawimgData(rawimg.begin(), rawimg.end());
					cv::Mat image = cv::imdecode(rawimgData, cv::ImreadModes::IMREAD_COLOR);
					int imageWidth = image.cols;
					int imageHeight = image.rows;

                    outBoxes = vehicleDetectModel->infer(image, true);

					auto& detectInfo = msg.data;

					for (auto& vec : outBoxes)
					{
						for (auto& o : vec)
						{
							if (o.width < MIN_OBJECT_WIDTH || o.height < MIN_OBJECT_HEIGHT)
								continue;

							//if (o.class_ >= 1 && o.class_ <= 3)
							{
								DetectInfo info;
								info.rect.emplace_back((float)o.x / imageWidth, (float)o.y / imageHeight);
								info.rect.emplace_back((float)(o.x + o.width) / imageWidth, (float)(o.y + o.height) / imageHeight);
								detectInfo.emplace_back(std::move(info));
							}
						}
					}
				}
				catch (cv::Exception& e)
				{
					std::cerr << "error: " << e.what() << std::endl;
					msg.setMsgCode(Code_CV_Error);
					goto response;
				}
				catch (const std::exception& e)
				{
					std::cerr << "error: " << e.what() << std::endl;
					msg.setMsgCode(Code_Infer_Error);
					goto response;
				}

			response:
				std::string result = "";
				{
					std::stringstream os;
					try
					{
						cereal::JSONOutputArchive archive(os, cereal::JSONOutputArchive::Options(MAX_DECIMAL_PLACES, cereal::JSONOutputArchive::Options::IndentChar::space, 0));
						msg.serialize(archive);
					}
					catch (std::exception& e)
					{
						msg.setMsgCode(Code_Inner_Error);
						std::cerr << "cereal: " << e.what() << std::endl;
					};
					result = os.str();
				}
				if (msg.code != Code_Success)
				{
					ISS_LOG_WARN("{}:{} [detect] [{}] {}", remoteIP, remotePort, msg.code, msg.msg);
				}
				else
				{
					ISS_LOG_INFO("{}:{} [detect] [{}] {}", remoteIP, remotePort, msg.code, msg.msg);
				}

				if (result.length() > MAX_RESULT_DEBUG)
					std::cout << result.substr(0, MAX_RESULT_DEBUG) << "  ..." << std::endl;
				else
					std::cout << result << std::endl;

				rep.fill_json(result);
				rep.chunked(true);

			}, aop_check{});

		// 加载模型
		initLibNvInferPlugins(nullptr, "");
		featureAction.checkInitModels();

		INI::File ini(DETECT_CFG->modelPath() + "/vehicle/config_infer.txt");
		auto modelPath = ini.GetSection("property")->GetValue("model-engine-file").AsString();
		auto absoluteModelPath = DETECT_CFG->modelPath() + "/vehicle/" + modelPath;
		std::cout << absoluteModelPath << std::endl;
		vehicleDetectModel = new DetectModel(absoluteModelPath, deviceId, 0.1f, 0.5f, 128);
        vehicleDetectModel->initResource();

		ini.Load(DETECT_CFG->modelPath() + "/secondary/config_infer_secondary_vehiclecolor.txt");
		modelPath = ini.GetSection("property")->GetValue("model-engine-file").AsString();
		vehicleColorThreshold = ini.GetSection("property")->GetValue("classifier-threshold").AsDouble();
		absoluteModelPath = DETECT_CFG->modelPath() + "/secondary/" + modelPath;
		std::cout << absoluteModelPath << std::endl;

		auto labelPath = ini.GetSection("property")->GetValue("labelfile-path").AsString();
		auto absoluteLabelPath = DETECT_CFG->modelPath() + "/secondary/" + labelPath;
		parseLabelsFile(absoluteLabelPath, vehicleColorLabels);

		auto meanfilePath = ini.GetSection("property")->GetValue("mean-file").AsString();
		auto absoluteMeanfilePath = DETECT_CFG->modelPath() + "/secondary/" + meanfilePath;

		vehicleColorModel = new ClassModel(absoluteModelPath, deviceId, true, absoluteMeanfilePath);
        vehicleColorModel->initResource();

		ini.Load(DETECT_CFG->modelPath() + "/secondary/config_infer_secondary_vehicletype.txt");
		modelPath = ini.GetSection("property")->GetValue("model-engine-file").AsString();
		vehicleTypeThreshold = ini.GetSection("property")->GetValue("classifier-threshold").AsDouble();
		absoluteModelPath = DETECT_CFG->modelPath() + "/secondary/" + modelPath;
		std::cout << absoluteModelPath << std::endl;

		labelPath = ini.GetSection("property")->GetValue("labelfile-path").AsString();
		absoluteLabelPath = DETECT_CFG->modelPath() + "/secondary/" + labelPath;
		parseLabelsFile(absoluteLabelPath, vehicleTypeLabels);

		meanfilePath = ini.GetSection("property")->GetValue("mean-file").AsString();
		absoluteMeanfilePath = DETECT_CFG->modelPath() + "/secondary/" + meanfilePath;

		vehicleTypeModel = new ClassModel(absoluteModelPath, deviceId, true, absoluteMeanfilePath);
        vehicleTypeModel->initResource();
		// 启动服务
		server->start("0.0.0.0", port);
	}

	/*
	* 解析分类标签文件
	* @param labelsFilePath 标签文件路径
	* @param labels	标签列表
	*/
	bool parseLabelsFile(const std::string& labelsFilePath, std::vector<std::string>& labels)
	{
		std::ifstream labels_file(labelsFilePath);
		std::string delim{ ';' };
		if (!labels_file.is_open())
		{
			ISS_LOG_ERROR("Could not open labels file:{}", labelsFilePath);
			return false;
		}
		while (labels_file.good() && !labels_file.eof())
		{
			std::string line, word;
			size_t pos = 0, oldpos = 0;

			std::getline(labels_file, line, '\n');
			if (line.empty())
				continue;

			while ((pos = line.find(delim, oldpos)) != std::string::npos)
			{
				word = line.substr(oldpos, pos - oldpos);
				labels.push_back(word);
				oldpos = pos + delim.length();
			}
			labels.push_back(line.substr(oldpos));
		}

		if (labels_file.bad())
		{
			ISS_LOG_ERROR("Failed to parse labels file:{}, iostate:{}", labelsFilePath, (int)labels_file.rdstate());
			return false;
		}
		return true;
	}
}
