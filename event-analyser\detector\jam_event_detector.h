/**
 * Project AI事件分析模块
 */


#ifndef _JAMEVENTDETECTOR_H
#define _JAMEVENTDETECTOR_H

#include "area_event_detector.h"

 /**
  *
  * 拥堵事件检测
  */
namespace evt
{
	// 拥堵最小速度 框移动百分比/每帧
	#define JAM_SLOW_MIN_SPEED 0.8f

	// 拥堵解除速度 框移动百分比/每帧
	#define JAM_NORMAL_MIN_SPEED 5.f

	// 拥堵 候选 通过比率
	#define JAM_CHECK_PASS_RATIO 0.1f

	// 拥堵 区域热力值 系数
	#define JAM_CHECK_HEAT_VALUE 0.3f

	// 拥堵 像素速度转车速 系数
	#define VEHICLE_SPEED_TRANS_COEFF 800.f

	class JamEventDetector : public AreaEventDetector {

	public:
		JamEventDetector(Area* area);
		/**
		 * @param type
		 */
		bool checkTargetType(TargetType type);

	private:
		/**
		 * 事件候选
		 * @param evt
		 */
		void onEventProposal(EventPtr evt) override;

		/**
		 * 事件维持
		 * @param evt
		 */
		void onEventMaintaining(EventPtr evt) override;

		/**
		 * 事件解除
		 * @param evt
		 */
		void onEventRelease(EventPtr evt) override;

		/**
		 * 区域处理开始
		 */
		void onUpdateRegionStart() override;

		EventPtr process(TargetPtr target) override;

		/**
		 * 区域处理结束
		 */
		EventPtr onUpdateRegionEnd() override;

		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;

		/**
		 * 检测解除状态
		 */
		bool checkJamRemoved();


		// 当前区域慢行数量
		int curSlowCount;

		// 当前正常行驶车辆
		int curNormalCount;

		// 当前区域所有目标数量
		int curAllCount;

		// 当前区域热力值
		float heatValue;

		// 当前区域占有率
		float occupiedValue;

		// 当前区域统计速度
		float curMeanSpeed;

		// 当前区域平均速度
		float areaMeanSpeed = 0.f;

		// 当前区域速度基数
		float curSpeedAreas;

		// 拥堵条件满足
		bool jamTestPassed;

		//----------------配置参数---------------//
		// 拥堵生效慢行数量
		int startSlowCount = 6;

		// 拥堵生效占有率
		float startOccupiedRatio = 0.3f;

		// 拥堵生效平均车速(km/h)
		int startMeanSpeed = 20;

		// 拥堵解除占有率
		float removeOccupiedRatio = 0.1f;
		
		// 拥堵解除 正常车速比率
		float removeNormalRatio = 0.5f;

		// 拥堵移除时长(秒）
		float removeTime = 300.f;

		// 参与拥堵计算车辆最小尺寸
		int slowVehicleMinSize = 30;

		//-----------------------------------------//
	};
}
#endif //_JAMEVENTDETECTOR_H