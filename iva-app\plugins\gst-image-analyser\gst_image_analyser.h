
#ifndef __GST_IMAGEANALYSER_H__
#define __GST_IMAGEANALYSER_H__

#include <gst/base/gstbasetransform.h>
#include <gst/video/video.h>
#include "opencv2/imgproc/imgproc.hpp"
#include <chrono>
#ifdef NVIDIA
#include <cuda.h>
#include <cuda_runtime.h>
#include "nvbufsurface.h"
#include "nvbufsurftransform.h"
#include "gst-nvquery.h"
#include "gstnvdsmeta.h"
#else
#include "bufsurftransform.h"
#endif // NVIDIA
#include "ivaelements.h"
#include "ivameta.h"
#ifdef ENABLE_IMAGE_ANALYSER
#include "image_analyser.h"
#endif //ENABLE_IMAGE_ANALYSER

/* Package and library details required for plugin_init */
#define PACKAGE "imageanalyser"
#define VERSION "1.0"
#define LICENSE "Proprietary"
#define DESCRIPTION "image analyser"
#define BINARY_PACKAGE "image analyser"
#define URL "http://wtoe.cn/"

G_BEGIN_DECLS
/* Standard boilerplate stuff */
typedef struct _GstImageAnalyser GstImageAnalyser;
typedef struct _GstImageAnalyserClass GstImageAnalyserClass;
/* Standard boilerplate stuff */
GType gst_imageanalyser_get_type(void);
#define GST_TYPE_IMAGEANALYSER (gst_imageanalyser_get_type())
#define GST_IMAGEANALYSER(obj) (G_TYPE_CHECK_INSTANCE_CAST((obj),GST_TYPE_IMAGEANALYSER,GstImageAnalyser))
#define GST_IMAGEANALYSER_CLASS(klass) (G_TYPE_CHECK_CLASS_CAST((klass),GST_TYPE_IMAGEANALYSER,GstImageAnalyserClass))
#define GST_IMAGEANALYSER_GET_CLASS(obj) (G_TYPE_INSTANCE_GET_CLASS((obj), GST_TYPE_IMAGEANALYSER, GstImageAnalyserClass))
#define GST_IS_IMAGEANALYSER(obj) (G_TYPE_CHECK_INSTANCE_TYPE((obj),GST_TYPE_IMAGEANALYSER))
#define GST_IS_IMAGEANALYSER_CLASS(klass) (G_TYPE_CHECK_CLASS_TYPE((klass),GST_TYPE_IMAGEANALYSER))
#define GST_IMAGEANALYSER_CAST(obj)  ((GstImageAnalyser *)(obj))
GST_DEBUG_CATEGORY_STATIC(gst_imageanalyser_debug);
#define GST_CAT_DEFAULT gst_imageanalyser_debug
static GQuark _dsmeta_quark = 0;
// Boiler plate stuff
struct _GstImageAnalyserClass
{
	GstBaseTransformClass parent_class;
};

/* Default values for properties */
#define DEFAULT_GPU_ID 0
#define FRAME_LOG_REMAIN 1000
#define FPS_FRAME_COUNT 200

/* Enum to identify properties */
enum
{
	PROP_0,
	PROP_GPU_DEVICE_ID,
	PROP_PRINT_PROCESS_LOG,
	PROP_DETECTING,
	PROP_VIDEO_ID,
	PROP_PRESET_ID,
};

#ifdef NVIDIA
#define GST_CAPS_FEATURE_MEMORY_NVMM "memory:NVMM"
#define CHECK_NVDS_MEMORY_AND_GPUID(object, surface)  \
  ({ int _errtype=0;\
   do {  \
    if ((surface->memType == NVBUF_MEM_DEFAULT || surface->memType == NVBUF_MEM_CUDA_DEVICE) && \
        (surface->gpuId != object->gpu_id))  { \
    GST_ELEMENT_ERROR (object, RESOURCE, FAILED, \
        ("Input surface gpu-id doesnt match with configured gpu-id for element," \
         " please allocate input using unified memory, or use same gpu-ids"),\
        ("surface-gpu-id=%d,%s-gpu-id=%d",surface->gpuId,GST_ELEMENT_NAME(object),\
         object->gpu_id)); \
    _errtype = 1;\
    } \
    } while(0); \
    _errtype; \
  })

#define CHECK_NPP_STATUS(npp_status,error_str) do { \
  if ((npp_status) != NPP_SUCCESS) { \
    g_print ("Error: %s in %s at line %d: NPP Error %d\n", \
        error_str, __FILE__, __LINE__, npp_status); \
    goto error; \
  } \
} while (0)

#define CHECK_CUDA_STATUS(cuda_status,error_str) do { \
  if ((cuda_status) != cudaSuccess) { \
    g_print ("Error: %s in %s at line %d (%s)\n", \
        error_str, __FILE__, __LINE__, cudaGetErrorName(cuda_status)); \
    goto error; \
  } \
} while (0)
#endif //NVIDIA

static GstStaticPadTemplate gst_imageanalyser_sink_template =
GST_STATIC_PAD_TEMPLATE ("sink",
                                GST_PAD_SINK,
                                GST_PAD_ALWAYS,
#ifdef NVIDIA
	GST_STATIC_CAPS(GST_VIDEO_CAPS_MAKE_WITH_FEATURES
	(GST_CAPS_FEATURE_MEMORY_NVMM,
		"{ RGBA }")));
#else
    GST_STATIC_CAPS ("ANY"));
#endif
                             
static GstStaticPadTemplate gst_imageanalyser_src_template =
GST_STATIC_PAD_TEMPLATE ("src",
                            GST_PAD_SRC,
                            GST_PAD_ALWAYS,
#ifdef NVIDIA
	GST_STATIC_CAPS(GST_VIDEO_CAPS_MAKE_WITH_FEATURES
	(GST_CAPS_FEATURE_MEMORY_NVMM,
		"{ RGBA }")));
#else
    GST_STATIC_CAPS ("ANY"));
#endif


using std::chrono::steady_clock;
struct _GstImageAnalyser
{
	GstBaseTransform base_trans;
#ifdef NVIDIA
	cudaStream_t npp_stream;
#endif
	BufSurface *inter_buf;
	GstVideoInfo video_info;
	guint gpu_id;

	// 通道状态
	IVAChannelState detecting = IVA_CHANNEL_PAUSED_DEFAULT;
	gint video_id;
	gint preset_id;
	gint stream_id;

	// 帧率记录
	int fps_frame_count_tmp;
	long fps_start_time_stamp;
	gboolean print_process_log;
	gint frame_log_remain;

	// 性能调试
	bool debug_perf = false;
};

/*
* 获取当前帧图像数据
* @param imageanalyser
* @param output  输出opencv Mat数据
* @param surface  NvBufSurface
* @param batch_id  batch序号
*/
static bool capture_frame(GstImageAnalyser* imageanalyser, cv::Mat& output, BufSurface* surface, int batch_id);

/*
* 记录帧率
* @param imageanalyser 
*/
static void record_fps(GstImageAnalyser* imageanalyser);

#ifdef ENABLE_IMAGE_ANALYSER
/*
* 生成图像目标 meta数据
* @param output 图像模块输出结果
* @param metalist  待生成的IVAFrameMeta的IVATargetArray对象
* @param type 检测器类型
*/
static void generate_targets_meta(ia::OutputData& output, IVATargetArray*& metalist, ia::DetectorType type);
#endif

G_END_DECLS
#endif /* __GST_IMAGEANALYSER_H__ */
