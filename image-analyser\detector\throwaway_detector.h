/**
 * Project IVA (image analyser)
 */
#pragma once
#include <atomic>
#include <vector>
#include <list>
#include "base_detector.h"

namespace ia
{
    /**
     * @brief 抛洒物检测器
     */
    class ThrowawayDetector: public ObjectDetector
    {
    public:
        ThrowawayDetector();

        /**
         * @brief 重置通道和检测状态
         */
        void reset() override;

        /**
        * @brief 事件检测
        * @param frameData 当前检测帧，包含当前帧的相关信息，比如：宽高
        */
        ImageObjectList detect(const FrameData& frameData) override;

    private:
        /**
         * @brief 获取初始化参数
         */
        void setParams();

        /**
         * @brief  生成感兴趣掩码
         * @param[in] width,height 帧图像宽高
         */
        void setInterestedMask(int width, int height);

        /**
         * @brief 根据ROI掩码过滤检测框
         * @param[in] objects 推理检测出的目标
         */
        void filterRects(ImageObjectList& objects);

        /**
         * @brief IOU匹配过滤
         * @param[in] objects 推理检测出的目标
         * @param[in] frameIndex 当前帧索引
         */
        ImageObjectList trackerFilter(ImageObjectList &objects, int frameIndex);


    private:
        int  framesNum = 5;                                 //!< 缓存帧数
        int  matchedCountAtLeast = 3;                       //!< 当前目标在历史缓存中最少出现的次数
        bool isFilterObject = true;                         //!< 是否使用IOU或跟踪过滤目标框
        bool enablePostprocess = true;                      //!< 是否打开后处理
        float ratioThreshold = 0.8;                         //!< 目标框与检测区重叠阈值
        float iouThreshold = 0.85;                          //!< iou阈值
        std::vector<cv::Mat> interestedMasks;               //!< 子区域点掩码
        //std::vector<ImageObjectList> historyObjects;        //!< 缓存N帧目标框
        std::list<std::pair<int, ImageObjectList>> historyObjects;        //!< 缓存N帧目标框



    };
}
