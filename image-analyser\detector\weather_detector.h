#pragma once
#include "list"
#include "base_detector.h"
#include "class_model.h"
#include "model_task.h"


namespace ia
{

    struct InferenceOutput
    {
        cv::Mat inputFrame;
        std::shared_ptr<void> data = nullptr;
        uint32_t size = 0;
    };


    /**
     * @brief 抛洒物检测器
     */
    class  WeatherDetector: public ClassifyDetector
    {
    public:
        WeatherDetector(bool needFrame, int deviceID, int interval, int channel) : ClassifyDetector(needFrame, interval){
            setParams();
            deviceId = deviceID;
            channelId = channel;
            parseLabelsFiles();
        }

        /**
         * @brief 重置通道和检测状态
         */
        void reset() override;

        /**
         * @brief 检测
         * @param[in] frameData 帧数据
         * @return 检测结果
         */
        ImageClass detect(const FrameData& frameData) override;

        /**
         * @brief 设置分类过滤开关
         * @param detectState
         */
        void setDetectEnable(const std::map<DetectorType, bool>& detectEnable) override;

    private:
        /**
         * @brief 获取初始化参数
         */
        void setParams();

       /**
        * @brief 推理、事件检测后处理
        * @param[in] inferOutputs 模型推理输出
        */
        void process(model_warehouse::CLASS_RESULT inferOutputs);

        bool parseLabelsFiles();

        ImageClass postprocess();
    private:
        bool enablePostprocess = true;                      //!< 是否打开后处理{}{}
        float threshold = 0.5;
        int  framesNum = 10;                                 //!< 缓存帧数
        int  matchedCountAtLeast = 7;                       //!< 当前目标在历史缓存中最少出现的次数
        std::mutex objectsLock;
        std::list<ImageClass> historyObjects;               //!< 缓存N帧目标框，每帧只输出一个阈值最大的类别
        std::vector<std::string> weatherLabels;             //!< 天气标签
        std::map<int, bool> filterMap;                    //!< 检测过滤开关
        int channelId = 0;
        int taskNum = 1;
    };
}