/**
 * Project AI事件分析模块
 */


#ifndef _EVENTIMAGECOMPARISON_H
#define _EVENTIMAGECOMPARISON_H

#include <memory>
#include "element/event.h"

/**
* 事件图像对比模块（单例）
*/
namespace evt
{
	class EventImageComparison {
	public:

		static EventImageComparison* getInstance();

		/**
		 * 模块是否开启
		 */
		bool isEnabled();

		/**
		 * 事件图像对比, 加入缓存列表
		 * @param evt
		 * @param mat
		 */
		void compareEvent(EventPtr evt, cv::Mat mat);

		/**
		 * 更新图像
		 * @param channelID
		 * @param mat
		 */
		void updateImage(int channelID, cv::Mat mat);

		/**
		 * 需要更新图像
		 */
		bool imageUpdateNeeded();
	private:

		/**
		 * 对比线程 ( 线程数量可配置)
		 */
		void onThreadCompareEvents();
	};
}
#endif //_EVENTIMAGECOMPARISON_H
