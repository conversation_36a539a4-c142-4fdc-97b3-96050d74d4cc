#pragma once
#include "class_task_base.h"
#include "config/weather_config.hpp"

/**
* 天气分类器任务
*/
namespace ia {
    using namespace model_warehouse;

    /**
    * 天气分类任务行为类
    */
    class WeatherAction : public ClassifierActionBase
    {
    public:
        explicit WeatherAction(int deviceID = 0) : ClassifierActionBase(deviceID) {};
        ~WeatherAction() override = default;

        /**
        * 检测模型初始化
        * 天气任务的初始化方式与其他分类器不同，所以重载此方法
        */
        void initModels() override;
    };

    typedef ModelTask<CLASS_RESULT> WeatherTask;
}