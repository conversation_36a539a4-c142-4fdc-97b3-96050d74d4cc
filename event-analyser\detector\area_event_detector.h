/**
 * Project AI事件分析模块
 */

#ifndef _AREAEVENTDETECTOR_H
#define _AREAEVENTDETECTOR_H

#include "target_event_detector.h"
#include "area/area.h"

 /**
  * 区域事件检测(基类)
  *
  * 主要统计区域内的整体状态情况，如拥堵等
  */
namespace evt
{
	class AreaEventDetector : public TargetEventDetector {
	public:
		AreaEventDetector(Area* area);
	protected:

		/**
		 * @param evt
		 */
		virtual void onEventProposal(EventPtr evt);

		/**
		 * @param evt
		 */
		virtual void onEventMaintaining(EventPtr evt);

		/**
		 * @param evt
		 */
		virtual void onEventRelease(EventPtr evt);

		virtual void onUpdateRegionStart();
		virtual EventPtr onUpdateRegionEnd();

		/**
		 * 所属区域
		 */
		Area* parentArea;
	};
}
#endif //_AREAEVENTDETECTOR_H
