/**
 * Project AI事件分析模块
 */


#ifndef _PEDESTRAINEVENTDETECTOR_H
#define _PEDESTRAINEVENTDETECTOR_H

#include "target_event_detector.h"

 /**
  * 行人检测器
  */
namespace evt
{

	#define PEDESTRAIN_SPEED_TRANS_COEFF 27   ///< 行人像素/框速度转为实际速度系数值 1.5 * 3600 / 1000 * 5（1.5 摩托车实长 3600/1000 秒/米 5 修正系数）

	class PedestrainEventDetector : public TargetEventDetector {
	private:

		/**
		 * 检查目标类型
		 * @param type
		 */
		bool checkTargetType(TargetType type);

        /**
         * 检查目标分类类型
         * @param target
         */
        bool checkTargetClassType(TargetPtr target);

		/**
		 * 事件分析核心逻辑
		 */
		EventPtr process(TargetPtr target) override;

		/**
		 * 事件候选
		 * @param evt
		 * @param target
		 */
		void onEventProposal(EventPtr evt, TargetPtr target) override;

		/**
		 * 事件维持
		 * @param evt
		 * @param target
		 */
		void onEventMaintaining(EventPtr evt, TargetPtr target) override;

		/**
		 * 事件解除
		 * @param evt
		 * @param target
		 */
		void onEventRelease(EventPtr evt, TargetPtr target) override;

		/**
		 * 事件目标匹配检查
		 * @param evt
		 * @param target
		 */
		bool matchCheck(EventPtr evt, TargetPtr target) override;

		/**
		 * 目标速度过滤检查 满足返回true
		 * @param target
		 * @param ignoreStill 是否过滤静态目标
		 */
		bool checkSpeed(TargetPtr target, bool ignoreStill = false);

		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;

		//----------------配置参数---------------//
		// 行人候选时间(秒)
		float proposeTime = 0.5f;

		// 行人检测通过比率
		float checkRate = 0.2f;

		// 行人事件相似检查距离间隔（像素）
		int eventSpace = 100;

		// 行人最高速度 (km / h)
		float maxSpeed = 40.f;

		// 行人最低 高宽比
		float minHWRatio = 1.4f;
		//-----------------------------------------//
	};
}
#endif //_PEDESTRAINEVENTDETECTOR_H