
#ifndef __IVA_COMMON_H__
#define __IVA_COMMON_H__

#ifdef __cplusplus
extern "C"
{
#endif

#include <gst/gst.h>

#define GST_ELEMENT_ADD_LINK_MANY(bin, group_name, element_1, element_2, ...) \
	gst_bin_add_many(GST_BIN(bin), element_2,  ## __VA_ARGS__, NULL); \
	if (!gst_element_link_many(element_1, element_2,  ## __VA_ARGS__, NULL)) \
	{	\
		g_printerr("[%s] elements could not be linked. Exiting.\n", group_name); \
		return NULL; \
	} \

#define GST_ELEMENT_LINK_MANY_CHECK(group_name, element_1, element_2, ...) \
	if (!gst_element_link_many(element_1, element_2,  ## __VA_ARGS__, NULL)) \
	{	\
		g_printerr("[%s] elements could not be linked. Exiting.\n", group_name); \
		return NULL; \
	} \

#define GST_ELEMENT_INDEX_NAME(elem_name, name, index) \
	g_snprintf(elem_name, sizeof(elem_name), name"%d", index) \

#define GST_ELEMENT_TEMP_NAME(elem_name, index) \
	(elem_name + std::to_string(index)).c_str()

#define CHECK_IF_MSG(cond, msg) \
	if (!cond) \
	{	\
		NVGSTDS_ERR_MSG_V(msg);	\
		return NULL;	\
	}	\

#define CHECK_IF_MSG1(cond, msg) \
	if (!cond) \
	{	\
		NVGSTDS_ERR_MSG_V(msg);	\
		return -1;	\
	}	\

#define GST_ELEMENT_CHECK(elem, elem_name) \
	if (!elem) \
	{	\
		NVGSTDS_ERR_MSG_V("Failed to create '%s'", elem_name);	\
		return NULL;	\
	}	\

#define GST_ELEMENT_CHECK1(elem, elem_name) \
	if (!elem) \
	{	\
		NVGSTDS_ERR_MSG_V("Failed to create '%s'", elem_name);	\
		return -1;	\
	}	\

#define GST_ELEMENT_CHECK2(elem, elem_name) \
	if (!elem) \
	{	\
		NVGSTDS_ERR_MSG_V("Failed to get '%s'", elem_name);	\
		return;	\
	}	\

#define NVGSTDS_ERR_MSG_V(msg, ...) \
    g_print("** ERROR: <%s:%d>: " msg "\n", __func__, __LINE__, ##__VA_ARGS__)

#define NVGSTDS_INFO_MSG_V(msg, ...) \
    g_print("** INFO: <%s:%d>: " msg "\n", __func__, __LINE__, ##__VA_ARGS__)

#define NVGSTDS_WARN_MSG_V(msg, ...) \
    g_print("** WARN: <%s:%d>: " msg "\n", __func__, __LINE__, ##__VA_ARGS__)

#define NVGSTDS_LINK_ELEMENT(elem1, elem2) \
    do { \
      if (!gst_element_link (elem1,elem2)) { \
        GstCaps *src_caps, *sink_caps; \
        src_caps = gst_pad_query_caps ((GstPad *) (elem1)->srcpads->data, NULL); \
        sink_caps = gst_pad_query_caps ((GstPad *) (elem2)->sinkpads->data, NULL); \
        NVGSTDS_ERR_MSG_V ("Failed to link '%s' (%s) and '%s' (%s)", \
            GST_ELEMENT_NAME (elem1), \
            gst_caps_to_string (src_caps), \
            GST_ELEMENT_NAME (elem2), \
            gst_caps_to_string (sink_caps)); \
        goto done; \
      } \
    } while (0)

#define NVGSTDS_LINK_ELEMENT_FULL(elem1, elem1_pad_name, elem2, elem2_pad_name) \
  do { \
    GstPad *elem1_pad = gst_element_get_static_pad(elem1, elem1_pad_name); \
    GstPad *elem2_pad = gst_element_get_static_pad(elem2, elem2_pad_name); \
    GstPadLinkReturn ret = gst_pad_link (elem1_pad,elem2_pad); \
    if (ret != GST_PAD_LINK_OK) { \
      gchar *n1 = gst_pad_get_name (elem1_pad); \
      gchar *n2 = gst_pad_get_name (elem2_pad); \
      NVGSTDS_ERR_MSG_V ("Failed to link '%s' and '%s': %d", \
          n1, n2, ret); \
      g_free (n1); \
      g_free (n2); \
      gst_object_unref (elem1_pad); \
      gst_object_unref (elem2_pad); \
      break; \
    } \
    gst_object_unref (elem1_pad); \
    gst_object_unref (elem2_pad); \
  } while (0)

#define NVGSTDS_BIN_ADD_GHOST_PAD_NAMED(bin, elem, pad, ghost_pad_name) \
  do { \
    GstPad *gstpad = gst_element_get_static_pad (elem, pad); \
    if (!gstpad) { \
      NVGSTDS_ERR_MSG_V ("Could not find '%s' in '%s'", pad, \
          GST_ELEMENT_NAME(elem)); \
      break; \
    } \
    gst_element_add_pad (bin, gst_ghost_pad_new (ghost_pad_name, gstpad)); \
    gst_object_unref (gstpad); \
  } while (0)

#define NVGSTDS_BIN_ADD_GHOST_PAD(bin, elem, pad) \
      NVGSTDS_BIN_ADD_GHOST_PAD_NAMED (bin, elem, pad, pad)

#define NVGSTDS_ELEM_ADD_PROBE(probe_id, elem, pad, probe_func, probe_type, probe_data) \
    do { \
      GstPad *gstpad = gst_element_get_static_pad (elem, pad); \
      if (!gstpad) { \
        NVGSTDS_ERR_MSG_V ("Could not find '%s' in '%s'", pad, \
            GST_ELEMENT_NAME(elem)); \
        break; \
      } \
      probe_id = gst_pad_add_probe(gstpad, (probe_type), probe_func, probe_data, NULL); \
      gst_object_unref (gstpad); \
    } while (0)

#define NVGSTDS_ELEM_REMOVE_PROBE(probe_id, elem, pad) \
    do { \
      if (probe_id == 0 || !elem) { \
          break; \
      } \
      GstPad *gstpad = gst_element_get_static_pad (elem, pad); \
      if (!gstpad) { \
        NVGSTDS_ERR_MSG_V ("Could not find '%s' in '%s'", pad, \
            GST_ELEMENT_NAME(elem)); \
        break; \
      } \
      gst_pad_remove_probe(gstpad, probe_id); \
      gst_object_unref (gstpad); \
    } while (0)

#define GET_FILE_PATH(path) ((path) + (((path) && strstr ((path), "file://")) ? 7 : 0))

#define REMOVE_BIN_ELEMENT(gst_bin, gst_element)	\
	do \
	{ \
		if (GST_IS_ELEMENT(gst_element)) \
		{ \
			gst_element_set_state(gst_element, GST_STATE_NULL); \
			gst_bin_remove(GST_BIN(gst_bin), gst_element); \
			gst_element = NULL; \
		} \
	} while (false)

/**
 * Function to link sink pad of an element to source pad of tee.
 *
 * @param[in] tee Tee element.
 * @param[in] sinkelem downstream element.
 *
 * @return true if link successful.
 */
inline gboolean
link_element_to_tee_src_pad (GstElement * tee, GstElement * sinkelem)
{
  gboolean ret = FALSE;
  GstPad *tee_src_pad = NULL;
  GstPad *sinkpad = NULL;
  GstPadTemplate *padtemplate = NULL;

  padtemplate = (GstPadTemplate *)
      gst_element_class_get_pad_template (GST_ELEMENT_GET_CLASS (tee),
      "src_%u");
  tee_src_pad = gst_element_request_pad (tee, padtemplate, NULL, NULL);
  if (!tee_src_pad) {
    NVGSTDS_ERR_MSG_V ("Failed to get src pad from tee");
    goto done;
  }

  sinkpad = gst_element_get_static_pad (sinkelem, "sink");
  if (!sinkpad) {
    NVGSTDS_ERR_MSG_V ("Failed to get sink pad from '%s'",
        GST_ELEMENT_NAME (sinkelem));
    goto done;
  }

  if (gst_pad_link (tee_src_pad, sinkpad) != GST_PAD_LINK_OK) {
    NVGSTDS_ERR_MSG_V ("Failed to link '%s' and '%s'", GST_ELEMENT_NAME (tee),
        GST_ELEMENT_NAME (sinkelem));
    goto done;
  }

  ret = TRUE;

done:
  if (tee_src_pad) {
    gst_object_unref (tee_src_pad);
  }
  if (sinkpad) {
    gst_object_unref (sinkpad);
  }
  return ret;
}

/**
 * Function to link source pad of an element to sink pad of muxer element.
 *
 * @param[in] streammux muxer element.
 * @param[in] elem upstream element.
 * @param[in] index pad index of muxer element.
 *
 * @return true if link successful.
 */
inline gboolean
link_element_to_streammux_sink_pad (GstElement *streammux, GstElement *elem,
                                    gint index)
{
  gboolean ret = FALSE;
  GstPad *mux_sink_pad = NULL;
  GstPad *src_pad = NULL;
  gchar pad_name[16];

  if (index >= 0) {
    g_snprintf (pad_name, 16, "sink_%u", index);
    pad_name[15] = '\0';
  } else {
    strcpy (pad_name, "sink_%u");
  }

  mux_sink_pad = gst_element_get_request_pad (streammux, pad_name);
  if (!mux_sink_pad) {
    NVGSTDS_ERR_MSG_V ("Failed to get sink pad from streammux");
    goto done;
  }

  src_pad = gst_element_get_static_pad (elem, "src");
  if (!src_pad) {
    NVGSTDS_ERR_MSG_V ("Failed to get src pad from '%s'",
                        GST_ELEMENT_NAME (elem));
    goto done;
  }

  if (gst_pad_link (src_pad, mux_sink_pad) != GST_PAD_LINK_OK) {
    NVGSTDS_ERR_MSG_V ("Failed to link '%s' and '%s'", GST_ELEMENT_NAME (streammux),
        GST_ELEMENT_NAME (elem));
    goto done;
  }

  ret = TRUE;

done:
  if (mux_sink_pad) {
    gst_object_unref (mux_sink_pad);
  }
  if (src_pad) {
    gst_object_unref (src_pad);
  }
  return ret;
}

/**
 * Function to link sink pad of an element to source pad of demux element.
 *
 * @param[in] demux demuxer element.
 * @param[in] elem downstream element.
 * @param[in] index pad index of demuxer element.
 *
 * @return true if link successful.
 */
inline gboolean
link_element_to_demux_src_pad (GstElement *demux, GstElement *elem,
                               guint index)
{
  gboolean ret = FALSE;
  GstPad *demux_src_pad = NULL;
  GstPad *sink_pad = NULL;
  gchar pad_name[16];

  g_snprintf(pad_name, 16, "src_%02d", index);
  demux_src_pad = gst_element_get_request_pad(demux, pad_name);
  NVGSTDS_LINK_ELEMENT_FULL(demux, pad_name, elem, "sink");
  //gst_object_unref(demux_src_pad);

  //g_snprintf (pad_name, 16, "src_%u", index);
  //pad_name[15] = '\0';

  //demux_src_pad = gst_element_get_request_pad (demux, pad_name);
  //if (!demux_src_pad) {
  //  NVGSTDS_ERR_MSG_V ("Failed to get sink pad from demux");
  //  goto done;
  //}

  //sink_pad = gst_element_get_static_pad (elem, "sink");
  //if (!sink_pad) {
  //  NVGSTDS_ERR_MSG_V ("Failed to get src pad from '%s'",
  //                      GST_ELEMENT_NAME (elem));
  //  goto done;
  //}

  //if (gst_pad_link (demux_src_pad, sink_pad) != GST_PAD_LINK_OK) {
  //  NVGSTDS_ERR_MSG_V ("Failed to link '%s' and '%s'", GST_ELEMENT_NAME (demux),
  //      GST_ELEMENT_NAME (elem));
  //  goto done;
  //}

  ret = TRUE;

// done:
  if (demux_src_pad) {
    gst_object_unref (demux_src_pad);
  }
  if (sink_pad) {
    gst_object_unref (sink_pad);
  }
  return ret;
}

/*
 * Function to replace string with another string.
 * Make sure @ref src is big enough to accommodate replacements.
 *
 * @param[in] str string to search in.
 * @param[in] replace string to replace.
 * @param[in] replace_with string to replace @ref replace with.
 */
inline void
str_replace (gchar * str, const gchar * replace, const gchar * replace_with)
{
  gchar tmp[1024];
  gchar *ins = tmp;
  ins[0] = '\0';
  gchar *str_orig = str;
  gchar *iter;

  if (!str || !replace || !replace_with) {
    return;
  }
  gint replace_len = strlen (replace);
  gint replace_with_len = strlen (replace_with);

  while ((iter = strstr (str, replace))) {
    gint num_char = iter - str;

    strncpy (ins, str, num_char);
    ins += num_char;

    strcpy (ins, replace_with);
    ins += replace_with_len;

    str = iter + replace_len;
  }
  strcpy (ins, str);
  strcpy (str_orig, tmp);
}

#ifdef __cplusplus
}
#endif

#endif
