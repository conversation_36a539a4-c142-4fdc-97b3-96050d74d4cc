/**
 * Project AI事件分析模块
 */

#include "drive_across_event_detector.h"
#include "util/scene_utility.h"
#include "area/area.h"

/**
 * DriveAcrossEventDetector implementation
 * 
 * 变道检测器
 */

namespace evt
{
	EventPtr DriveAcrossEventDetector::process(TargetPtr target)
	{
		CHECK_TARGET_TYPE(target);
		if (!passedRegion(target)) return NULL;

		// TODO 如果子区域和车道重叠？

		// 获取上次跨越信息
		AreaPassInfo lastPassInfo;
		if (!target->getLatestArea(lastPassInfo, 1))
			return NULL;

		if (!lastPassInfo.area->hasEventType(eventType))
			return NULL;

		// 获取本次跨越信息
		AreaPassInfo passInfo;
		target->getLatestArea(passInfo);
		if (!passInfo.area->hasEventType(eventType))
			return NULL;

		// 检查跨越后的帧数
		if (target->getTrackIndex() - passInfo.trackIndex < checkFrameCount)
			return NULL;

		// 检查跨越后的像素距离
		if(!checkIfDistanceGreaterThan(target->getLatestPos(0, true), passInfo.rect.getCenterPosition(), acrossDistance))
			return NULL;

		return proposeNew(target, EventState_Confirming, eventSpace);
	}

	/**
	 * 区域配置更新
	 */
	void DriveAcrossEventDetector::onUpdateRegionConfig()
	{
		removeTime = 60.f;

		regionConfig.getValue<float>(checkTime, DRIVE_ACROSS_CHECK_TIME);
		regionConfig.getValue<float>(removeTime, DRIVE_ACROSS_REMOVE_TIME);
		regionConfig.getValue<int>(acrossDistance, DRIVE_ACROSS_DISTANCE);
		regionConfig.getValue<int>(eventSpace, DRIVE_ACROSS_EVENT_SPACE);

		checkFrameCount = (int)(checkTime * (float)frameRate);
	}
}
