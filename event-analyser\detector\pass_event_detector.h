/**
 * Project AI事件分析模块
 */


#ifndef _PASSEVENTDETECTOR_H
#define _PASSEVENTDETECTOR_H

#include "target_event_detector.h"

/**
* PassEventDetector implementation
*
* 目标跨越区域相关事件检测（基类）
*
* 主要关心目标是否跨越区域，如变道，驶入，驶离等
*/
namespace evt
{
	class PassEventDetector : public TargetEventDetector {
	protected:

		/**
		 * 检查目标类型
		 * @param type
		 */
		bool checkTargetType(TargetType type) override;

		/**
		 * 是否跨越
		 * @param target
		 */
		bool passedRegion(const TargetPtr& target);

	};
}
#endif //_PASSEVENTDETECTOR_H