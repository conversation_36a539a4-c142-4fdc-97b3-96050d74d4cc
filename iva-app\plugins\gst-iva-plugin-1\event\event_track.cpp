#include "event_track.h"
#include <fstream>
#include "ivautils.h"
#include "ivaconfig.hpp"

constexpr int OBJECT_EVENT_RECT_NUM = 100;
/**
 * @brief 创建记录事件发生时的目标的初始位置和相关信息
 * @param[in] trackPath 跟着框存储的文件位置
 * @param[in] duration  录像总时长, s
 * @param[in] before    目标在视频中的位置, ms
 * @param[in] targetTracks 所有事件的框子信息
 * @param[in] event 目标对应的事件信息
 */
void updateTrackSaveTask(std::string trackPath, int duration, int before, TargetTracks* targetTracks, evt::EventInfo event)
{
    if (targetTracks == nullptr)
        return;

    TrackSaveTask task;
    task.eventID = event.id;
    task.eventType = event.type;
    task.trackFilePath = trackPath;
    task.startTime = event.occurTime;
    task.target = event.targetID;
    task.duration = duration;
    task.index = (int)((float)before / 1000.f * 25.f);  ///< 转换成帧位置
    task.roiID = event.roiID;
    task.area = event.occurArea;
    task.box.x = event.occurRect.getLeft() / EVT_PROCESS_WIDTH;
    task.box.y = event.occurRect.getTop() / EVT_PROCESS_HEIGHT;
    task.box.width = event.occurRect.width / EVT_PROCESS_WIDTH;
    task.box.height = event.occurRect.height/ EVT_PROCESS_HEIGHT;

    targetTracks->emplace(event.targetID, TrackSaveTaskList{task});

    /// 如果是抛洒物和烟火，则直接绘制occurArea n次
    if ((task.eventType == evt::EventType_Obstacle) || (task.eventType == evt::EventType_FireSmoke))
    {
        for (int i = 0 ; i < OBJECT_EVENT_RECT_NUM; ++i)
        {
            task.index++;
            (*targetTracks)[task.target].emplace_back(task);
        }

    }
}



void updateTrackData(TargetTracks* targetTracks, evt::TrackList& inputTracks, unsigned long bufPTS)
{
    if (targetTracks == nullptr)
        return;

    for (auto targetIt = targetTracks->begin(); targetIt != targetTracks->end();)
    {
        auto targetId = targetIt->first;
        auto &targetTrackList = targetIt->second;


        for (auto &t: inputTracks)
        {
            TrackSaveTask trackSaveTask = targetTrackList.back();
            if ((t.id == targetId) &&
            ((trackSaveTask.eventType != evt::EventType_Obstacle)
            && (trackSaveTask.eventType != evt::EventType_FireSmoke)
            && (trackSaveTask.eventType != evt::EventType_Landslide))) ///< object事件的targetId和target事件targetId不是独立的，可能重复
            {
                trackSaveTask = targetTrackList.back();
                trackSaveTask.index++;
                trackSaveTask.box.x = t.rect.getLeft() / EVT_PROCESS_WIDTH;
                trackSaveTask.box.y = t.rect.getTop() / EVT_PROCESS_HEIGHT;
                trackSaveTask.box.width = t.rect.width / EVT_PROCESS_WIDTH;
                trackSaveTask.box.height = t.rect.height/ EVT_PROCESS_HEIGHT;
                targetTrackList.emplace_back(trackSaveTask);
                break;
            }
        }

//        auto targetStartTime = targetTrackList.front().startTime;
//        auto targetDuration = targetTrackList.front().duration;
//        auto nowTime = get_system_timestamp();
//        if (nowTime - targetStartTime > targetDuration * 1000)
//        {
//            //targetIt = targetTracks->erase(targetIt);
//        }
//        else
        {
            ++targetIt;
        }

    }
}

void saveTrackDataToFile(TargetTracks* targetTrackList, int targetId, int findKeyFrameTime)
{
    if (targetTrackList == nullptr)
        return;

    int revisedFrameIndex = (int)((float)findKeyFrameTime / 1000.f * 25);

    TargetTracks targetTracks = *targetTrackList;
    if (targetTracks.find(targetId) != targetTracks.end())
    {
        auto& tracks = targetTracks[targetId];
        auto trackFilePath = tracks.front().trackFilePath;
        std::ofstream trackFile(trackFilePath, std::ios_base::app);
        for (const auto& track : tracks)
        {
            if (revisedFrameIndex > 0)
                trackFile << track.index - revisedFrameIndex - 5 << " " << track.roiID;
            else
                trackFile << track.index - 5 << " " << track.roiID;

            auto right = track.box.x + track.box.width;
            auto bottom = track.box.y + track.box.height;
            trackFile << " " << track.box.x << " " << track.box.y << " " << right << " " << bottom;
            trackFile << "\n";
        }
        targetTrackList->erase(targetId);
    }

}
