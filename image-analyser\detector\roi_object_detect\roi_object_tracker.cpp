#include "roi_object_tracker.h"

namespace roi_object_detect{

TraceObject::TraceObject(ROIObjectParam* roiObjectParam,int i, int id){
    m_id = id;
    m_rect = roiObjectParam->singleFrameRects[i];
    //m_roiImg = roiObjectParam->singleFrameRois[i];
    m_traceTimes = 1;
    m_staticFrontTimesThres = roiObjectParam->roiObjectTrackerParam->staticFrontTimesThres;
    m_maxDecreaseTimes = roiObjectParam->roiObjectTrackerParam->maxDecreaseTimes;
    m_decreaseTimes = m_maxDecreaseTimes;
    m_Alarmflag = roiObjectParam->singleFrameRectAlarmFlags[i];
    m_isUseRectFlag = roiObjectParam->roiObjectTrackerParam->isUseRectFlag;
    m_compareIouThres1 = roiObjectParam->roiObjectTrackerParam->compareIouThres1;
    m_compareIouThres2 = roiObjectParam->roiObjectTrackerParam->compareIouThres2;
    m_compareStaticThres = roiObjectParam->roiObjectTrackerParam->compareStaticThres;
}

ObjBox TraceObject::getRect(){
    return m_rect;
}

void TraceObject::compare(std::vector<ObjBox>& rects,std::vector<bool>& rectFlags){
    float iouMax = 0.0;
    int index = -1;
    for( int i = 0 ; i < rects.size(); i++){
        ObjBox rect = rects[i];
        //float iou = rectIou(m_rect,rect);
        float iou = rectIouRegion(m_rect,rect);
        if(iou > iouMax){
            iouMax = iou;
            index = i;
        }
    }
    if(iouMax >= m_compareIouThres1){
        m_traceTimes++;
        rects.erase( rects.begin() + index);
        rectFlags.erase( rectFlags.begin() + index);
    }else if((!m_isUseRectFlag && iouMax >= m_compareIouThres2) || (m_isUseRectFlag && iouMax >= m_compareIouThres2 && rectFlags[index])){
        m_Alarmflag = rectFlags[index];
        m_traceTimes = 1;
        m_rect = rects[index];
        rects.erase( rects.begin() + index);
        rectFlags.erase( rectFlags.begin() + index);
    }else{
        m_traceTimes -= 1;
    }
}

bool TraceObject::compareStatic(std::vector<ObjBox>& rects,std::vector<bool>& rectFlags){
    float iouMax = 0.0;
    int index = -1;
    for( int i = 0 ; i < rects.size(); i++){
        ObjBox rect = rects[i];
        //float iou = rectIou(m_rect,rect);
        float iou = rectIouRegion(m_rect,rect);
        if(iou > iouMax){
            iouMax = iou;
            index = i;
        }
    }
    if(iouMax > m_compareStaticThres){
        m_decreaseTimes = m_maxDecreaseTimes;
        rects.erase( rects.begin() + index);
        rectFlags.erase( rectFlags.begin() + index);
    }else{
        m_traceTimes -= 1;
    }
    if(m_decreaseTimes <= 0){
        m_destroyStatus = true;
    }
    return m_destroyStatus;
}

bool TraceObject::isFirstAlarm(){
    if(m_isFirstAlarm && m_Alarmflag){
        m_isFirstAlarm = false;
        return true;
    }
    return false;
}

bool TraceObject::judgeDelete(){
    return m_traceTimes<1;
}

bool TraceObject::judgeIsStatic(){
    return m_traceTimes>m_staticFrontTimesThres;
}

StaticObjGenerator::StaticObjGenerator(){
    reset();
}

void StaticObjGenerator::reset(){
    m_traceObjectList.clear();
    m_staticObjectList.clear();
    m_id = 0;
}

void StaticObjGenerator::appendRects(ROIObjectParam* roiObjectParam){
    int i = 0;
    //append the rects to static object
    while(i<m_staticObjectList.size()){
        bool alarmStatus = m_staticObjectList[i].compareStatic(roiObjectParam->singleFrameRects,roiObjectParam->singleFrameRectAlarmFlags);
        if(alarmStatus){
            m_staticObjectList.erase(m_staticObjectList.begin() + i);
        }else{
            i++;
        }
    }
    i = 0;
    //append the rects to other object
    while(i<m_traceObjectList.size()){
        m_traceObjectList[i].compare(roiObjectParam->singleFrameRects,roiObjectParam->singleFrameRectAlarmFlags);
        if(m_traceObjectList[i].judgeIsStatic()){
            m_staticObjectList.push_back(m_traceObjectList[i]);
            m_traceObjectList.erase(m_traceObjectList.begin() + i);
        }else if(m_traceObjectList[i].judgeDelete()){
            m_traceObjectList.erase(m_traceObjectList.begin() + i);
        }else{
            i++;
        }
    }
    //create new trace list
    for( int i = 0 ; i < roiObjectParam->singleFrameRects.size(); i++){
        if(roiObjectParam->roiObjectTrackerParam->newTraceAppendFlag || roiObjectParam->singleFrameRectAlarmFlags[i]){
            m_traceObjectList.push_back(TraceObject(roiObjectParam,i,m_id));
            m_id = (m_id+1)%1000;
        }
    }
}

void StaticObjGenerator::getStaticRegions(ROIObjectParam* roiObjectParam){
    //return the static object which matching the alarm condition
    roiObjectParam->alarmRects.clear();
    for( int i = 0 ; i < m_staticObjectList.size(); i++){
        if(m_staticObjectList[i].isFirstAlarm()){
            roiObjectParam->alarmRects.push_back(m_staticObjectList[i].getRect());
        }
    }
}

}
