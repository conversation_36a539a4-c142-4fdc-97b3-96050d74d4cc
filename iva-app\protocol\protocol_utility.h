
#pragma once
#include "event_analyser.h"
#include "protocol/all.h"

/**
 * @brief 一些协议结构体相关的数据转换接口
 */
namespace iva::protocol
{
    constexpr int UDP_START_LISTEN_PORT = 5002;
    /**
     * 坐标点 --> 向量
     * @param points 起始点, 结束点
     * @param rtSrc	 检测区
     */
    evt::Vector2D trans2Vector(const std::vector<network::Point>& points,  network::DetectArea rtSrc);

    /**
     * 坐标点 归一化
     * @param pt	 输入点
     * @param rtSrc	 检测区
     */
    evt::Point trans2Point(network::Point pt, network::DetectArea rtSrc);

    /**
    * 坐标点 --> 线
    * @param pt		点集合
    * @param rtSrc	 检测区
    */
    evt::Line trans2Line(const std::vector<network::Point>& points, network::DetectArea rtSrc);

    /**
     * 点集合 --> evt::Polygon
     * @param rect
     * @param rtSrc	 检测区
     */
    evt::Polygon trans2Polygon(const std::vector<network::Point>& points, network::DetectArea rtSrc);

    /**
     * CheckAreaItemNode (wtoesocket) --> evt::RegionInfo
     * @param node
     * @param rtSrc	 检测区
     */
    evt::RegionInfo trans2Region(const network::CheckAreaItemNode& node, network::DetectArea rtSrc);

    /**
     * vector<Param> --> map<string, string>
     * @param params
     * @param mapValue
     */
    void transParam(const std::vector<network::Param>& params, std::map<std::string, std::string>& mapValue);

    /**
     * 车道线信息转换
     * @param lane
     * @param rtSrc
     */
    evt::LaneInfo trans2Lane(const network::Lane& lane, network::DetectArea rtSrc);

    /**
    * ROI信息转换
    * @param rois
    * @param rtSrc
    */
    evt::ROIInfoList transRois(const std::vector<network::ROINode>& rois, network::DetectArea rtSrc);

    /**
    * 基于事件类型 获取检测配置的子区域（坐标点集合）
    * @param detectParam 检测参数
    * @param eventType 事件类型
    */
    std::vector<std::vector<float>> getDetectMask(const network::DetectParam& detectParam, evt::EventType eventType);

    /**
     * 基于事件类型 获取子区域的区域配置（坐标点集合）
     * @param detectParam 检测参数
     * @param eventType 事件类型
     * @return 子区域Id对应的坐标点集合 key areaId, value:子区域坐标点集合
     */
    std::map<int, std::vector<std::vector<float>>> getRoiDetectMask(const network::DetectParam& detectParam, evt::EventType eventType);

    /**
    * 获取检测状态是否需要开启
    * @param detectParam 检测参数
    * @param eventType 事件类型
    */
    bool getDetectEnabled(const network::DetectParam& detectParam, evt::EventType eventType);

    /**
     * 创建路径
     */
    bool createPath(const std::string& szPath);

    /**
     * 获取检测参数中的车道线，vector point 转成 vector float
     * @param detectParam
     */
    std::vector<std::vector<float>>  getDetectLaneLinePts(const network::DetectParam &detectParam);

    /**
    * 获取事件掩码属性是否开启
    * @param evtProperty 事件掩码
    * @param eventType 事件类型
    */
    bool isEventPropertyEnabled(int evtProperty, evt::EventType eventType);


}