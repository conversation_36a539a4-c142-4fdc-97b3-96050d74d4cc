/**
 * Project AI事件分析模块
 */


#ifndef _STOPEVENTDETECTOR_H
#define _STOPEVENTDETECTOR_H

#include "target_event_detector.h"

/**
* 停车检测器
*/
namespace evt
{
	#define STOP_MAINTAINING_CHECK_RATE 0.5f				// 停车维持检查通过比率
	#define STOP_ACCUMULATED_VELOCITY_MAX 200				// 停车累计速度（像素）最大值

	class StopEventDetector : public TargetEventDetector {
	protected:

		/**
		 * 检查目标类型
		 * @param type
		 */
		bool checkTargetType(TargetType type) override;

		/**
		 * 事件分析核心逻辑
		 */
		EventPtr process(TargetPtr target) override;

		/**
		 * 事件候选
		 * @param evt
		 * @param target
		 */
		void onEventProposal(EventPtr evt, TargetPtr target) override;

		/**
		 * 事件维持
		 * @param evt
		 * @param target
		 */
		void onEventMaintaining(EventPtr evt, TargetPtr target) override;

		/**
		 * 事件解除
		 * @param evt
		 * @param target
		 */
		void onEventRelease(EventPtr evt, TargetPtr target) override;

		/**
		 * 事件目标匹配检查
		 * @param evt
		 * @param target
		 */
		bool matchCheck(EventPtr evt, TargetPtr target) override;

		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;

	private:

		// 停车检测IOU
		float checkIOU = 0.85f;

		// 停车候选时长(秒)
		float proposeTime = 0.8f;

		// 停车检查通过比率
		float checkRate = 0.6f;

		// 停车检查间隔(像素)
		float eventSpace = 20;
	};
}
#endif //_STOPEVENTDETECTOR_H