/**
 * Project AI事件分析模块
 */

#ifndef _OBJECTEVENTDETECTOR_H
#define _OBJECTEVENTDETECTOR_H

#include "event_detector.h"
#include "element/event_object.h"

/**
* 物体事件检测器（基类）
*
* 按场景物体(图像模块检出)分类的事件类型检测器， 如抛洒物、烟火、路障等
*/
namespace evt
{

	class ObjectEventDetector : public EventDetector {
		friend class Area;
		friend class Event;
	public:
		ObjectEventDetector();

	protected:
		/**
		 * 核心判断逻辑
		 * @param target
		 */
		virtual EventPtr process(EventObject& object);

		/**
		 * 持有事件更新
		 */
		virtual void onUpdateEvent();

		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;


		/**
		 * 移除时间(秒)
		 */
		float removeTime = 60.f;
	};
}
#endif //_OBJECTEVENTDETECTOR_H