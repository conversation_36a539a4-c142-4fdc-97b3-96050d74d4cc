/**
 * Project IVA
 */

#pragma once
#include <vector>
#include "protocol/all.h"
#include "element/event_info.h"

namespace iva::protocol
{

    void initStatusManager();

    void disposeStatusManager();
    /**
      * @brief     请求初始化
      * @param[in] processID   进程号
      * @param[in] channelSize 当前进程支持的通道总个数
      */
    void sendRequestInit(int processID, int channelSize);
    void reSendRequestInit();

    /**
     * @brief 更新各种初始化消息的接收状态
     */
    void updateSystemConfigStatus();
    void updateAlgorithmParamStatus();
    void updateBasicConfigStatus();
    void updateChannelDetectStatus();
    bool isReinitRequesting();

    /**
     * @brief 事件解除通知
     * @param[in] eventId:      事件Id
     * @param[in] finishTime:   事件解除时的时间
     * @param[in] eventType:    事件类型
     * @param[in] imagePath:    事件对应的图片路径
     * @param[in] videoPath:    事件对应的视频路径
     */
    void postEventRemove(const std::string& eventId, const std::string& finishTime, int eventType, const std::string& imagePath, const std::string& videoPath = "");

    /**
     * @brief 事件撤回通知
     * @param[in] eventId:       事件Id
     * @param[in] eventType:     事件类型
     * @param[in] occurTime:     事件发生时的时间
     */
    void postEventWithdraw(const std::string& eventId, int eventType, const std::string& occurTime);

    /**
     * @brief 视频质量告警
     * @param[in] videoId:       视频源Id
     * @param[in] alarmTypes:    告警类型集
     * @param[in] isPauseDetect: 是否暂停检测
     */
    void postVideoQuaAlarm(int videoId, const std::vector<int>& alarmTypes, int isPauseDetect);

    /**
     * @brief 视频质量恢复
     * @param[in] videoId:       视频源Id
     * @param[in] presetId:      预置位Id
     * @param[in] restoreTypes:  视频质量恢复类型集
     */
    void postVideoQuaRecovery(int videoId, int presetId, const std::vector<int>& restoreTypes);

    /**
     * @brief 通道检测状态上报
     * @param[in] index:         管道序号
     * @param[in] videoId:       视频源Id
     * @param[in] isDetect:      是否处于检测状态
     */
    void postDetectStatus(int index, int videoId, int isDetect);

    /**
     * @brief 请求日夜切换
     * @param[in] index:         管道序号
     * @param[in] videoId:       视频源Id
     * @param[in] isDay:         请求日或夜的时间切换方案
     */
    void postDayNight(int index, int videoId, int isDay);

    /**
     * @brief 事件上报
     */
    bool postEvent(int index, const std::string& videoPath, const std::string& imgPath,
                   network::AlarmType alarmType, const network::Area_t& objectArea, const evt::EventInfo& eventInfo);

    bool postTrack(std::vector<network::Trackinfo>& tracks );
    void postVehicleInfo(const std::vector<network::VehicleInfo>& vehicleInfo);

    /**
     * @brief 上报事件录像完成(IVA录像)消息
     */
    bool postEventVideoFinished(std::string videoWebUrl);
}
