#pragma once
#include <string>
#include "ini/inibase.h"
#include "version.h" // Generated by CMAKE

static std::string getVersion() {
	auto tt = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
	struct tm *ptm = localtime(&tt);
	int iYear = ptm->tm_year + 1900;

	char version[128];
	sprintf(version, "%s Copyright © 2016 - %d  WELLTRANS O&E CO., All Rights Reserved.", IVA_VERSION, iYear);
	return version;
}

#if CAMBRICON
    #define DEFAULT_INTERVAL 1
    #define FIRE_SMOKE_INTERVAL 25
    #define ROADBLOCK_INTERVAL 125
    #define ROI_OBJECT_INTERVAL 125
    #define LAN_INTERVAL 200
#elif NVIDIA
	#define DEFAULT_INTERVAL 1
    #define FIRE_SMOKE_INTERVAL 1
    #define ROADBLOCK_INTERVAL 1
    #define ROI_OBJECT_INTERVAL 1
    #define LAN_INTERVAL 30
#else
    #define DEFAULT_INTERVAL 1
    #define FIRE_SMOKE_INTERVAL 45
    #define ROADBLOCK_INTERVAL 125
    #define ROI_OBJECT_INTERVAL 125
    #define LAN_INTERVAL 200
#endif // !CAMBRICON

/*
 * IVA 配置 (INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(iva, "config/iva.ini",
(int,	maxSizeBuffers,			pipe,   200,	u8"管线buffer的最大尺寸")
(int,	batchTimeout,			pipe,   33000,  u8"批处理超时")
(int,	udpBufferSize,			pipe,   0,		u8"UDP接收缓冲区字节大小")
//(bool,	enableImageAnalyser,	option, true,	u8"是否开启图像诊断模块")
(bool,	usingCustomOsd,			option, false,	u8"是否使用定制化的事件截图")
//(bool,	enableFvmOffsetFilter,  option, true,	u8"是否使用fvm偏移事件过滤")
(bool,	usingPersonModel,		option, false,	u8"是否使用多模型（区分行人）")
(bool,	reportVehicleInfo,		option, false,	u8"是否上报车辆信息")
#ifdef USE_NEW_STREAMMUX
(int, videoWidth, video, 1920, u8"输出视频-宽") 
(int, videoHeight, video, 1080, u8"输出视频-高")
#else
(int, videoWidth, video, 640, u8"输出视频-宽") //744
(int, videoHeight, video, 384, u8"输出视频-高") //416
#endif // !USE_NEW_STREAMMUX
(int, framerate, video, 0, u8"输出视频-帧率")
(int, bitrate, video, 2800, u8"输出视频-码率kbps")
(int,   interpolation,			video,  3,		u8"插值方法")
(bool,  drawTrackLine,			osd,	true,	u8"是否绘制拖尾")
(bool,  drawSpeed,				osd,	false,	u8"是否绘制车速")
(bool,  drawClassType,			osd,	false,	u8"是否绘制二分类类型")
(bool,  drawType,				osd,	false,	u8"是否绘制目标类型")
(bool,  drawTargetID,			osd,	false,	u8"是否绘制目标ID")
(bool,  drawEventROI,			osd,	false,	u8"是否绘制事件ROI")
(bool,  drawConstruct,			osd,	false,	u8"是否绘制施工关联目标")
(bool,  drawAllPedstrain,		osd,	false,	u8"是否绘制所有行人")
(bool,  drawImageEvent,			osd,	true,	u8"是否实时绘制图像事件")
//(int,   trackerType,			tracker, 1,		u8"跟踪类型 |0 KF&munkres 1 IOU 2 KLT")
(int,   vehicleInferInterval,	infer, DEFAULT_INTERVAL,u8"推理抽帧(车辆)")
(int,   nonVehicleInferInterval,infer,	 5,		u8"推理抽帧(非车辆)")
(int,	firesmokeInterval,		infer,	 FIRE_SMOKE_INTERVAL,	u8"推理抽帧(烟火)")
(int,	roadblockInterval,		infer,	 ROADBLOCK_INTERVAL,	u8"推理抽帧(路障)")
(int,	laneInterval,			infer,	 LAN_INTERVAL,	u8"推理抽帧(车道线)")
(int,	roiobjectInterval,		infer,	 ROI_OBJECT_INTERVAL,	u8"推理抽帧(抛洒物)")
(int,   landslideInterval, 		infer,	 25*10,	u8"推理抽帧(塌方)")
(string, fontPath,	path, "/data/opt/fonts/simhei.ttf", u8"中文字体目录路径")
(string, modelPath,	path, "/data/opt/models",			u8"模型目录路径")
#ifdef NVIDIA
(bool,   recordVideo,           record,  true,   u8"是否录制视频")
(int,    recordDuration,        record,	  60,	 u8"文件录制时长")
(int,   imageQuality,           search,   70,	 u8"图像存储的质量0-100")
(bool,  drawRects,              search,  false,  u8"截图是否绘制目标框")
#endif

(bool,  usingThrowawayRoadblockModel, infer,true,u8"是否使用抛洒物锥桶集成模型")
(string, resourceIP, ip, "", u8"资源IP(外网映射用)")
(int, maxEncodeCount, video, 4, u8"结构化视频最大输出数量")
(bool, detectVehicleTypeOnStop, infer, false, u8"停车事件时检测车辆类型")
(bool, detectVehicleColorOnStop, infer, false, u8"停车事件时检测车辆颜色")
(bool, detectVehicleTypeOnOpposite, infer, false, u8"逆行事件时检测车辆类型")
(bool, detectVehicleColorOnOpposite, infer, false, u8"逆行事件时检测车辆颜色")
#ifdef NVIDIA
(bool, detectPersonClothesOnPedstrain, infer, false, u8"行人事件时检测行人衣物")
#else
(bool, detectPersonClothesOnPedstrain, infer, true, u8"行人事件时检测行人衣物")
#endif
)

#define SETTINGS iva::Config::instance()
