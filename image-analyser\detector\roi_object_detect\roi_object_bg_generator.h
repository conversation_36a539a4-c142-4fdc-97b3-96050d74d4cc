#pragma once

#include "roi_object_parms.h"
#include "util/algorithm_util.h"

#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

using namespace algorithm_util;

namespace roi_object_detect{

    class BgImgGenerator{
        public:
            BgImgGenerator(FeatureModelParam& featureModelParam,BgImgGeneratorParam& bgImgGeneratorParam);
            ~BgImgGenerator();

            //reset params
            void reset();

            //update background
            void update(ROIObjectParam* roiObjectParam);

            // calculate difference map  based on the feature vectors of two background images,
            // get rects froom difference map
            void setTowBgRects(ROIObjectParam* roiObjectParam);

            // detect the new background image. 
            // if there is a foreground static object in an area , erase the area and update the area with the old background image .
            void updateGridForNewBg(ROIObjectParam* roiObjectParam);

            //padding unfilled bgImg, return img
            cv::Mat getBgPad(cv::Mat img);

            //return new bgImg
            cv::Mat getNewBg();

            // Whether the filling rate meets the use standard 
            bool isFilledEnough();

            // has the background changed
            bool isBgChanged();

        private:
            //judge the bg is change or not.
            void changeBg();

            // reset params
            void resetParams();

            //padding the bgImg
            cv::Mat pad(cv::Mat img);

        private:
            int m_gridW; // w of the grid map
            int m_gridH; // h of the grid map
            int m_falseNums; //the number of grids which not be filled
            int m_ratio; // filling rate

            float m_motionThres; //  threshold used to distinguish whether it is a moving object
            float m_bgFrontThres; // threshold used to distinguish whether it is front or bg
            float m_unFillRatio; //  unfilling rate
            float m_unFillRatioThres; //  threshold used to judge whether the unfilling rate meets the standard

            bool m_isBgChanged; // has the background changed
            bool m_isBgEmpty = true;
            int m_isNewBgFinished; // has the new background been completed

            std::vector<bool> m_bgFlags; // flag used to record whether each gird is filled or not
            std::vector<ObjBox> m_towBgRects; // box generated from the featrue different map between old and new background images 

            cv::Mat m_bg; // background img being generated 
            cv::Mat m_bgFinal; //  background img is be used
            cv::Mat m_bgPad; // background img from padding the unfilled background image 
            cv::Mat m_bgMask; // background mask  Used to distinguish between filled pixels and unfilled pixels

    };
}