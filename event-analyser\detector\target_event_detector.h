/**
 * Project AI事件分析模块
 */


#ifndef _TARGETEVENTDETECTOR_H
#define _TARGETEVENTDETECTOR_H

#include "event_detector.h"
#include "element/event_object.h"
/**
* 目标事件检测器（基类）
*
* 按单个目标分类的事件类型检测器，如停车，逆行等
*/
namespace evt
{
	// 检查目标类型
	#define CHECK_TARGET_TYPE(target) \
		if(!checkTargetType(target->getType())) return NULL; \

	// 检查目标帧数
	#define CHECK_TARGET_FRAME_COUNT(target) \
		if(target->getTrackCount() < checkFrameCount) return NULL; \

	class TargetEventDetector : public EventDetector {
		friend class Area;
	public:
		TargetEventDetector();
	protected:
		/**
		 * 事件范围匹配标记检查
		 * @param target
		 */
		virtual bool eventMatchSpace(TargetPtr target);

		/**
		 * 检查目标类型
		 * @param type
		 */
		virtual bool checkTargetType(TargetType type);

		/**
		 * 核心判断逻辑
		 * @param target
		 */
		virtual EventPtr process(TargetPtr target);

		/**
		 * 处理图像目标
		 * @param object
		 */
		virtual void process(EventObject object);

		/**
		 * 事件状态检查
		 * @param event
		 * @param target
		 */
		virtual void onUpdateEvent(EventPtr event, TargetPtr target);

		/**
		 * 事件候选
		 * @param evt
		 * @param target
		 */
		virtual void onEventProposal(EventPtr evt, TargetPtr target);

		/**
		 * 事件维持
		 * @param evt
		 * @param target
		 */
		virtual void onEventMaintaining(EventPtr evt, TargetPtr target);

		/**
		 * 事件解除
		 * @param evt
		 * @param target
		 */
		virtual void onEventRelease(EventPtr evt, TargetPtr target);

		/**
		 * 事件目标匹配检查
		 * @param evt
		 * @param target
		 */
		virtual bool matchCheck(EventPtr evt, TargetPtr target);

		/**
		 * 目标检查帧数
		 */
		int checkFrameCount = 1;

		/**
		 * 移除时间(秒)
		 */
		float removeTime = 60.f;

		// 检测时长(秒)
		float checkTime = 5.f;

	};
}
#endif //_TARGETEVENTDETECTOR_H
