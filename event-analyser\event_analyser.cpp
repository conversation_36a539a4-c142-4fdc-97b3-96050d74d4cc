/**
 * Project AI事件分析模块
 */

#include "event_analyser.h"
#include <map>

/**
 * EventAnalyser implementation
 * 
 * 事件分析模块
 */
namespace evt
{
	/**
	 * 当前所有通道
	 * map <通道ID， 通道对象>
	 */
	map<int, Channel*> channels;
	std::mutex channelMutex;

	/**
	* 初始化通道配置
	* @param channelID 通道ID
	* @param cfgs 感兴趣区配置
	* @param evtWidth,evtHight 事件分析处理宽高
	* @param presetID 预置位ID
	* @param planLevel 参数预案等级
	*/
	Channel* initChannel(int channelID, ROIInfoList& cfgs, int evtWidth, int evtHight, int presetID, int planLevel) {
		channelMutex.lock();
		if (channels.find(channelID) == channels.end())
		{
			auto channel = new Channel(channelID);
			channels[channelID] = channel;
		}
		channelMutex.unlock();
		channels[channelID]->init(cfgs, evtWidth, evtHight, presetID, planLevel);
		return channels[channelID];
	}

	/**
	 * 获取通道
	 * @param channelID
	 * @return Channel*
	 */
	Channel* getChannel(int channelID) {

		std::lock_guard<std::mutex> m(channelMutex);
		if (channels.find(channelID) == channels.end())
		{
			auto channel = new Channel(channelID);
			channels[channelID] = channel;
		}
		return channels[channelID];
	}

	/**
	 * 释放通道
	 * @param channelID 通道ID  (-1 清除所有)
	 */
	void releaseChannel(int channelID)
	{
		std::lock_guard<std::mutex> m(channelMutex);
		if (channelID == -1)
		{
			for (auto& channel : channels)
			{
				delete channel.second;
			}
			channels.clear();
		}
		else
		{
			auto it = channels.find(channelID);
			if (it != channels.end())
			{
				delete it->second;
				channels.erase(it);
			}
		}
	}
}