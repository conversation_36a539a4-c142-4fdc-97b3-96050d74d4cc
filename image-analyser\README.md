
## AI图像分析模块(image-analyser)

### 主要接口
-----------------------------
`include/image_analyser.h`

|接口名称 | 接口描述|
|  :----  | :----  |
| Channel* getChannel(int channel)  | 获取通道 |
| void releaseChannel(int channelID = -1)  | 释放通道 |

-----------------------------
`include/channel.h`

|接口名称 | 接口描述|
|  :----  | :----  |
| OutputData process(const FrameData& frameData)  | 核心检测流程接口 |
| void setEnable(DetectorType type, bool enable)  | 设置检测器开关 |
| bool getEnable(DetectorType type)  | 获取检测器开关 |
| void setCheckAreaMask(const RegionPositions& pts, DetectorType detectType)  | 设置检测器 子区域掩码 |
| bool needFrameMat()  | 当前帧是否需要画面帧数据 |
| void reset() | 重置通道 |


示例代码 (详情参考gst-image-analyser):
```c++

auto image_channel = ia::getChannel(stream_id);

ia::FrameData frame_data;
frame_data.width = video_width;
frame_data.height = video_height;
frame_data.frameIndex = ds_frame_meta->frame_num;

ia::OutputData output = image_channel->process(frame_data);

```
