/**
 * Project AI事件分析模块
 */

#ifndef _EVENTANALYSER_H
#define _EVENTANALYSER_H

#include "channel.h"
#include "module_def.h"

  /**
   * 事件分析模块
   */
namespace evt
{
	/**
	 * 初始化通道配置
	 * @param channelID 通道ID
	 * @param cfgs 感兴趣区配置
	 * @param evtWidth,evtHight 事件分析处理宽高
	 * @param presetID 预置位ID
	 * @param planLevel 参数预案等级
	 */
	EVT_EXPORT Channel* initChannel(int channelID, ROIInfoList& cfgs, int evtWidth, int evtHight, int presetID= 0, int planLevel=1);

	/**
	 * 获取通道
	 * @param channelID 通道ID
	 * @param presetID 预置位ID
	 */
	EVT_EXPORT Channel* getChannel(int channelID);

	/**
	 * 释放通道
	 * @param channelID 通道ID  (-1 清除所有)
	 */
	EVT_EXPORT void releaseChannel(int channelID= -1);
}
#endif //_EVENTANALYSER_H