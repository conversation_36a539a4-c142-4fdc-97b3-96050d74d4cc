
#include <string>
#include <iostream>
#include <experimental/filesystem>
#include <vector>
#include <cassert>
#include <future>
#include <opencv2/opencv.hpp>
#include "gstivaplugin1.h"
#include "log.h"
#include "ivautils.h"
#include "ivaconfig.hpp"
#include "event/event_proc.h"
#include "protocol_sender.h"
#include "protocol_manager.h"
#include "enum/target_type.h"
#include "protocol_utility.h"

#ifdef ENABLE_IMAGE_ANALYSER
#include "image_analyser.h"
#endif // ENABLE_IMAGE_ANALYSER

#ifdef NVIDIA
#include <npp.h>
#include "record/event_video_record.h"
#include "record/video_record.h"
#endif // NVIDIA


namespace fs = std::experimental::filesystem;

/* Define our element type. Standard GObject/GStreamer boilerplate stuff */
#define gst_ivaplugin1_parent_class parent_class
G_DEFINE_TYPE(GstIvaPlugin1, gst_ivaplugin1, GST_TYPE_BASE_TRANSFORM);

static void gst_ivaplugin1_set_property(GObject * object, guint prop_id, const GValue * value, GParamSpec * pspec);
static void gst_ivaplugin1_get_property(GObject * object, guint prop_id, GValue * value, GParamSpec * pspec);
static gboolean gst_ivaplugin1_set_caps(GstBaseTransform * btrans, GstCaps * incaps, GstCaps * outcaps);
static gboolean gst_ivaplugin1_start(GstBaseTransform * btrans);
static gboolean gst_ivaplugin1_stop(GstBaseTransform * btrans);
static GstFlowReturn gst_ivaplugin1_transform_ip(GstBaseTransform *btrans, GstBuffer * inbuf);
#ifdef NVIDIA
static std::pair<int, std::string> get_classifier_class_id(NvDsClassifierMeta * classifierMeta);
static std::map<std::string, std::string> get_classifier_class_types(NvDsClassifierMetaList* classifierMetaList);
static bool initClassifierTable();
#endif//NVIDIA
#ifdef ENABLE_TRACK_FEATURE
static void retrieveFeature(float scaleX,float scaleY, const std::string& fileName, int position, record::PassedTargetInfo& targetInfo, std::string imgPath);
static std::string writeImage(guint channelId, cv::Mat frame, gint frameNum, GstIvaPlugin1 *ivaplugin1,evt::TargetInfoList& targets);
static void writeImageTask(GstIvaPlugin1 *ivaPlugin1);
#endif //ENABLE_TRACK_FEATURE
/**
 * classifierId与classifierType映射表
 * @note 由于deepstream6.0.1当前版本不能从config文件读取classifierType，所以暂时通过映射的方式处理
 */
static std::map<int, std::string> classifierTable;

/* Install properties, set sink and src pad capabilities, override the required
 * functions of the base class, These are common to all instances of the
 * element.
 */
static void gst_ivaplugin1_class_init(GstIvaPlugin1Class * klass)
{
	GObjectClass *gobject_class;
	GstElementClass *gstelement_class;
	GstBaseTransformClass *gstbasetransform_class;

	gobject_class = (GObjectClass *)klass;
	gstelement_class = (GstElementClass *)klass;
	gstbasetransform_class = (GstBaseTransformClass *)klass;

	/* Overide base class functions */
	gobject_class->set_property = GST_DEBUG_FUNCPTR(gst_ivaplugin1_set_property);
	gobject_class->get_property = GST_DEBUG_FUNCPTR(gst_ivaplugin1_get_property);
	gstbasetransform_class->set_caps = GST_DEBUG_FUNCPTR(gst_ivaplugin1_set_caps);
	gstbasetransform_class->start = GST_DEBUG_FUNCPTR(gst_ivaplugin1_start);
	gstbasetransform_class->stop = GST_DEBUG_FUNCPTR(gst_ivaplugin1_stop);
	gstbasetransform_class->transform_ip = GST_DEBUG_FUNCPTR(gst_ivaplugin1_transform_ip);

	/* Install properties */
	g_object_class_install_property(gobject_class, PROP_GPU_DEVICE_ID,
		g_param_spec_uint("gpu-id", "GPU device ID", "GPU device ID", 0, G_MAXUINT, 0,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	g_object_class_install_property(gobject_class, PROP_PRINT_PROCESS_LOG,
		g_param_spec_boolean("print-process-log", "Print log enabled", "Print log enabled", true,
		(GParamFlags)(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS)));

	g_object_class_install_property(gobject_class, PROP_DETECTING,
		g_param_spec_int("detecting", "Channel detecting", "Channel detecting", 0, G_MAXINT, 0,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	g_object_class_install_property(gobject_class, PROP_VIDEO_ID,
		g_param_spec_int("video-id", "Video ID in current channel", "Video ID in current channel", 0, G_MAXINT, 0,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	g_object_class_install_property(gobject_class, PROP_PRESET_ID,
		g_param_spec_int("preset-id", "Preset ID in current channel", "Preset ID in current channel", 0, G_MAXINT, 0,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	g_object_class_install_property(gobject_class, PROP_AUTORESET_TIME,
		g_param_spec_uint("autoResetTime", "Auto reset time", "Auto reset time", 0, G_MAXUINT, 0,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	g_object_class_install_property(gobject_class, PROP_IMG_ANALYSER_ENABLED,
		g_param_spec_boolean("img-analyser-enabled", "Image analyser enabled", "Image analyser enabled", false,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

	g_object_class_install_property(gobject_class, PROP_DRAW_FRAME,
		g_param_spec_boolean("draw-frame", "draw frame rects enabled", "draw frame rects enabled", false,
			GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));

    g_object_class_install_property(gobject_class, PROP_SNAPSHOT_REQUEST,
        g_param_spec_boolean("snapshot-request", "Snapshot request", "Snapshot request", false,
            GParamFlags(G_PARAM_READWRITE | G_PARAM_STATIC_STRINGS | GST_PARAM_MUTABLE_READY)));
	/* Set sink and src pad capabilities */
	gst_element_class_add_pad_template(gstelement_class, gst_static_pad_template_get(&gst_ivaplugin1_src_template));
	gst_element_class_add_pad_template(gstelement_class, gst_static_pad_template_get(&gst_ivaplugin1_sink_template));
	gst_element_class_set_details_simple(gstelement_class, "IVA plugin 1", "IVA Plugin 1", "IVA Plugin 1", "AI team @ wtoe@ https://wtoe.cn");
}

static void gst_ivaplugin1_init(GstIvaPlugin1 * ivaplugin1)
{
	GstBaseTransform *btrans = GST_BASE_TRANSFORM(ivaplugin1);
	gst_base_transform_set_in_place(GST_BASE_TRANSFORM(btrans), TRUE);
	gst_base_transform_set_passthrough(GST_BASE_TRANSFORM(btrans), TRUE);
	/* This quark is required to identify NvDsMeta when iterating through the buffer metadatas */
#ifdef NVIDIA
	if (!_dsmeta_quark)
		_dsmeta_quark = g_quark_from_static_string(NVDS_META_STRING);
#endif
	/* Initialize all property variables to default values */
	ivaplugin1->gpu_id = DEFAULT_GPU_ID;
	ivaplugin1->print_process_log = TRUE;
	ivaplugin1->frame_log_remain = FRAME_LOG_REMAIN;
	ivaplugin1->status_inited = FALSE;
	ivaplugin1->init_status = FALSE;

	ivaplugin1->video_id = -1;
	ivaplugin1->preset_id = -1;
	ivaplugin1->detecting = IVA_CHANNEL_PAUSED_DEFAULT;
	ivaplugin1->detecting_change_time = steady_clock::now();
	ivaplugin1->last_frame_state.cam_resetable = false;
	ivaplugin1->auto_reset_time = 0;
	ivaplugin1->img_analyser_enabled = false;
	ivaplugin1->event_channel = NULL;
	ivaplugin1->events = NULL;
	ivaplugin1->draw_frame = false;
	ivaplugin1->fps_frame_count_tmp = 0;
	ivaplugin1->fps_start_time_stamp = 0;
#ifdef NVIDIA // 二分类 支持
    assert(initClassifierTable());
#endif //NVIDIA
}

/* Function called when a property of the element is set. Standard boilerplate.
 */
static void gst_ivaplugin1_set_property(GObject * object, guint prop_id, const GValue * value, GParamSpec * pspec)
{
	GstIvaPlugin1 *ivaplugin1 = GST_IVAPLUGIN1(object);
	switch (prop_id) {
	case PROP_GPU_DEVICE_ID:
		ivaplugin1->gpu_id = g_value_get_uint(value);
		break;
	case PROP_PRINT_PROCESS_LOG:
		ivaplugin1->print_process_log = g_value_get_boolean(value);
		break;
	case PROP_VIDEO_ID:
		ivaplugin1->video_id = g_value_get_int(value);
		break;
	case PROP_PRESET_ID:
		ivaplugin1->preset_id = g_value_get_int(value);
		break;
	case PROP_DETECTING:
	{
		ivaplugin1->init_status = TRUE;
		IVAChannelState detecting = (IVAChannelState)g_value_get_int(value);
		if (ivaplugin1->detecting != detecting)
		{
			ivaplugin1->detecting_change_time = steady_clock::now();
			ivaplugin1->last_frame_state.cam_resetable = false;
		}
		ivaplugin1->detecting = detecting;

		if (ivaplugin1->detecting != IVA_CHANNEL_PAUSED_DEFAULT)
			ivaplugin1->status_inited = FALSE;
		if (ivaplugin1->detecting == IVA_CHANNEL_RESTORED)
			ivaplugin1->last_frame_state = FrameState();
	}
	break;
	case PROP_AUTORESET_TIME:
		ivaplugin1->auto_reset_time = g_value_get_uint(value);
		break;
	case PROP_IMG_ANALYSER_ENABLED:
		ivaplugin1->img_analyser_enabled = g_value_get_boolean(value);
		break;
	case PROP_DRAW_FRAME:
	{
		bool draw_frame = g_value_get_boolean(value);
		if (ivaplugin1->draw_frame != draw_frame)
		{
			ivaplugin1->draw_state_time = steady_clock::now();
		}
		ivaplugin1->draw_frame = draw_frame;
		break;
	}
    case PROP_SNAPSHOT_REQUEST:
    {
        bool snapshot_request = g_value_get_boolean(value);
        ivaplugin1->snapshot_request = snapshot_request;
        break;
    }
	default:
		G_OBJECT_WARN_INVALID_PROPERTY_ID(object, prop_id, pspec);
		break;
	}
}

/* Function called when a property of the element is requested. Standard
 * boilerplate.
 */
static void gst_ivaplugin1_get_property(GObject * object, guint prop_id, GValue * value, GParamSpec * pspec)
{
	GstIvaPlugin1 *ivaplugin1 = GST_IVAPLUGIN1(object);
	switch (prop_id) {
	case PROP_GPU_DEVICE_ID:
		g_value_set_uint(value, ivaplugin1->gpu_id);
		break;
	case PROP_PRINT_PROCESS_LOG:
		g_value_set_boolean(value, ivaplugin1->print_process_log);
		break;
	case PROP_VIDEO_ID:
		g_value_set_int(value, ivaplugin1->video_id);
		break;
	case PROP_PRESET_ID:
		g_value_set_int(value, ivaplugin1->preset_id);
		break;
	case PROP_DETECTING:
		g_value_set_int(value, ivaplugin1->detecting);
		break;
	case PROP_AUTORESET_TIME:
		g_value_set_uint(value, ivaplugin1->auto_reset_time);
		break;
	case PROP_IMG_ANALYSER_ENABLED:
		g_value_set_boolean(value, ivaplugin1->img_analyser_enabled);
		break;
	case PROP_DRAW_FRAME:
		g_value_set_boolean(value, ivaplugin1->draw_frame);
		break;
    case PROP_SNAPSHOT_REQUEST:
        g_value_set_boolean(value, ivaplugin1->snapshot_request);
        break;
	default:
		G_OBJECT_WARN_INVALID_PROPERTY_ID(object, prop_id, pspec);
		break;
	}
}

/**
 * Initialize all resources and start the output thread
 */
static gboolean gst_ivaplugin1_start(GstBaseTransform * btrans)
{
	GstIvaPlugin1 *ivaplugin1 = GST_IVAPLUGIN1(btrans);
#ifdef NVIDIA
	CHECK_CUDA_STATUS(cudaSetDevice(ivaplugin1->gpu_id),
		"Unable to set cuda device");
	CHECK_CUDA_STATUS(cudaStreamCreate(&ivaplugin1->npp_stream),
		"Could not create cuda stream");
#endif // NVIDIA

	ivaplugin1->nvosd_ctx = ai_osd_create_context();
	if (!ivaplugin1->nvosd_ctx)
		goto error;
	return TRUE;
error:

	return FALSE;
}

/**
 * Stop the output thread and free up all the resources
 */
static gboolean gst_ivaplugin1_stop(GstBaseTransform * btrans)
{
	GstIvaPlugin1 *ivaplugin1 = GST_IVAPLUGIN1(btrans);
#ifdef NVIDIA
	if (ivaplugin1->inter_buf)
		NvBufSurfaceDestroy(ivaplugin1->inter_buf);
	ivaplugin1->inter_buf = NULL;
	if (ivaplugin1->npp_stream)
	{
		cudaStreamDestroy(ivaplugin1->npp_stream);
		ivaplugin1->npp_stream = NULL;
	}
#endif // NVIDIA
	if (ivaplugin1->nvosd_ctx)
		ai_osd_destroy_context(ivaplugin1->nvosd_ctx);

	ivaplugin1->osdParam.destroy();
	GST_DEBUG_OBJECT(ivaplugin1, "deleted CV Mat \n");
	if (ivaplugin1->events != NULL)
	{
		delete ivaplugin1->events;
	}
#ifdef NVIDIA
	if (ivaplugin1->targetSaveTracks != NULL)
	{
		delete ivaplugin1->targetSaveTracks;
	}

    if (ivaplugin1->videoRecordPtr != nullptr)
    {
        ivaplugin1->writingImage = false;
        if (ivaplugin1->writeImageThread.joinable())
            ivaplugin1->writeImageThread.join();

        ivaplugin1->videoRecordPtr->stop();
        ivaplugin1->videoRecordPtr->destroy();
        ivaplugin1->videoRecordPtr.reset();
    }
#endif

	return TRUE;
}

/**
 * Called when source / sink pad capabilities have been negotiated.
 */
static gboolean gst_ivaplugin1_set_caps(GstBaseTransform * btrans, GstCaps * incaps,
	GstCaps * outcaps)
{
	GstIvaPlugin1 *ivaplugin1 = GST_IVAPLUGIN1(btrans);
	/* Save the input video information, since this will be required later. */
	gst_video_info_from_caps(&ivaplugin1->video_info, incaps);

#ifndef NVIDIA
	devSetDevId(ivaplugin1->gpu_id);
#endif // !NVIDIA

#ifdef NVIDIA
	CHECK_CUDA_STATUS(cudaSetDevice(ivaplugin1->gpu_id), "Unable to set cuda device");
	nvll_osd_set_params(ivaplugin1->nvosd_ctx, ivaplugin1->video_info.width, ivaplugin1->video_info.height);

	if (ivaplugin1->inter_buf)
		NvBufSurfaceDestroy(ivaplugin1->inter_buf);
	ivaplugin1->inter_buf = NULL;

	NvBufSurfaceCreateParams create_params;
	/* An intermediate buffer for NV12/RGBA to BGR conversion  will be
	 * required. Can be skipped if custom algorithm can work directly on NV12/RGBA. */
	create_params.gpuId = ivaplugin1->gpu_id;
	create_params.width = ivaplugin1->video_info.width;
	create_params.height = ivaplugin1->video_info.height;
	create_params.size = 0;
	create_params.colorFormat = NVBUF_COLOR_FORMAT_RGBA;
	create_params.layout = NVBUF_LAYOUT_PITCH;
#ifdef __aarch64__
	create_params.memType = NVBUF_MEM_DEFAULT;
#else
	create_params.memType = NVBUF_MEM_CUDA_UNIFIED;
#endif

	if (NvBufSurfaceCreate(&ivaplugin1->inter_buf, 1,
		&create_params) != 0) {
		GST_ERROR("Error: Could not allocate internal buffer for dsexample");
		goto error;
	}
#endif //NVIDIA
	ivaplugin1->osdParam.init();
	GST_DEBUG_OBJECT(ivaplugin1, "created CV Mat\n");

	if (ivaplugin1->events == NULL)
		ivaplugin1->events = new std::vector<evt::EventInfo>();

#ifdef NVIDIA
	if (ivaplugin1->targetSaveTracks == NULL)
		ivaplugin1->targetSaveTracks = new TargetTracks();
#endif

	return TRUE;

#ifdef NVIDIA
error :
	if (ivaplugin1->npp_stream) {
		cudaStreamDestroy(ivaplugin1->npp_stream);
		ivaplugin1->npp_stream = NULL;
	}
	return FALSE;
#endif //NVIDIA
}

/**
 * Called when element recieves an input buffer from upstream element.
 */
static GstFlowReturn gst_ivaplugin1_transform_ip(GstBaseTransform * btrans, GstBuffer * inbuf)
{
	GstIvaPlugin1 *ivaplugin1 = GST_IVAPLUGIN1(btrans);
	GstMapInfo in_map_info;
	GstFlowReturn flow_ret = GST_FLOW_ERROR;
	BufSurface *surface = NULL;
	BatchMeta *batch_meta = NULL;
	FrameMeta *ds_frame_meta = NULL;
	AIMetaList* l_frame = NULL;
	AIMetaList* l_user = NULL;

	int index = 0;
#ifdef NVIDIA
    std::shared_ptr<record::EventVideoRecord> eventVideoRecord =nullptr;
	CHECK_CUDA_STATUS(cudaSetDevice(ivaplugin1->gpu_id), "Unable to set cuda device");
#endif //NVIDIA
	memset(&in_map_info, 0, sizeof(in_map_info));
	if (!gst_buffer_map(inbuf, &in_map_info, GST_MAP_READ))
	{
		g_print("Error: Failed to map gst buffer\n");
		goto error;
	}
	surface = (BufSurface*)in_map_info.data;
#ifdef NVIDIA
	if (CHECK_NVDS_MEMORY_AND_GPUID(ivaplugin1, surface))
	{
		goto error;
	}
#endif //NVIDIA
	batch_meta = gst_buffer_get_batch_meta(inbuf);
	if (batch_meta == nullptr) {
		GST_ELEMENT_ERROR(ivaplugin1, STREAM, FAILED, ("BatchMeta not found for input buffer."), (NULL));
		return GST_FLOW_ERROR;
	}

	// Standard way of iterating through buffer metadata
	for (l_frame = batch_meta->frame_meta_list; l_frame != NULL; l_frame = l_frame->next)
	{
		ds_frame_meta = (FrameMeta*)(l_frame->data);

		AIMetaList* l_user_meta = ds_frame_meta->frame_user_meta_list;
		UserMeta* iva_user_meta = (UserMeta*)(l_user_meta->data);
		for (l_user = ds_frame_meta->frame_user_meta_list; l_user != NULL; l_user = l_user->next)
		{
			UserMeta* user_meta = (UserMeta*)(l_user->data);
			if (user_meta->base_meta.meta_type == IVA_META_FRAME_INFO)
			{
				iva_user_meta = user_meta;
				break;
			}
		}
		auto iva_frame_meta = (IVAFrameMeta*)iva_user_meta->user_meta_data;

		auto batch_id = ds_frame_meta->batch_id;
		auto stream_id = ds_frame_meta->source_id;
		ivaplugin1->stream_id = stream_id;
        auto frame_num = ivaplugin1->frame_num = ds_frame_meta->frame_num;

		cv::Mat frameMat;
		guint channelId = ds_frame_meta->source_id;
		float video_width = ivaplugin1->video_info.width;
		float video_height = ivaplugin1->video_info.height;
		//float original_width = ds_frame_meta->source_frame_width;
		//float original_height = ds_frame_meta->source_frame_height;
		float scale_x = EVT_PROCESS_WIDTH / video_width;
		float scale_y = EVT_PROCESS_HEIGHT / video_height;

		// 记录帧率
		record_fps(ivaplugin1);

		// 获取检测数据
		evt::TrackList inputTracks;
		fetch_targets(ivaplugin1, inputTracks, ds_frame_meta, scale_x, scale_y);

		// 事件分析		
		evt::TargetInfoList outputTargets; // 获取检测目标
		evt::EventObjectList evtObjects; // 物体事件检测 （抛洒物、烟火、路障等）
		evt::EventInfoList jams;	// 拥堵信息

        if (ivaplugin1->snapshot_request)
        {
            if (frameMat.empty())
                capture_frame(ivaplugin1, frameMat, surface, ds_frame_meta->batch_id);

            snapImg( frameMat, ivaplugin1->video_id, channelId, ivaplugin1->preset_id, scale_x, scale_y);
            ivaplugin1->snapshot_request = false;
        }

		if (ivaplugin1->detecting != IVA_CHANNEL_PAUSED_DEFAULT)
		{
			if (ivaplugin1->event_channel == NULL)
			{
				ivaplugin1->event_channel = evt::getChannel(channelId);
				if (ivaplugin1->event_channel)
				{
					//IVA_LOG_INFO("[plugin1] Channel: {} getChannel ok", channelId);
					ivaplugin1->event_channel->setNewEvtCallback([&](evt::EventInfo ei) {ivaplugin1->events->emplace_back(ei); });
					ivaplugin1->event_channel->setWithdrawEvtCallback(onEventWithdrawCallback);
					ivaplugin1->event_channel->setTargetsPassedCallback(onTargetPassedCallback);
					if (SETTINGS->reportVehicleInfo())
						ivaplugin1->event_channel->setVehiclesInfoCallback(onVehiclesInfoCallback);
#ifdef NVIDIA
                    if (ivaplugin1->videoRecordBasePtr == nullptr)
                    {
                        auto eventVideoRecord = record::getEventVideoRecord(channelId);
                        if ((eventVideoRecord) && (eventVideoRecord->getRecordType() == record::EvtRecordType::IVA))
                        {
                            ivaplugin1->videoRecordBasePtr = eventVideoRecord;
                            eventVideoRecord->registerEvtRecordCallback([=](int duration, int position, const record::EvtVideoRecordTask& evt){
                                if (evt.eventInfo.type == evt::EventType_Jam && evt.eventInfo.removeTime > 0)  ///< 拥堵解除事件录像
                                {
                                    iva::protocol::postEventRemove(evt.eventInfo.id, get_system_full_time_str(evt.eventInfo.removeTime), evt.eventInfo.type, evt.imgPath, evt.videoPath);
                                }
                                else  ///< 事件录像框存储
                                {
                                    std::string fileName;
                                    auto pos = evt.imgPath.find_last_of('.');
                                    if(pos != std::string::npos)
                                        fileName =  evt.imgPath.substr(0, pos);
                                    std::string trackPath = std::string(WEB_SERVER_ROOT) + "/" + fileName + ".txt";
                                    updateTrackSaveTask(trackPath, duration, position, ivaplugin1->targetSaveTracks, evt.eventInfo);
                                    iva::protocol::postEvent(evt.channelId, evt.videoPath, evt.imgPath, evt.alarmType, evt.objectArea, evt.eventInfo);
                                }
                            });

                            eventVideoRecord->registerEvtRecordCompletedCallback([=](std::string videoPath, int duration, int realDuration, int targetId){
                                saveTrackDataToFile(ivaplugin1->targetSaveTracks, targetId, duration*1000 - realDuration);
                                std::string videoWebUrl = iva::protocol::PROTOCOL_MANAGER.getWebURL() + videoPath;
                                iva::protocol::postEventVideoFinished(videoWebUrl);

                            });

                        }


                    }
#endif
#ifdef USE_SEARCH_MODULE
                    if (!ivaplugin1->writingImage)
                    {
                        ivaplugin1->writingImage = true;
                        ivaplugin1->passedImages = std::make_shared<std::deque<ImageToSave>>();
                        ivaplugin1->writeImageThread = std::thread([ivaplugin1](){writeImageTask(ivaplugin1);});
                    }

                    if (ivaplugin1->videoRecordPtr == nullptr)
                    {
                        ivaplugin1->videoRecordPtr = record::getVideoRecord(channelId);
                        if (ivaplugin1->videoRecordPtr)
                        {
                            ivaplugin1->videoRecordPtr->registerRecordCallback([=](const std::string& fileName, int position, record::PassedTargetInfo targetInfo){
                                std::string imagePath = writeImage(channelId, targetInfo.frame, ivaplugin1->frame_num, ivaplugin1, targetInfo.targets);
                                retrieveFeature(scale_x, scale_y, fileName, position, targetInfo, imagePath);
                            });
                        }
                    }

                    ivaplugin1->event_channel->setVehiclesPassedCallback([&, scale_x, scale_y, ivaplugin1, channelId](evt::TargetInfoList& targets) {
                        if (frameMat.empty())
                            capture_frame(ivaplugin1, frameMat, surface, ds_frame_meta->batch_id);
                        if (ivaplugin1->videoRecordPtr)
                        {
                            auto now = std::chrono::system_clock::now();
                            ivaplugin1->videoRecordPtr->submit(record::PassedTargetInfo{now, targets, frameMat}); //!< 提交录像任务
                        }
                        else //! 未开启录像，则只上报特征和截图
                        {
                            std::string imagePath = writeImage(channelId, frameMat, ivaplugin1->frame_num, ivaplugin1, targets);
                            record::PassedTargetInfo passedTargetInfo{std::chrono::system_clock::now(), targets, frameMat};
                            retrieveFeature(scale_x, scale_y, "", 0, passedTargetInfo, imagePath);
                        }
                    });
#endif //USE_SEARCH_MODULE
				}
			}
			if (ivaplugin1->event_channel)
			{
				update_image_object(evt::EventType_RoadBlock, iva_frame_meta->roadblocks, evtObjects, scale_x, scale_y);
				update_image_object(evt::EventType_FireSmoke, iva_frame_meta->firesmokes, evtObjects, scale_x, scale_y);
				update_image_object(evt::EventType_Obstacle, iva_frame_meta->roiobjects, evtObjects, scale_x, scale_y);
                update_image_object(evt::EventType_Landslide, iva_frame_meta->landslides, evtObjects, scale_x, scale_y);
                update_image_object(evt::EventType_Weather, iva_frame_meta->weathers, evtObjects, scale_x, scale_y);

				if (evtObjects.size() > 0)
					ivaplugin1->event_channel->update(evtObjects, ivaplugin1->preset_id);

				// 车辆事件检测
				if (ivaplugin1->event_channel->update(inputTracks, frame_num, ivaplugin1->preset_id))
				{

					ivaplugin1->event_channel->getCurrentTargets(outputTargets, ivaplugin1->preset_id);

					// 事件截图
					if (ivaplugin1->events->size() > 0)
					{
						if (frameMat.empty())
							capture_frame(ivaplugin1, frameMat, surface, ds_frame_meta->batch_id);
					}



					// 结构化页面叠加 拥堵信息
					if (ivaplugin1->draw_frame)
					{
						ivaplugin1->event_channel->getCurrentEventInfos(jams, evt::EventType_Jam, ivaplugin1->preset_id);
					}

					// 事件上报
					if (ivaplugin1->events->size() > 0)
					{
						updateEvents(channelId, ivaplugin1->events, frameMat, frame_num, scale_x, scale_y, ivaplugin1->gpu_id);
					}
#ifdef IVA_EVENT_VIDEO_RECORD
					// 更新轨迹保存
					updateTrackData(ivaplugin1->targetSaveTracks, inputTracks, ds_frame_meta->buf_pts);
#endif

					ivaplugin1->events->clear();
				}
			}
		}

		// 结构化页面叠加
		if (ivaplugin1->draw_frame && gst_buffer_is_writable(inbuf))
		{
			bool bShowTrack = (isLogEnabled(SHOW_IVA_TRACK) && ivaplugin1->print_process_log);
			OSDFrameInfo frameinfo{ index, scale_x, scale_y, bShowTrack, frame_num, ivaplugin1->detecting };
			modify_frame(surface, ivaplugin1->nvosd_ctx, outputTargets, jams, evtObjects, ivaplugin1->osdParam, frameinfo);
		}

		// 手工暂停 绘制检测区暂停 无需上报图像状态
		if (iva_frame_meta->detecting != IVA_CHANNEL_PAUSED_DEFAULT)
		{
			// 图像质量状态
			FrameState frameState;
			frameState.blur = iva_frame_meta->blur;
			frameState.black_screen = iva_frame_meta->black_screen;
			frameState.fog = iva_frame_meta->fog;
			frameState.shifting = iva_frame_meta->shifting;

			// 更新上报图像质量状态
			updateFrameState(frameState, ivaplugin1->last_frame_state, stream_id, ivaplugin1->video_id, ivaplugin1->preset_id, true);
		}

		// 打印 插件状态
		ivaplugin1->frame_log_remain--;
		if (ivaplugin1->frame_log_remain <= 0)
		{
			ivaplugin1->frame_log_remain = FRAME_LOG_REMAIN;

			// 实时打印日志
			if (isLogEnabled(SHOW_IVA_FRAME) && ivaplugin1->print_process_log)
			{
				if (ivaplugin1->detecting)
				{
					IVA_LOG_INFO("Channel:{}, video_id:{}, preset_id:{},width:{} height:{} targets:{}",
						stream_id, ivaplugin1->video_id, ivaplugin1->preset_id, ivaplugin1->video_info.width, ivaplugin1->video_info.height, inputTracks.size());
				}
				else
				{
					IVA_LOG_WARN("Channel:{}, not detecting, status:{}, video_id:{}, preset_id:{}", stream_id, ivaplugin1->detecting, ivaplugin1->video_id, ivaplugin1->preset_id);
					if (ivaplugin1->init_status == FALSE) {
						//IVA_LOG_WARN("not recv restore message, restart progess");
						//iva::protocol::reSendRequestInit();
						// exit(1);
					}
				}
			}
		}

		// 上报真实的检测状态
		if (!ivaplugin1->status_inited || ivaplugin1->frame_log_remain % FRAME_STATUS_REPORT == (FRAME_STATUS_REPORT-1))
		{
			if (ivaplugin1->video_id > 0)
			{
				ivaplugin1->status_inited = true;
                iva::protocol::postDetectStatus(stream_id, ivaplugin1->video_id, ivaplugin1->detecting != IVA_CHANNEL_PAUSED_DEFAULT);
			}
		}
		index++;
	}

	flow_ret = GST_FLOW_OK;

error:
	gst_buffer_unmap(inbuf, &in_map_info);

	if (!ivaplugin1->draw_frame)
	{
		return GST_BASE_TRANSFORM_FLOW_DROPPED;
	}
	else
	{
		return flow_ret;
	}
}

/*
*	获取当前帧
*/
static bool capture_frame(GstIvaPlugin1* ivaplugin1, cv::Mat& output, BufSurface* ip_surf, int batch_id)
{
#ifdef NVIDIA
    //Memset the memory
    NvBufSurfaceMemSet(ivaplugin1->inter_buf, 0, 0, 0);

    NvBufSurfTransformRect src_rect = { 0, 0, (uint32_t)(ivaplugin1->video_info.width), (uint32_t)ivaplugin1->video_info.height };
    NvBufSurfTransformRect dst_rect = { 0, 0, (uint32_t)ivaplugin1->video_info.width, (uint32_t)ivaplugin1->video_info.height };

    NvBufSurfTransformParams transform_params;
    // Set the transform parameters
    transform_params.src_rect = &src_rect;
    transform_params.dst_rect = &dst_rect;
//    transform_params.transform_flag =
//    	NVBUFSURF_TRANSFORM_FILTER | NVBUFSURF_TRANSFORM_CROP_SRC |
//    	NVBUFSURF_TRANSFORM_CROP_DST;
//    transform_params.transform_filter = NvBufSurfTransformInter_Default;


    NvBufSurfTransformConfigParams transform_config_params;
    /* Configure transform session parameters for the transformation */
    transform_config_params.compute_mode = NvBufSurfTransformCompute_Default;
    transform_config_params.gpu_id = ivaplugin1->gpu_id;
    transform_config_params.cuda_stream = ivaplugin1->npp_stream;

    /* Set the transform session parameters for the conversions executed in this
     * thread. */
    NvBufSurfTransform_Error error = NvBufSurfTransformSetSessionParams (&transform_config_params);
    if (error != NvBufSurfTransformError_Success) {
        GST_ELEMENT_ERROR (ivaplugin1, STREAM, FAILED,
                           ("NvBufSurfTransformSetSessionParams failed with error %d", error), (NULL));
        return false;
    }

    // Transformation scaling+format conversion if any.
    NvBufSurfTransform_Error err = NvBufSurfTransform(ip_surf, ivaplugin1->inter_buf, &transform_params);
    if (err != NvBufSurfTransformError_Success) {
        GST_ELEMENT_ERROR(ivaplugin1, STREAM, FAILED,
                          ("NvBufSurfTransform failed with error %d while converting buffer", err),
                          (NULL));
        return false;
    }

    // Map the buffer so that it can be accessed by CPU
    if (NvBufSurfaceMap(ivaplugin1->inter_buf, 0, 0, NVBUF_MAP_READ) != 0) {
        return false;
    }

    // Cache the mapped data for CPU access
    if(ivaplugin1->inter_buf->memType == NVBUF_MEM_SURFACE_ARRAY)
        NvBufSurfaceSyncForCpu (ivaplugin1->inter_buf, batch_id, 0);

    cv::Mat in_mat =
            cv::Mat(ivaplugin1->video_info.height, ivaplugin1->video_info.width,
                    CV_8UC4, ivaplugin1->inter_buf->surfaceList[batch_id].mappedAddr.addr[0],
                    ivaplugin1->inter_buf->surfaceList[batch_id].pitch);


#if (CV_MAJOR_VERSION >= 4)
	cv::cvtColor(in_mat, output, cv::COLOR_RGBA2BGR);
#else
	cv::cvtColor(in_mat, output, CV_RGBA2BGR);
#endif

	if (NvBufSurfaceUnMap(ivaplugin1->inter_buf, 0, 0)) {
		return false;
	}
#else //NVIDIA
	surface2cvmat(&ip_surf->surfaceList[batch_id], output);
	try{
        cv::cvtColor(output, output, cv::COLOR_RGB2BGR);
    }
    catch (std::exception& e){
		return false;
    }
#endif //NVIDIA
	return true;
}

/*
*	获取当前检测目标集
*/
static void fetch_targets(GstIvaPlugin1* ivaplugin1, evt::TrackList& output, FrameMeta* ds_frame_meta, float scale_x, float scale_y)
{
	ObjectMetaList*obj_meta_list = ds_frame_meta->obj_meta_list;

	AIMetaList* l_obj = NULL;
	for (l_obj = ds_frame_meta->obj_meta_list; l_obj != NULL; l_obj = l_obj->next)
	{
		auto obj_meta = (ObjectMeta*)(l_obj->data);
		if (obj_meta->class_id > IVA_TARGET_TYPE_TWO_WHEEL)
			continue;
		//! 获取目标分类类型
#ifdef NVIDIA // 二分类 支持
        auto targetClassTypes = get_classifier_class_types(obj_meta->classifier_meta_list);
#else
		std::map<std::string, std::string> targetClassTypes;
#endif
		evt::Track track{ (int)(obj_meta->object_id), (evt::TargetType)obj_meta->class_id, targetClassTypes,
				evt::Rect {obj_meta->rect_params.left* scale_x + OFFSET_VALUE,
						 obj_meta->rect_params.top* scale_y + OFFSET_VALUE,
						 obj_meta->rect_params.width* scale_x,
						 obj_meta->rect_params.height* scale_y }};
		
		output.emplace_back(std::move(track));
	}
}

/*
*	记录帧率
*/
static void record_fps(GstIvaPlugin1* ivaplugin1)
{
	if (ivaplugin1->fps_start_time_stamp == 0)
	{
		ivaplugin1->fps_start_time_stamp = get_system_timestamp_of_usec();
	}
	else
	{
		ivaplugin1->fps_frame_count_tmp++;

		// calculate time for FPS_FRAME_COUNT frames, and get the fps, log for it
		if (ivaplugin1->fps_frame_count_tmp >= FPS_FRAME_COUNT)
		{
			long time_now = get_system_timestamp_of_usec();
			long time = time_now - ivaplugin1->fps_start_time_stamp;
			double fps = ivaplugin1->fps_frame_count_tmp / (time / 1000000.0);

			if (isLogEnabled(SHOW_IVA_FPS) && ivaplugin1->print_process_log)
			{
				IVA_LOG_INFO("CHANNEL {} FPS:{}, total{} in {} usec",
					ivaplugin1->stream_id,
					std::to_string(fps).c_str(),
					ivaplugin1->fps_frame_count_tmp,
					std::to_string(time).c_str());
			}
			ivaplugin1->fps_frame_count_tmp = 0;
			ivaplugin1->fps_start_time_stamp = 0;
		}
	}
}

evt::EventType get_weather_event_type(int classId)
{
    switch (classId)
    {
        case IVA_TARGET_TYPE_WEATHER_FOG:
            return evt::EventType::EventType_Weather_Fog;
        case IVA_TARGET_TYPE_WEATHER_SNOW:
            return evt::EventType::EventType_Weather_Snow;
        case IVA_TARGET_TYPE_WEATHER_RAIN:
            return evt::EventType::EventType_Weather_Rain;
        default:
            return evt::EventType::EventType_None;
    }
}

/*
*	物体事件检测 （抛洒物、烟火、路障等）
*/
static void update_image_object(evt::EventType evtType, IVATargetArray*& meta_targets, evt::EventObjectList&evt_objs, float scale_x, float scale_y)
{
	if (meta_targets == NULL) return;
	for (int i=0; i< meta_targets->len; ++i)
	{
		auto& target = meta_targets->targets[i];
        auto eventType = evtType;
        if (evtType == evt::EventType::EventType_Weather) ///< 天气事件，需要根据分类结果进行分类子事件
        {
            eventType = get_weather_event_type(target.class_id);
        }
		evt::EventObject object{ target.target_id, eventType,
						evt::Rect(target.x * scale_x, target.y * scale_y, target.width * scale_x, target.height * scale_y) };
		evt_objs.emplace_back(std::move(object));
	}
}
#ifdef NVIDIA // 二分类 支持
/**
 * 获取分类器的分类类型
 * @return key: classifierType value: label
 */
static std::map<std::string, std::string> get_classifier_class_types(NvDsClassifierMetaList* classifierMetaList)
{
	std::map<std::string, std::string> targetClassTypes;
	if (classifierMetaList)
	{
		for (auto classMetaList = classifierMetaList; classMetaList != NULL; classMetaList = classMetaList->next)
		{
			auto class_meta = (NvDsClassifierMeta*)(classMetaList->data);
			auto classifierId = class_meta->unique_component_id;
			if (auto it(classifierTable.find(classifierId)); it != classifierTable.end())
			{
				auto [classId, label] = get_classifier_class_id(class_meta);
				if (classId > -1)
					targetClassTypes.emplace(it->second, label);
			}
		}
	}
	return targetClassTypes;
}

/**
 * 获取分类器的分类ID,分类标签
 */
static std::pair<int, std::string> get_classifier_class_id(NvDsClassifierMeta* classifierMeta)
{
	if (classifierMeta)
	{
		for (auto label_list = classifierMeta->label_info_list; label_list != NULL; label_list = label_list->next)
		{
			auto labelInfo = (NvDsLabelInfo*)(label_list->data);
			if (labelInfo && labelInfo->result_label[0] != '\0')
				return { (int)labelInfo->result_class_id,std::string(labelInfo->result_label) };
		}
	}
	return { -1,"" };
}

/**
 *	从config文件读取classifierId与classifierType的映射关系
 */
static bool initClassifierTable()
{
    if (!classifierTable.empty())
        return true;

    std::string configFile = SETTINGS->modelPath() + "/classifier_type.txt";

    if (!fs::exists(configFile) || (fs::is_empty(configFile)))
    {
        IVA_LOG_ERROR("\n\n\n分类器类型配置文件{}不存在或者配置为空\n\n\n", configFile);
        return false;
    }

    std::ifstream infile(configFile);
    std::string lineContent;
    int fileLine = 0;
    while (std::getline(infile, lineContent))
    {
        ++fileLine;
        std::vector<std::string> token;
        split(lineContent,token,",");
        if (token.size() != 2)
        {
            IVA_LOG_ERROR("\n\n\n分类器类型配置文件{},{}行:{} 配置数量错误，请按此格式配置:3,personClothes\n\n\n" ,configFile, fileLine, lineContent);
            continue;
        }

        try{
            int classifierId = std::stoi(token[0]);
            classifierTable[classifierId] = token[1];
        }
        catch (std::exception& e){
            IVA_LOG_ERROR("\n\n\n读取分类器配置文件{},{}行:{} 失败：非法分类器ID:{}, 错误码:{}, 请按此格式配置:3,personClothes \n\n\n" , configFile, fileLine, lineContent, token[0], e.what());
            return false;
        }
    }
    return true;
}

/**
 *	特征提取
 *	@param scaleX, scaleY 原始帧缩放比例
 *	@param fileName       录制视频存储路径
 *	@param position       录像起始播放时间(秒)
 *	@param targets        过线目标
 *	@param frame          过线目标时的帧图像
 *	@param imgPath        过线存图路径
 */
static void retrieveFeature(float scaleX,float scaleY, const std::string& fileName, int position, record::PassedTargetInfo& targetInfo, std::string imgPath)
{
    if (targetInfo.frame.empty() || scaleX == 0 || scaleY == 0)
    {
        IVA_LOG_ERROR("parameter invalid")
        return;
    }
    auto imageWidth = targetInfo.frame.cols;
    auto imageHeight = targetInfo.frame.rows;
    uint64_t timeOffset = 0;                      //!< 当同一个视频资源，在同一时刻，检测到不同的车辆时，上报的过车记录必须将时间戳进行微调处理(增加1ms)
    // 提取特征
    for (auto& t : targetInfo.targets)
    {
        auto featureInfo = new network::FeatureInfo();
        featureInfo->laneId = t.laneID;
        featureInfo->roiId = t.roiID;
        featureInfo->img = imgPath;
        featureInfo->video = fileName;
        featureInfo->passTime = (uint64_t)std::chrono::round<std::chrono::milliseconds>(targetInfo.startTime.time_since_epoch()).count() + timeOffset;
        auto rect = t.tracks.back().rect;
        featureInfo->rect = { {rect.getLeft() / (float)imageWidth, rect.getTop() / (float)imageHeight}, {rect.getRight() / (float)imageWidth, rect.getBottom() / (float)imageHeight} };
        featureInfo->videoOffset = position;		//录像起始播放时间(秒)
        featureInfo->vehicleColor = t.classTypes.getClassType(evt::VEHICLE_COLOR_CLASSIFIER, true);
        featureInfo->vehicleType = t.classTypes.getClassType(evt::VEHICLE_TYPE_CLASSIFIER, true);
        featureInfo->speed = t.speed;
        cv::Rect roi{ (int)(rect.x / scaleX), (int)(rect.y / scaleY), (int)(rect.width / scaleX), (int)(rect.height / scaleY) };
        cv::Mat mat{targetInfo.frame, roi};
        ia::retrieveFeature(mat, featureInfo);
        ++timeOffset;
    }
}

/**
 *	过线存图，将存图任务提交到异步任务队列中
 *	@param channelId     pipeline通道号
 *	@param frame         过线目标时的帧图像
 *	@param frameNum      帧序号
 *	@param ivaPlugin1    通道对应的插件1
 */
static std::string writeImage(guint channelId, cv::Mat frame, gint frameNum, GstIvaPlugin1 *ivaPlugin1, evt::TargetInfoList& targets)
{
    if (frame.empty() || !ivaPlugin1)
    {
        IVA_LOG_ERROR("parameter invalid")
        return "";
    }

    //  存储图片
    auto timestamp = get_system_timestamp();
    std::string szDate = get_system_short_time_str(timestamp);

    std::stringstream fileName;
    fileName << get_system_simple_time_str(timestamp) << "_" << channelId + 1 <<  "_" << frameNum % 1000 ;

    std::string imagePath = "/searchimg/" + szDate + "/" + fileName.str() + ".jpg";
    std::string fullPath = std::string(WEB_SERVER_ROOT) + imagePath;
    iva::protocol::createPath(fullPath);

    std::unique_lock lk(ivaPlugin1->imagesLock);
    ivaPlugin1->passedImages->emplace_back(ImageToSave{fullPath, frame, SETTINGS->drawRects() ? targets : evt::TargetInfoList{}}); //!< 不绘制框的时候，不用传入targets
    lk.unlock();

    ivaPlugin1->writeImageCondition.notify_one();

    return imagePath;
}

/**
 *	过线存图线程
 *	@param ivaPlugin1    通道对应的插件1
 */
static void writeImageTask(GstIvaPlugin1 *ivaPlugin1)
{
    if (!ivaPlugin1)
    {
        IVA_LOG_ERROR("failed to initialize ivaPlugin1")
        return;
    }

    while (ivaPlugin1->writingImage)
    {
        std::unique_lock lk(ivaPlugin1->conditionLock);
        auto &images = ivaPlugin1->passedImages;
        if (!ivaPlugin1->writeImageCondition.wait_for(lk, std::chrono::milliseconds(300), [&images]() { return (images && !images->empty()); }))
            continue;

        while(ivaPlugin1->writingImage && !images->empty())
        {
            std::unique_lock lock(ivaPlugin1->imagesLock);
            auto [imagePath, frame, targets] = ivaPlugin1->passedImages->back();
            ivaPlugin1->passedImages->pop_back();
            lock.unlock();

            if (SETTINGS->drawRects())
            {
                for (auto&target : targets)
                {
                    auto rect = target.tracks.back().rect;
                    cv::Rect cvRect;
                    cvRect.x = std::max((int)rect.x, 0);
                    cvRect.y = std::max((int)rect.y, 0);
                    cvRect.width = std::min(int(rect.width), frame.cols - cvRect.x);
                    cvRect.height = std::min(int(rect.height), frame.rows - cvRect.y);
                    cv::rectangle(frame, cvRect, cv::Scalar(0, 0, 255), 2);
                }
            }

            try{
                cv::imwrite(imagePath, frame, {cv::IMWRITE_JPEG_QUALITY, SETTINGS->imageQuality()});
            }
            catch (const cv::Exception &e){
                IVA_LOG_ERROR("[plugin1] Channel: %d, searchimg save error: %s",ivaPlugin1->stream_id, e.what());
            }
        }
    }
}
#endif

/**
 * Boiler plate for registering a plugin and an element.
 */
static gboolean ivaplugin1_plugin_init(GstPlugin * plugin)
{
	GST_DEBUG_CATEGORY_INIT(gst_ivaplugin1_debug, "ivaplugin1", 0,
		"iva plugin 1");

	return gst_element_register(plugin, "ivaplugin1", GST_RANK_PRIMARY,
		GST_TYPE_IVAPLUGIN1);
}
GST_PLUGIN_DEFINE(GST_VERSION_MAJOR, GST_VERSION_MINOR, ivaplugin1, DESCRIPTION, ivaplugin1_plugin_init, "3.0", LICENSE, BINARY_PACKAGE, URL)


