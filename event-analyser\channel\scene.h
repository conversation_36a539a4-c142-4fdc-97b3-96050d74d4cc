/**
 * Project AI事件分析模块
 */


#ifndef _SCENE_H
#define _SCENE_H

#include <chrono>
#include <memory>
#include <mutex>
#include <vector>
#include <list>
#include "channel.h"
#include "scene_state.h"
#include "element/track.h"
#include "element/event_object.h"
#include "element/target.h"
#include "area/roi.h"
#include "area/region.h"

/**
* 通道事件分析场景
*/
namespace evt
{
	#define DEFAULT_FRAME_WIDTH 1920
	#define DEFAULT_FRAME_HEIGHT 1080

	using std::vector;
	using std::list;
	using std::shared_ptr;
	using std::chrono::steady_clock;

	/**
	* 通道场景状态
	*/
	enum DetectState
	{
		DetectState_Paused, // 暂停
		DetectState_Resumed,// 恢复
		DetectState_Running // 检测中
	};

	/**
	* 事件撤回理由
	*/
	enum WithdrawReason
	{
		WithdrawReason_Default,		// 默认
		WithdrawReason_Jam,			// 拥堵
		WithdrawReason_TrafficFlow, // 交通流
		WithdrawReason_CameraOffset // 相机偏移(图像)
	};

	class Scene {
	public:

		Scene(int id);
		~Scene();

		/**
		 * 初始化感兴趣区配置
		 * @param roicfg
		 * @param planLevel 参数预案等级
		 */
		void init(vector<ROIInfo>& roicfgs, int planLevel= 1);

		/**
		 * 暂停检测
		 * @camOffset 是否摄像机偏移暂停
		 */
		void pause(bool camOffset= false);

		/**
		 * 恢复检测
		 */
		void resume();

		/**
		 * 设置画面尺寸
		 * @param w 宽
		 * @param h 高
		 */
		void setFrameSize(int w, int h);

		/**
		 * 设置画面帧率
		 * @param rate 帧率
		 */
		void setFrameRate(int rate);

		/**
		 * 设置发生新事件回调
		 * @param cb
		 */
		void setNewEvtCallback(EventCallback cb);

		/**
		 * 设置事件撤回回调
		 * @param cb
		 */
		void setWithdrawEvtCallback(EventCallback cb);

		/**
		 * 设置过线目标回调
		 * @param cb
		 */
		void setTargetsPassedCallback(TargetsPassedCallback cb);

		/**
		 * 设置车辆过线回调
		 * @param cb
		 */
		void setVehiclesPassedCallback(TargetsPassedCallback cb);

		/**
		 * 设置车辆信息上报回调
		 * @param cb
		 */
		void setVehiclesInfoCallback(const VehiclesInfoCallback &cb) {vehiclesInfoCallback = cb;}

		/**
		 * 更新轨迹（新增或更新目标）
		 * @param tracks 轨迹框列表
		 * @param frameIndex 检测帧序号
		 */
		bool update(TrackList& tracks, long frameIndex =0);

		/**
		 * 更新场景事件物体信息 （抛洒物、烟火、路障等）
		 * @param objs 物体列表
		 */
		void update(EventObjectList& objs);

		/**
		 * 获取当前所有的目标集合
		 */
		void getCurrentTargets(TargetInfoList& outputTargets);

		/**
		 * 获取当前事件信息
		 * @outputEvents 输出事件信息集合
		 * @evtType 查找类型
		 */
		void getCurrentEventInfos(EventInfoList & outputEvents, EventType evtType);

		/**
         * 捕获清晰的过线车辆
         * @param target
         */
        void capturePassedVehicle(TargetPtr target, ROI* roi);

        /**
         * 获取ROI列表
         */
        inline std::vector<Polygon> getRoiList()
        {
            std::lock_guard<std::mutex> m(sceneMutex);

            std::vector<Polygon> rois;
            for (auto& roi : roiList)
                rois.emplace_back(roi->getFramePolygon());
            return rois;
        }

	private:

		/**
		 * 释放资源
		 */
		void release();

		/**
		 * 匹配目标
		 * @param track
		 */
		TargetPtr matchTarget(Track & track);

		/**
		 * 匹配更新目标ROI及区域信息
		 * @param target
		 */
		std::tuple<ROI*, Area*> matchROI(TargetPtr target);

		/**
		 * 更新目标过线状态
		 * @param target
		 */
		void updateCountLine(TargetPtr target, ROI* roi);

		/**
		 * 检查事件
		 */
		void checkEvents();

		/**
		 * 后处理事件
		 */
		void postProcessEvents();

		/**
		 * 撤回指定类型事件
		 * @param t 事件类型
		 * @param withdrawTime 撤回时间
		 * @param reason 撤回原因
		 * @param roiID 指定ROI
		 */
		void withdrawEventsByType(EventType t, int withdrawTime, WithdrawReason reason= WithdrawReason_Default, int roiID =0);

		/**
		 * 基于ID 获取目标
		 */
		TargetPtr getTargetById(int id);

		/**
		 * 处理撤回事件
		 * @param evt
		 */
		void dealWithdrawEvent(EventPtr evt);

	private:
		/**
		 * 通道号
		 */
		int channelID;
		/**
		 * 当前检测帧序号
		 */
		long frameIndex;
		/**
		 * 当前通道帧率
		 */
		int frameRate;
		/**
		 * 帧宽
		 */
		int frameWidth;
		/**
		 * 帧高
		 */
		int frameHeight;

		/**
		 * 目标列表
		 */
		TargetList targetList;

		/**
		 * 过线目标列表
		 */
		TargetInfoList passedTargetLists;

		/**
		 * 过线车辆列表
		 */
        TargetInfoList passedVehicleLists;
		/**
		 * ROI列表
		 */
		vector<ROI*> roiList;

		/**
		 * 事件地图
		 */
		EventMap* eventMap;
		/**
		 * 目标地图
		 */
		TargetMap* targetMap;

		/**
		 * 新事件发生回调函数
		 */
		EventCallback newEvtCallback;
		/**
		 * 目标过线回调函数
		 */
		TargetsPassedCallback targetsPassedCallback;

		/**
		 * 设置车辆过线回调
		 */
		TargetsPassedCallback vehiclesPassedCallback;

		std::chrono::steady_clock::time_point lastReportTime;
		/**
         * 目标信息上报回调
         */
		VehiclesInfoCallback vehiclesInfoCallback;

		/**
		 * 事件撤回回调函数
		 */
		EventCallback withdrawEvtCallback;

		/**
		 * 场景状态
		 */
		SceneState sceneState;

		/**
		 * 检测开始时间
		 */
		steady_clock::time_point startTime;

		/**
		 * 检测状态
		 */
		DetectState detectState;

		std::mutex sceneMutex;

		/**
		 * 参数预案等级
		 */
		int paramPlanLevel = 1;

		/**
 		 * 拥堵后撤回之前多少秒内的停车事件
 		 */
		int stopWithdrawJamTime = 60;

		/**
		 * 交通流偏移后撤回之前多少秒内的逆行事件
		 */
		int oppsiteWithdrawTime = 60;

	};
}
#endif //_SCENE_H
