/**
 * Project AI事件分析模块
 */


#include "lane.h"

/**
 * Lane implementation
 * 
 * 车道
 * 
 * 基于子区域扩展而来，包含车道类型等相关属性
 */

namespace evt {

	/**
	 * @param info
	 * @param roiID
	 */
	Lane::Lane(LaneInfo info, int roiID) {
		this->id = info.id;
		this->eventMask = info.eventMask;
		this->targetMask = info.targetMask;
		this->polygon = info.polygon;
		this->config = info.config;
		this->laneType = info.laneType;
		this->direction = info.direction;
		this->roiID = roiID;
		this->areaType = AreaType_Lane;
		onConfigUpdated();
	}

	/**
	 * 获取车道类型
	 * @return LaneType
	 */
	LaneType Lane::getLaneType() {
		return laneType;
	}
}