/**
 * Project AI事件分析模块
 */


#ifndef _ACCIDENTEVENTDETECTOR_H
#define _ACCIDENTEVENTDETECTOR_H

#include <chrono>
#include "area_event_detector.h"

 /**
  *
  * 交通事故事件检测
  */
namespace evt
{

	class AccidentEventDetector : public AreaEventDetector {

	public:
		AccidentEventDetector(Area* area);
		/**
		 * @param type
		 */
		bool checkTargetType(TargetType type);

	private:
		/**
		 * 事件候选
		 * @param evt
		 */
		void onEventProposal(EventPtr evt) override;

		/**
		 * 事件维持
		 * @param evt
		 */
		void onEventMaintaining(EventPtr evt) override;

		/**
		 * 事件解除
		 * @param evt
		 */
		void onEventRelease(EventPtr evt) override;

		/**
		 * 区域处理开始
		 */
		void onUpdateRegionStart() override;

		EventPtr process(TargetPtr target) override;
		void process(EventObject object) override;
		/**
		 * 区域处理结束
		 */
		EventPtr onUpdateRegionEnd() override;

		/**
		 * 检测解除状态
		 */
		bool checkAccidentRemoved();

		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;

	private:
		//----------------配置参数---------------//
		// 行人车辆检测距离(像素)
		int checkDistance = 300;

		// 慢行车辆数量
		int checkStopCount = 2;

		//-----------------------------------------//
		// 检查是否 处于交通事故中
		bool checkInAccidentPassed = false;

		// 当前疑似事故点 （有可能存在操控员移动 或事故人员移动情况）
		Rect curPossibleAccidentRect;

		// 已记录发生事故点
		Rect accidentOccurRect;

		// 当前路障数量
		int curBlockCount = 0;

		/**
		 * 路障上次检测到的时间点
		 */
		std::chrono::steady_clock::time_point lastRoadblockFoundTime;
	};
}
#endif //_ACCIDENTEVENTDETECTOR_H