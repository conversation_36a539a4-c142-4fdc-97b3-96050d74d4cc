/**
 * Project AI事件分析模块
 */

#include "event_image_comparison.h"

/**
 * EventImageComparison implementation
 * 
 * 事件图像对比模块（单例）
 */
namespace evt
{
	/**
	 * @return EventImageComparison*
	 */
	EventImageComparison* EventImageComparison::getInstance() {
		static EventImageComparison evtImageComparison;
		return &evtImageComparison;
	}

	/**
	 * 模块是否开启
	 * @return bool
	 */
	bool EventImageComparison::isEnabled() {
		return false;
	}

	/**
	 * 事件图像对比, 加入缓存列表
	 * @param evt
	 * @param mat
	 */
	void EventImageComparison::compareEvent(EventPtr evt, cv::Mat mat) {

	}

	/**
	 * 更新图像
	 * @param channelID
	 * @param mat
	 */
	void EventImageComparison::updateImage(int channelID, cv::Mat mat) {

	}

	/**
	 * 需要更新图像
	 * @return bool
	 */
	bool EventImageComparison::imageUpdateNeeded() {
		return false;
	}

	/**
	 * 对比线程 ( 线程数量可配置)
	 */
	void EventImageComparison::onThreadCompareEvents() {

	}
}