/**
 * Project AI事件分析模块
 */


#ifndef _VECTOR2D_H
#define _VECTOR2D_H

#include "module_def.h"
#include "point.h"

/**
* 二唯向量
*/
namespace evt
{
	class EVT_EXPORT Vector2D {
	public:
		float x;
		float y;

		Vector2D();

		Vector2D(float x, float y);

		Vector2D(Point p1, Point p2);

		Vector2D operator+(float f);

		Vector2D operator+(Vector2D f);
		Vector2D& operator+=(Vector2D& v);

		Vector2D operator-(float f);
		Vector2D operator-(Vector2D f);
		Vector2D& operator-=(Vector2D& v);

		Vector2D operator/(float f);

		Vector2D operator*(float f);

		bool operator==(const Vector2D &p);

		bool operator!=(const Vector2D &p);

		float dot(const Vector2D &v);

		double cross(const Vector2D &v);

		float length();

		void normalize();

		// inner angle
		float angle(Vector2D& v);

		bool isValid();
	};
}
#endif //_VECTOR2D_H