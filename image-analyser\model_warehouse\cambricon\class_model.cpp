#include "class_model.h"

#include <utility>

namespace model_warehouse{

    ClassModel::ClassModel(std::string modelFile, int deviceID, bool isVehicle, std::string meanFile)
            :BaseModel(std::move(modelFile), deviceID, isVehicle ? NetworkInputType::CHW : NetworkInputType::HWC, std::move(meanFile))
    {
        isVehicleModel = isVehicle;
    }

    /**
     * 模型加载，资源初始化
     */
    void ClassModel::initResource()
    {
        if (!modelInited)
        {
            predictor.init(std::move(modelPath), deviceId);
            networkInputType = predictor.getInputType();
            initInputDimensions();
            initOutputDimensions();

            modelInited = true;
        }
    }

    /**
     * @brief 初始化模型输入维度信息
     */
    void ClassModel::initInputDimensions()
    {
        BaseModel::initInputDimensions();
    }

    /**
     * @brief 初始化模型输出维度信息
     */
    void ClassModel::initOutputDimensions()
    {
        auto dims1 = predictor.getOutputDims(0);
        //outputChannel = isVehicleModel ? dims1.d[0] : dims1.d[1];
        outputChannel = dims1.d[1];
    }

    /**
     * @brief                  执行一次图像推理流程：预处理、推理、后处理输出
     * @param[in] images       原始输入图像
     * @param[in] padding      是否进行图像padding
     * @return                 推理后处理输出结果
     */
    CLASS_RESULT ClassModel::infer(std::vector<cv::Mat> images, bool padding)
    {
        std::vector<float> imageData;
        std::vector<ModelOutput> buffers;
        std::vector<cv::Mat> processedImages;

        if (preprocessFunc)
            for (const auto & input : images)
            {
               auto image = preprocessFunc(input, inputWidth, inputHeight, isKeepAspectRatio);
               processedImages.push_back(image);
            }
        else
            preprocess(images, false, !isVehicleModel, imageData);

        if (!predictor.infer(buffers, processedImages, (int)images.size()))
            return {};

        return postprocess(buffers);
    }

    /**
     * @brief                  推理后处理，处理推理输出结果
     * @param imageSize        推理图像数量
     * @return                 推理后处理输出结果
     */
    CLASS_RESULT ClassModel::postprocess(std::vector<ModelOutput>& inferOutputs)
    {
        std::vector<std::vector<float>> scores;
        for (size_t i = 0; i < inferOutputs.size(); i++)
        {
            std::vector<float> outputDataHost((float *)inferOutputs[i].data.get(), (float *)inferOutputs[i].data.get() + (inferOutputs[0].size / sizeof(float)));

            float sumScores = 0.0;
            float minScore = 999999999.0;

            for(int j=0; j < outputChannel; j++)
            {
                if(minScore > outputDataHost[i * outputChannel + j])
                    minScore = outputDataHost[i * outputChannel + j];
            }

            if (minScore < 0)
            {
                for(int j=0; j < outputChannel; j++)
                    outputDataHost[i * outputChannel + j] = outputDataHost[i * outputChannel + j] - minScore + 0.01;
            }

            for (int j = 0; j < outputChannel; j++)
                sumScores += outputDataHost[i * outputChannel + j];

            std::vector<float> score;
            for(int j=0; j < outputChannel; j++)
                score.push_back(outputDataHost[i * outputChannel + j] / sumScores);

            scores.push_back(score);
        }

        return scores;
    }


    std::optional<int> ClassModel::getMaxResult(std::vector<float>& results, float threshold)
    {
        auto itMax = std::max_element(results.begin(), results.end());
        if ((*itMax) > threshold)
        {
            int klass =(int)(itMax - results.begin());
            return klass;
        }
        return std::nullopt;
    }
}
