#pragma once

#include "util/algorithm_util.h"
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

#define USING_DETECTMODEL
// 抛洒物 检测频率
#define ROI_OBJECT_DETECT_INTERVAL 30
#define WEATHER_DETECT_INTERVAL 1800

using namespace algorithm_util;
namespace roi_object_detect{

    //parameters for feature model
    struct FeatureModelParam
    {
        std::string modelDir;
        std::string modelPath;
        int maxFeatureNum;
        int imgInputW;
        int imgInputH;
        int gridW;
        int gridH;
        int downScale;
    };

    /**
     * @brief 抛洒物检测模式
     */
    enum class DetectThrowawayMode
    {
        BG_MODEL = 0,     ///< 背景建模算法检测
        DETECT_MODEL = 1, ///< 纯检测模型检测
        FUSION_MODEL = 2  ///< 融合检测
    };

    //parameters for detect model
    struct DetectModelParam
    {
        DetectThrowawayMode detectThrowawayMode;
        int cacheSize;
        std::string modelPath;
        float scaleForSeg;
        int inputW;
        int inputH;
        bool segMode;
        float nmsIouThres;
        float scoresThres;
        float rectIouThres;
        int batch;
        int paddingValue;
    };

    //parameters for class model
    struct ClassModelParam
    {
        std::string modelPath;
        float scaleForSeg;
        float scaleForPadding;
        float scoresGip;
        bool segMode;
        int batch;
        int paddingValue;
    };

    //parameters for  bgImg Generat module
    struct BgImgGeneratorParam
    {
        float motionThres;
        float bgFrontThres;
        float unFillRatioThres;
    };

    //parameters for distance solver module
    struct DistanceSolverParam
    {
        float updataDisThres;
        int maxDistanceNum;
        int validDistanceNum;
        int moveDistanceNum;
    };

    //parameters for select rect module
    struct SelectRectParam
    {
        bool isOpening;
        float iouThresForSelect;
        float iouThresForAlarm;
        float scoreThresForBinary;
        float farRatio;
        int minW;
        int minH;
        int maxW;
        int maxH;
        int maxWForFirstFilter;
        int maxHForFirstFilter;
        cv::Mat interestedMask;
        int roadNums;
        float carLeastWidth;
        std::vector<float> carNumForRoads;
    };

    //parameters for tracker module
    struct RoiObjectTrackerParam
    {
        int staticFrontTimesThres;
        int maxDecreaseTimes;
        float compareIouThres1;
        float compareIouThres2;
        bool isUseRectFlag;
        float compareStaticThres;
        bool newTraceAppendFlag;
    };

    //parameters for img save module
    //The saved imgs can be used as debugging information 
    struct ImgSavePathParam
    {
        bool saveBinaryImgFlag;
        bool saveBgImgFlag;
        bool saveOtherImgsFlag;
        std::string imgSaveDir;
        std::string bgDir;
        std::string binaryDir;
        std::string interestedDir;
        std::string alarmDir;
        std::string classifyDir;
        std::string detectDir;
        std::string classSegDir;
        std::string detectSegDir;
        std::string checkBgDir;
    };

    struct ROIObjectParam
    {
        //selectParams
        SelectRectParam* selectRectParam;
        RoiObjectTrackerParam* roiObjectTrackerParam;
        // nums of multi distances
        int maxFeatureNum;  
        // orgin img
        cv::Mat orginImg;
        // resize to featureModel's input size
        cv::Mat resizeImg;
        // orgin w/h
        int imgW;  
        int imgH;  
        // featureModel's input size
        int imgFeatureInputW;
        int imgFeatureInputH;
        // downScale of feature model
        int downScale;
        //ratio from orgin_size to input_size
        float ratioW;
        float ratioH;
        int featureBatch;  
        //interested mask
        cv::Mat interestedMask;
        //data after imgPreprocess
        vector<float> imageData;
        vector<float> bgImageData;
        vector<float> newBgImageData;
        //state of every grid , to record the grid is fill or not
        std::vector<bool> bgFillFlags;
        //distances between current img to many previous imgs
        std::vector<std::vector<double>> distances;
        //index of current img'feature
        int cudaScoresIndex;
        //single distance , multi distance
        std::vector<double> singleDis1;
        std::vector<double> singleDis2;
        std::vector<double> singleDis4;
        std::vector<double> singleDis5;
        std::vector<double> multiDis;
        //distance between current img to background img
        std::vector<double> bgFrontDis;
        std::vector<double> towBgDis;
        //flag to record bgImagePreprocess
        bool isHasBgImagePreprocess;
        //background img
        cv::Mat bgImg;
        cv::Mat newBgImg;
        cv::Mat binaryImg;
        std::vector<ObjBox> towBgRects;
        //rects without traces
        std::vector<ObjBox> singleFrameRects;
        std::vector<cv::Mat> singleFrameRois;
        int staticFrontTimesThres;
        int maxDecreaseTimes;
        std::vector<bool> singleFrameRectAlarmFlags;
        //static rects
        std::vector<ObjBox> alarmRects;
        //rects which selecting by interested mask
        std::vector<ObjBox> interestedRects;
        cv::Mat segClassImg; // to classModel;
        //rects which selecting by class model
        std::vector<ObjBox> classifyRects;
        //scoreDiv between score0 and  score1
        float scoreDiv;
        cv::Mat segDetectImg; // to detectModel;
        std::vector<ObjBox> detectRects;

        //newBg is gen
        bool isNewBgGen;
        //newBg update is not finished
        int isNewBgFinished;
        bool isBgChanged;
    };
}

