
#ifndef IVA_META_API_H
#define IVA_META_API_H

#include <glib.h>
#ifdef NVIDIA
#include "gstnvdsmeta.h"
#endif

#include "ivaelements.h"
#include <vector>
#include <linux/uuid.h>
#include "protocol/iva.h"

#ifdef __cplusplus
extern "C"
{
#endif

#ifdef NVIDIA
#define IVA_META_FRAME_INFO (nvds_get_user_meta_type("IVA_META_FRAME_INFO"))
#else
#define IVA_META_FRAME_INFO	(START_USER_META +1)
#endif // TODO ai_get_user_meta_type

/** Defines IVA traffic event types. */
typedef enum
{
    IVA_TRAFFIC_EVENT_INVALID = -1,
    IVA_TRAFFIC_EVENT_STOP = 1,
    IVA_TRAFFIC_EVENT_OPPOSITE = 2,
	IVA_TRAFFIC_EVENT_EMERGENCY_OCCUPY = 3,
    IVA_TRAFFIC_EVENT_JAM = 5,
    IVA_TRAFFIC_EVENT_PEDSTRAIN = 6,
	IVA_TRAFFIC_EVENT_OBSTACLE = 8,
	IVA_TRAFFIC_EVENT_LEAVE = 9,
	IVA_TRAFFIC_EVENT_DRIVE_IN = 10,

	IVA_TRAFFIC_EVENT_DRIVE_ACROSS = 11, 
	IVA_TRAFFIC_EVENT_ROAD_BLOCK = 12,
	IVA_TRAFFIC_EVENT_ROAD_CONSTRUCTION =13,
	IVA_TRAFFIC_EVENT_FIRE_SMOKE = 14,
	IVA_TRAFFIC_EVENT_JAM_REMOVE = 15,
	IVA_TRAFFIC_EVENT_MOTORCYCLE = 16
} IVATrafficEventType;

/** Defines IVA target types. */
typedef enum
{
    IVA_TARGET_TYPE_PERSON = 0,
    IVA_TARGET_TYPE_CAR = 1,
    IVA_TARGET_TYPE_BUS = 2,
    IVA_TARGET_TYPE_TRUCK = 3,
    IVA_TARGET_TYPE_TWO_WHEEL = 4,

	IVA_TARGET_TYPE_ROADBLOCK =10,
	IVA_TARGET_TYPE_FIRE =11,
	IVA_TARGET_TYPE_SMOKE =12,
	IVA_TARGET_TYPE_LIGHT =13,
    IVA_TARGET_TYPE_THROWAYAY=14,
    IVA_TARGET_TYPE_LANDSLIDE =15,
    IVA_TARGET_TYPE_WEATHER_FOG =16,
    IVA_TARGET_TYPE_WEATHER_SNOW =17,
    IVA_TARGET_TYPE_WEATHER_RAIN =18,
    IVA_TARGET_TYPE_WEATHER_NG =19
} IVATargetType;

/** Defines IVA Model UniqueID. */
typedef enum
{
    IVA_VEHICLE_MODEL_UNIQUE_ID = IVA_TARGET_TYPE_CAR,            ///< 车辆行人模型UniqueID
    IVA_ROADBLOCK_MODEL_UNIQUE_ID = IVA_TARGET_TYPE_ROADBLOCK,       ///< 路障模型UniqueID
    IVA_FIRE_SMOKE_MODEL_UNIQUE_ID = IVA_TARGET_TYPE_FIRE,           ///< 烟火模型UniqueID
    IVA_THROWAYAY_MODEL_UNIQUE_ID = IVA_TARGET_TYPE_THROWAYAY,       ///< 抛洒物模型UniqueID
    IVA_CAMERA_OFFSET_MODEL_UNIQUE_ID = 50,                          ///< 偏移检测模型UniqueID
} IVAModelUniqueID;

/** Defines IVA VIDEO alarm types. */
typedef enum
{
	IVA_VIDEO_QUA_AUTO_RESET = 0,
	IVA_VIDEO_QUA_ALARM_SHIFTING = 1,
	IVA_VIDEO_QUA_ALARM_BLUR = 2,
	IVA_VIDEO_QUA_ALARM_FOG = 3,
	IVA_VIDEO_QUA_ALARM_BLACK_SCREEN = 4,

} IVAVideoQuaAlarmType;


// 通道状态
typedef enum
{
	IVA_CHANNEL_PAUSED_DEFAULT,  // 暂停（手工、检测区暂停等） 图像模块无需输入检测
	IVA_CHANNEL_PAUSED_SHIFT,	 // 偏移（暂停）			   图像模块需检测 (且暂只检测偏移恢复)
	IVA_CHANNEL_RESTORED		 // 已恢复					   图像模块需检测			   
} IVAChannelState;

typedef enum
{
	IVA_CHANNEL_STOP_REASON_DEFAULT = 0,// 默认 其他
	IVA_CHANNEL_STOP_REASON_MANUAL,		// 手工
	IVA_CHANNEL_STOP_REASON_CONFIG,		//画检测区
	IVA_CHANNEL_STOP_REASON_SHIFT		// 偏移		
} IVAChannelStopReason;

/** Defines channel base info in IVA */
typedef struct _IVAChannelBasicConf
{
	bool hasPtz;
	network::ChannelStatus status;
} IVAChannelBasicConf;

/** Defines rect in IVA */
typedef struct _IVARect
{
    float left;
	float top;
	float width;
	float height;
} IVARect;

/** Defines point in IVA */
typedef struct _IVAPoint
{
	float x;
	float y;
} IVAPoint;

/** Defines target detected in current frame by primary GIE in deepstream */
typedef struct _IVATarget
{
    gint target_id;
    gint class_id;
	float x;
	float y;
	float width;
	float height;
} IVATarget;

typedef struct _IVATargetArray
{
	gint len;
	IVATarget* targets;
} IVATargetArray;

/** Defines frame meta used in IVA*/
typedef struct _IVAFrameMeta
{
	IVAChannelState detecting;

	gboolean blur;
	gboolean black_screen;
	gboolean shifting;
	gboolean fog;
	gboolean flickering;

	IVATargetArray* roiobjects;
	IVATargetArray* roadblocks;
	IVATargetArray* firesmokes;
    IVATargetArray* landslides;
    IVATargetArray* weathers;
} IVAFrameMeta;

static void free_iva_target_array(IVATargetArray* arr)
{
	if (arr != NULL)
	{
		g_free(arr->targets);
		g_free(arr);
		arr = NULL;
	}
}

static void copy_iva_target_array(IVATargetArray* src, IVATargetArray* dst)
{
	memcpy(dst, src, sizeof(IVATargetArray));

	dst->len = src->len;
	size_t size = sizeof(IVATarget) * src->len;
	dst->targets = (IVATarget*)g_malloc0(size);
	memcpy(dst->targets, src->targets, size);
}

/* release function set by user. "data" holds a pointer to NvDsUserMeta*/
static void release_user_meta(gpointer data, gpointer user_data)
{
	UserMeta* user_meta = (UserMeta*)data;
	if (user_meta->user_meta_data) {

		auto iva_frame_meta = (IVAFrameMeta*)user_meta->user_meta_data;
		if (iva_frame_meta)
		{
			free_iva_target_array(iva_frame_meta->roiobjects);
			free_iva_target_array(iva_frame_meta->roadblocks);
			free_iva_target_array(iva_frame_meta->firesmokes);
            free_iva_target_array(iva_frame_meta->landslides);
            free_iva_target_array(iva_frame_meta->weathers);
		}
		g_free(user_meta->user_meta_data);
		user_meta->user_meta_data = NULL;
	}
}

static gpointer copy_user_meta(gpointer data, gpointer user_data)
{
	UserMeta* user_meta = (UserMeta*)data;
	IVAFrameMeta* src_user_metadata = (IVAFrameMeta*)user_meta->user_meta_data;
	IVAFrameMeta* dst_user_metadata = (IVAFrameMeta*)g_malloc0(sizeof(IVAFrameMeta));
	memcpy(dst_user_metadata, src_user_metadata, sizeof(IVAFrameMeta));

	if (src_user_metadata->roiobjects != NULL)
	{
		dst_user_metadata->roiobjects = (IVATargetArray*)g_malloc0(sizeof(IVATargetArray));
		copy_iva_target_array(src_user_metadata->roiobjects, dst_user_metadata->roiobjects);
	}

	if (src_user_metadata->roadblocks != NULL)
	{
		dst_user_metadata->roadblocks = (IVATargetArray*)g_malloc0(sizeof(IVATargetArray));
		copy_iva_target_array(src_user_metadata->roadblocks, dst_user_metadata->roadblocks);
	}

	if (src_user_metadata->firesmokes != NULL)
	{
		dst_user_metadata->firesmokes = (IVATargetArray*)g_malloc0(sizeof(IVATargetArray));
		copy_iva_target_array(src_user_metadata->firesmokes, dst_user_metadata->firesmokes);
	}

    if (src_user_metadata->landslides != NULL)
    {
        dst_user_metadata->landslides = (IVATargetArray*)g_malloc0(sizeof(IVATargetArray));
        copy_iva_target_array(src_user_metadata->landslides, dst_user_metadata->landslides);
    }

    if (src_user_metadata->weathers != NULL)
    {
        dst_user_metadata->weathers = (IVATargetArray*)g_malloc0(sizeof(IVATargetArray));
        copy_iva_target_array(src_user_metadata->weathers, dst_user_metadata->weathers);
    }

	return (gpointer)dst_user_metadata;
}

static IVAFrameMeta* new_user_meta()
{
	IVAFrameMeta* iva_frame_meta = (IVAFrameMeta*)g_malloc0(sizeof(IVAFrameMeta));
	memset(iva_frame_meta, 0, sizeof(IVAFrameMeta));
	return iva_frame_meta;
}

/** @} */
#ifdef __cplusplus
}
#endif
#endif
