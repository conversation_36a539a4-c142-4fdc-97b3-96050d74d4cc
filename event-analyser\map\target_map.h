/**
 * Project AI事件分析模块
 */

#ifndef _TARGETMAP_H
#define _TARGETMAP_H

#include <map>
#include "quad_tree.h"
#include "element/polygon.h"
#include "element/target.h"

/**
* 目标地图
*
* 负责记录目标位置 方便快速按范围进行目标匹配
*/
namespace evt
{
	#define HEATMAP_MAX_FRAME 5
	#define HEATMAP_CHECK_FRAME 3
	#define HEATMAP_GRID_WIDTH 40
	#define HEATMAP_GRID_HEIGHT 27

	struct HeatInfo
	{
		int heatValue;
		bool occupied;
		HeatInfo() :heatValue(0), occupied(0) {}
	};
	
	class ROI;
	class TargetMap {
		friend class Scene;
	public:

		TargetMap(int w, int h);
		~TargetMap();

		/**
		 * 新帧开始
		 */
		void newFrameStart();

		/**
		 * 新增目标
		 * @param target
		 */
		void addTarget(TargetPtr target, ROI* roi);

		/**
		 * 删除目标
		 * @param target
		 */
		void delTarget(TargetPtr target);

		/**
		 * 更新目标
		 * @param target
		 */
		void updateTarget(TargetPtr target, ROI* roi);

		/**
		 * 基于范围获取目标集合
		 * @param r 区域
		 * @param roiID 感兴趣区ID  0获取所有
		 */
		TargetList getTargetsByRect(Rect r, int roiID = 0);

		/**
		 * 更新热力图
		 * @param target
		 */
		void updateHeatmapCount(TargetPtr target, ROI* roi);

		/**
		 * 更新区域热力图索引
		 * @param polygon
		 * @param indexs
		 */
		void initAreaHeatMapIndexs(Polygon& polygon, std::vector<int>& indexs);

		/**
		 * 获取区域热力图值
		 * @param indexs
		 * @param level
		 */
		float getHeatValue(std::vector<int>& indexs, int level = HEATMAP_CHECK_FRAME);

		/**
		 * 获取区域占比
		 * @param indexs
		 */
		float getOccupiedValue(std::vector<int>& indexs);

	private:
		QuadTree* targetTree;

		// 检测宽高
		int frameWidth;
		int frameHeight;

		// 网格单元 大小
		int gridSizeW;
		int gridSizeH;
		int gridWidth;
		int gridHeight;

		std::map<int, HeatInfo> heatmap;
	};
}
#endif //_TARGETMAP_H
