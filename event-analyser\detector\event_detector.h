/**
 * Project AI事件分析模块
 */


#ifndef _EVENTDETECTOR_H
#define _EVENTDETECTOR_H

#include "config/region_config.h"
#include "enum/event_type.h"
#include "enum/event_state.h"
#include "config/config_key.h"
#include "element/event.h"
#include "element/target.h"

/**
*
* 事件检测器（基类）
*/
namespace evt
{
	// 默认检测帧率
	#define DEFAULT_FRAME_RATE 25

	/**
	* 检测器类型
	*/
	enum DetectorType
	{
		DetectorType_Target, // 按目标 (车辆, 行人)
		DetectorType_Area,   // 按区域 (拥堵)
		DetectorType_Object,  // 按物体（烟火，抛洒物等）
        DetectorType_Class   // 按分类结果 （恶劣天气分类等）
	};

	class EventDetector {
		friend class Area;
	public:

		EventDetector();
		virtual ~EventDetector();
		/**
		 * 初始化
		 * @param eventType 事件类型
		 */
		void init(EventType eventType);

		/**
		 * 设置区域参数
		 * @param cfg 区域配置
		 */
		void setRegionConfig(RegionConfig& cfg);

		/**
		 * 设置检测帧率
		 * @param val 帧率
		 */
		void setFrameRate(int val) { frameRate = val; };

		/**
		 * 删除持有事件
		 * @param evt
		 */
		bool removeHoldEvent(EventPtr evt);

		/**
		 * 获取检测器事件类型
		 */
		EventType getEventType() { return eventType; };

		/**
		 * 获取检测器类型
		 */
		DetectorType getDetectorType() { return detectorType; };

	protected:

		/**
		 * 区域配置更新
		 */
		virtual void onUpdateRegionConfig();

		/**
		 * 区域检测开始
		 */
		virtual void onUpdateRegionStart();

		/**
		 * 区域检测结束
		 */
		virtual EventPtr onUpdateRegionEnd();

		/**
		 * 候选新事件
		 * @param target 事件目标
		 * @param state 事件状态
		 * @param pad 事件检查间距(像素)
		 */
		EventPtr proposeNew(TargetPtr target, EventState state = EventState_Proposal, int pad = 0);

		/**
		 * 新事件
		 * @param state 事件状态
		 * @param pad 事件检查间距(像素)
		 */
		EventPtr proposeNew(EventState state = EventState_Confirming, Rect rect=Rect(), int pad= 0);

		/**
		 * 事件检查类型
		 */
		EventType eventType;

		/**
		 * 事件检查类型
		 */
		DetectorType detectorType;
		/**
		 * 个性化区域配置
		 */
		RegionConfig regionConfig;

		/**
		 * 持有（关注）的事件列表
		 */
		EventList holdEvents;

		/**
		 * 检测帧率
		 */
		int frameRate;
	};
}
#endif //_EVENTDETECTOR_H
