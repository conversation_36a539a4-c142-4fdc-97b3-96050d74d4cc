
#ifndef __GST_IVAPLUGIN1_H__
#define __GST_IVAPLUGIN1_H__

#include <gst/base/gstbasetransform.h>
#include <gst/video/video.h>
#include "opencv2/imgproc/imgproc.hpp"
#include <chrono>
#ifdef NVIDIA
#include <cuda.h>
#include <cuda_runtime.h>
#include "nvbufsurface.h"
#include "nvbufsurftransform.h"
#include "gst-nvquery.h"
#include "gstnvdsmeta.h"
#else
#include "bufsurftransform.h"
#endif // NVIDIA

#include "ivameta.h"
#include "event_analyser.h"
#include "frame/frame_osd.h"
#include "frame/frame_state.h"
#include "ivaelements.h"

#ifdef NVIDIA
#include "event/event_track.h"
#include "base_video_record.h"
#include "video_record.h"
#endif

/* Package and library details required for plugin_init */
#define PACKAGE "ivaplugin1"
#define VERSION "1.0"
#define LICENSE "Proprietary"
#define DESCRIPTION "iva plugin 1"
#define BINARY_PACKAGE "iva plugin 1"
#define URL "http://wtoe.cn/"

G_BEGIN_DECLS
/* Standard boilerplate stuff */
typedef struct _GstIvaPlugin1 GstIvaPlugin1;
typedef struct _GstIvaPlugin1Class GstIvaPlugin1Class;
/**
 * 过线待存储图像信息
 */
struct ImageToSave
{
    std::string imagePath;        //!< 过线截图的存储路径
    cv::Mat image;                //!< 过线截图
    evt::TargetInfoList targets;  //!< 过线目标
};
/* Standard boilerplate stuff */
#define GST_TYPE_IVAPLUGIN1 (gst_ivaplugin1_get_type())
#define GST_IVAPLUGIN1(obj) (G_TYPE_CHECK_INSTANCE_CAST((obj),GST_TYPE_IVAPLUGIN1,GstIvaPlugin1))
#define GST_IVAPLUGIN1_CLASS(klass) (G_TYPE_CHECK_CLASS_CAST((klass),GST_TYPE_IVAPLUGIN1,GstIvaPlugin1Class))
#define GST_IVAPLUGIN1_GET_CLASS(obj) (G_TYPE_INSTANCE_GET_CLASS((obj), GST_TYPE_IVAPLUGIN1, GstIvaPlugin1Class))
#define GST_IS_IVAPLUGIN1(obj) (G_TYPE_CHECK_INSTANCE_TYPE((obj),GST_TYPE_IVAPLUGIN1))
#define GST_IS_IVAPLUGIN1_CLASS(klass) (G_TYPE_CHECK_CLASS_TYPE((klass),GST_TYPE_IVAPLUGIN1))
#define GST_IVAPLUGIN1_CAST(obj)  ((GstIvaPlugin1 *)(obj))
GST_DEBUG_CATEGORY_STATIC(gst_ivaplugin1_debug);
#define GST_CAT_DEFAULT gst_ivaplugin1_debug
static GQuark _dsmeta_quark = 0;

/* Default values for properties */
#define DEFAULT_GPU_ID 0
#define FRAME_LOG_REMAIN 1400
#define FRAME_STATUS_REPORT 700
#define RGB_BYTES_PER_PIXEL 3
#define RGBA_BYTES_PER_PIXEL 4
#define OFFSET_VALUE 0
#define FPS_FRAME_COUNT 150

/* Enum to identify properties */
enum
{
	PROP_0,
	PROP_GPU_DEVICE_ID,
	PROP_PRINT_PROCESS_LOG,
	PROP_DETECTING,
	PROP_VIDEO_ID,
	PROP_PRESET_ID,
	PROP_AUTORESET_TIME,
	PROP_IMG_ANALYSER_ENABLED,
	PROP_DRAW_FRAME,
    PROP_SNAPSHOT_REQUEST
};

#ifdef NVIDIA
#define GST_CAPS_FEATURE_MEMORY_NVMM "memory:NVMM"

#define CHECK_NVDS_MEMORY_AND_GPUID(object, surface)  \
  ({ int _errtype=0;\
   do {  \
    if ((surface->memType == NVBUF_MEM_DEFAULT || surface->memType == NVBUF_MEM_CUDA_DEVICE) && \
        (surface->gpuId != object->gpu_id))  { \
    GST_ELEMENT_ERROR (object, RESOURCE, FAILED, \
        ("Input surface gpu-id doesnt match with configured gpu-id for element," \
         " please allocate input using unified memory, or use same gpu-ids"),\
        ("surface-gpu-id=%d,%s-gpu-id=%d",surface->gpuId,GST_ELEMENT_NAME(object),\
         object->gpu_id)); \
    _errtype = 1;\
    } \
    } while(0); \
    _errtype; \
  })

#define CHECK_NPP_STATUS(npp_status,error_str) do { \
  if ((npp_status) != NPP_SUCCESS) { \
    g_print ("Error: %s in %s at line %d: NPP Error %d\n", \
        error_str, __FILE__, __LINE__, npp_status); \
    goto error; \
  } \
} while (0)

#define CHECK_CUDA_STATUS(cuda_status,error_str) do { \
  if ((cuda_status) != cudaSuccess) { \
    g_print ("Error: %s in %s at line %d (%s)\n", \
        error_str, __FILE__, __LINE__, cudaGetErrorName(cuda_status)); \
    goto error; \
  } \
} while (0)
#endif // NVIDIA

static GstStaticPadTemplate gst_ivaplugin1_sink_template =
GST_STATIC_PAD_TEMPLATE("sink",
	GST_PAD_SINK,
	GST_PAD_ALWAYS,
#ifdef NVIDIA
	GST_STATIC_CAPS(GST_VIDEO_CAPS_MAKE_WITH_FEATURES
	(GST_CAPS_FEATURE_MEMORY_NVMM,
		"{ RGBA }")));
#else
    GST_STATIC_CAPS ("ANY"));
#endif

static GstStaticPadTemplate gst_ivaplugin1_src_template =
GST_STATIC_PAD_TEMPLATE("src",
	GST_PAD_SRC,
	GST_PAD_ALWAYS,
#ifdef NVIDIA
	GST_STATIC_CAPS(GST_VIDEO_CAPS_MAKE_WITH_FEATURES
	(GST_CAPS_FEATURE_MEMORY_NVMM,
		"{ RGBA }")));
#else
    GST_STATIC_CAPS ("ANY"));
#endif

using std::chrono::steady_clock;
struct _GstIvaPlugin1
{
	GstBaseTransform base_trans;
#ifdef NVIDIA
	cudaStream_t npp_stream;
	NvBufSurface *inter_buf;
#endif // NVIDIA
	GstVideoInfo video_info;
	guint gpu_id;

	// 通道状态
	IVAChannelState detecting = IVA_CHANNEL_PAUSED_DEFAULT;
	gint video_id;
	gint preset_id;
	gint stream_id;
    gint frame_num;

	// 事件分析
	evt::Channel* event_channel;
	std::vector<evt::EventInfo> *events;

	// 结构化绘制参数
	void *nvosd_ctx;
	bool draw_frame;
	OSDParam osdParam;
	steady_clock::time_point draw_state_time;

	// 上次视频质量状态
	FrameState last_frame_state;
	// 图像模块是否开启
	bool img_analyser_enabled;

	// 自动恢复时间 （分钟）
	guint auto_reset_time;
	// 上次检测状态变化时间
	steady_clock::time_point detecting_change_time;

	// 帧率记录
	int fps_frame_count_tmp;
	long fps_start_time_stamp;
	gboolean print_process_log;
	gint frame_log_remain;
    bool snapshot_request = false;
	// 检测状态是否初始化 默认否
	gboolean status_inited;

#ifdef NVIDIA
    // 过线视频录制
    std::shared_ptr<record::VideoRecord> videoRecordPtr = nullptr;
	std::shared_ptr<record::VideoRecordBase> videoRecordBasePtr = nullptr;
    // 过线存图

    std::atomic_bool writingImage = false;
    std::condition_variable writeImageCondition;
    std::thread writeImageThread;
    std::mutex imagesLock;
    std::mutex conditionLock;
    std::shared_ptr<std::deque<ImageToSave>> passedImages = nullptr;
    // 事件录像轨迹
    TargetTracks* targetSaveTracks;
#endif


  // 启动时是否接收到完整的参数，这里只需要收到一次restore消息即可，防止iva启动时所有通道不检测
  gboolean init_status;
};

// Boiler plate stuff
struct _GstIvaPlugin1Class
{
	GstBaseTransformClass parent_class;
};

/*
*	获取当前帧
*/
static bool capture_frame(GstIvaPlugin1* ivaplugin1, cv::Mat& output, BufSurface* ip_surf, int batch_id);

/*
*	获取当前检测目标集
*/
static void fetch_targets(GstIvaPlugin1* ivaplugin1, evt::TrackList& output, FrameMeta* ds_frame_meta, float scale_x, float scale_y);

/*
*	记录帧率
*/
static void record_fps(GstIvaPlugin1* ivaplugin1);

/*
*	物体事件检测 （抛洒物、烟火、路障等）
*/
static void update_image_object(evt::EventType evtType, IVATargetArray*& targets, evt::EventObjectList& evt_objs, float scale_x, float scale_y);

GType gst_ivaplugin1_get_type(void);

G_END_DECLS
#endif /* __GST_IVAPLUGIN1_H__ */
