#include <iostream>
#include <future>
#include "event_video_record.h"
#include "ivautils.h"
#include "log.h"
#include "protocol_manager.h"
#include "protocol_sender.h"


namespace record
{
    using namespace iva;
    using namespace std;
    using namespace std::chrono;

    std::mutex eventVideoRecordsLock;
    std::map<guint, std::shared_ptr<EventVideoRecord>> eventVideoRecords;           //!< 通道对应的录像context key:pipeline序号

    /**
     * 创建对应通道的录像context
     * @param[in] index   pipeline序号
     * @param[in] params  录像配置参数
     */
    void createEventRecord(guint index, guint before, guint after, const std::string& relativePath, NvDsSRInitParams params)
    {
        std::lock_guard lk(eventVideoRecordsLock);
        if (eventVideoRecords.find(index) == eventVideoRecords.end())
        {
            auto videoRecordPtr = std::make_shared<EventVideoRecord>(index, before, after);
            if (videoRecordPtr)
            {
                videoRecordPtr->create(params, relativePath);
                eventVideoRecords[index] = videoRecordPtr;
            }
        }
    }

    /**
     * 获取对应通道的录像context
     * @param[in] index       通道号
     */
    std::shared_ptr<EventVideoRecord> getEventVideoRecord(guint index)
    {
        std::lock_guard lk(eventVideoRecordsLock);
        return (eventVideoRecords.find(index) == eventVideoRecords.end()) ? nullptr : eventVideoRecords[index];
    }

    /**
     * @brief 注册录像开始回调，主要用于返回录像的文件名，并带上EvtVideoRecordTask参数，执行相关用户相关业务逻辑
     */
    void EventVideoRecord::registerEvtRecordCallback(EventRecordCB func)
    {
        eventRecordCb = std::move(func);
    }

    void EventVideoRecord::registerEvtRecordCompletedCallback(EventRecordCompletedCB func)
    {
        eventRecordCompletedCb = std::move(func);
    }

    /**
     * @brief 录像完成回调
     */
    void EventVideoRecord::setCompleted(guint realDuration)
    {
        VideoRecordBase::setCompleted(realDuration);

        //! 修改文件名(由于文件名是deepstream录像模块自动生成，需要根据自定义文件名重命名文件)
        auto recordTask = currentRecord;
        auto videoFilePath = fileName;
        auto f = std::async(std::launch::async, [recordTask, realDuration, videoFilePath, this](){
            std::string newFileName = recordTask.videoPath;
            std::string fullPath = std::string(WEB_SERVER_ROOT) + videoFilePath;
            std::string newPath = std::string(WEB_SERVER_ROOT) + "/" + newFileName;

            std::stringstream mvCmd;
            mvCmd << "mv " << fullPath << " " << newPath;
            system(mvCmd.str().c_str());

            eventRecordCompletedCb(newFileName, recordTask.totalDuration, realDuration, recordTask.eventInfo.targetID);
            for (auto& event : recordTask.eventTargets)
            {
                eventRecordCompletedCb(newFileName, event.totalDuration, realDuration, event.eventInfo.targetID);
            }
        });

        if (!evtVideoRecordTasks.empty())
        {
            auto future = std::async(std::launch::async,[this](){
                std::unique_lock lk(recordLock);
                guint revisedBeforeTime = 0; //!< 重新计算缓存中的beforeTime, 因为缓存中的startTime可能已经距离当前时间较长，需要更长的beforeTime
                for (auto it = evtVideoRecordTasks.begin(); it != evtVideoRecordTasks.end(); )
                {
                    auto now = std::chrono::system_clock::now();
                    revisedBeforeTime = (guint)round<seconds>(now - it->startTime).count() + beforeTime;
                    if (revisedBeforeTime > RECORD_CACHE_SIZE_SEC)   //!< 重新计算的beforeTime如果超过缓存时长，则该录像任务被延误了，无法录像
                    {
                        IVA_LOG_WARN("record task is overdue, channel {}", channel)
                        it = evtVideoRecordTasks.erase(it);
                        continue;
                    }
                    break;
                }
                if (evtVideoRecordTasks.empty())
                    return;

                auto task = evtVideoRecordTasks.front();
                if (start(revisedBeforeTime, duration, gpointer(this)))
                {
                    task.totalDuration = revisedBeforeTime + duration;
                    currentRecord = task;
                    evtVideoRecordTasks.erase(evtVideoRecordTasks.begin());
                    if (eventRecordCb != nullptr)
                    {
                        lk.unlock();
                        eventRecordCb(task.totalDuration, (int)revisedBeforeTime * 1000, task);
                    }
                }
            });
        }
    }

    /**
     * @brief 提交录像任务
     */
    bool EventVideoRecord::submit(EvtVideoRecordTask task)
    {
        std::unique_lock lk(recordLock);
        if (!isCompleted()) //!< 正在录像
        {
            //!< 在时间范围内，记录当前目标的时间
            if (task.startTime < currentRecord.startTime + std::chrono::seconds(duration - afterTime))
            {
                if (eventRecordCb != nullptr)
                {
                    int position = (int) (round <milliseconds> (task.startTime - currentRecord.startTime).count() + beforeTime * 1000); ///< 转换为ms
                    lk.unlock();
                    task.totalDuration = beforeTime + duration;
                    task.videoPath = currentRecord.videoPath;
                    eventRecordCb(task.totalDuration, position, task);
                    currentRecord.eventTargets.push_back(task);
                }
                return true;
            }
            else  //!< 不在录像时间范围内，则缓存录像任务，等当前录制结束后再从缓存中取任务进行录制
            {
                evtVideoRecordTasks.emplace_back(task);
            }
        }
        else  //!< 没有录像
        {
            if (start(beforeTime, duration, gpointer(this)))
            {
                task.totalDuration = beforeTime + duration;
                currentRecord = task;
                lk.unlock();
                if (eventRecordCb != nullptr)
                    eventRecordCb(task.totalDuration, (int)beforeTime * 1000, task);
            }
            return true;
        }
        return false;
    }
}
