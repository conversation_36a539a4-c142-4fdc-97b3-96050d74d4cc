/**
 * Project IVA (image analyser)
 */

#include <map>
#include <mutex>
#include <string>
#include "model_task.h"
#include "weather_task.h"
#include "pedestrian_classify_task.h"
#include "vehicletype_classify_task.h"
#include "vehiclecolor_classify_task.h"
#ifdef NVIDIA
#include "feature_task.h"
#endif

namespace ia {
	std::mutex mutexModel;
	std::map<ModelTaskType, ModelTaskPtr> modelTasks;

	/**
	* 获取模型任务
	* @param taskType 任务类型
	*/
	ModelTaskPtr getModelTask(ModelTaskType taskType, int deviceID)
	{
		std::lock_guard<std::mutex> m(mutexModel);
		if (modelTasks.find(taskType) != modelTasks.end())
			return modelTasks[taskType];

		ModelTaskPtr task = nullptr;
		switch (taskType)
		{
#ifdef NVIDIA
		case ModelTaskType::FeatureRetrieve:
			task = modelTasks[taskType] = std::make_shared<FeatureTask>(new FeatureAction(deviceID));
			break;
#endif
		case ModelTaskType::WeatherClassify:
			task = modelTasks[taskType] = std::make_shared<WeatherTask>(new WeatherAction(deviceID));
			break;
		case ModelTaskType::PedestrianClassify:
			task = modelTasks[taskType] = std::make_shared<MODEL_TASK_CLASS>(new PedestrianClassifyAction(deviceID));
			break;
		case ModelTaskType::VehicleTypeClassify:
			task = modelTasks[taskType] = std::make_shared<MODEL_TASK_CLASS>(new VehicleTypeClassifyAction(deviceID));
			break;
		case ModelTaskType::VehicleColorClassify:
			task = modelTasks[taskType] = std::make_shared<MODEL_TASK_CLASS>(new VehicleColorClassifyAction(deviceID));
			break;
		}
		return task;
	}
}
