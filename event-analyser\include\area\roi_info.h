/**
 * Project AI事件分析模块
 */


#ifndef _ROIINFO_H
#define _ROIINFO_H

#include <vector>
#include "region_info.h"
#include "lane_info.h"
#include "element/vector2d.h"
#include "element/line.h"
#include "feature_roi_info.h"

 /**
  * ROI配置信息
  */
namespace evt
{
	class ROIInfo : public RegionInfo{
	public:

		/**
		 * 逻辑ID
		 */
		int logicID;

		/**
		 * 配置方向
		 */
		Vector2D direction;

		/**
		 * 计数线
		 */
		Line countLine;

		/**
		 * 参考线
		 */
		Line referenceLine;

		/**
		 * 参考矩形
		 */
		Polygon referenceRect;

		/**
		 * 参考距离
		 */
		int referenceDistance;

		/**
		 * 子区域列表
		 */
		std::vector<RegionInfo> childRegions;

		/**
		 * 车道区域列表
		 */
		std::vector<LaneInfo> laneRegions;

        /**
         * 特征检测区
         */
        FeatureRoiInfo featureRoiInfo;
	};

	typedef std::vector<ROIInfo> ROIInfoList;
}
#endif //_ROIINFO_H
