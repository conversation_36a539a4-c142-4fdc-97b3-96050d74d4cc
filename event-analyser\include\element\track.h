/**
 * Project AI事件分析模块
 */


#ifndef _TRACK_H
#define _TRACK_H

#include "module_def.h"
#include <vector>
#include <map>
#include "enum/target_type.h"
#include "element/rect.h"

/**
* 轨迹帧
*/
namespace evt
{
	struct EVT_EXPORT Track {
		/**
		 * 跟踪ID
		 */
		int  id;

		/**
		 * 目标类型
		 */
		TargetType type;

        /**
         * 目标分类类型 eg: 12 对应分类器类别1，分类类型标签2
         */
        std::map<std::string, std::string> targetClassTypes;

		/**
		 * 轨迹矩形框
		 */
		Rect rect;

		/**
		 * 轨迹序号
		 */
		int index;
	};

	typedef std::vector<Track> TrackList;
}
#endif //_TRACK_H
