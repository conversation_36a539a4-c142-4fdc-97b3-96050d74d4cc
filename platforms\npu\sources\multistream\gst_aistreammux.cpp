#include "gst_aistreammux.h"
#include "gst_bufsurface_allocator.h"
#include "gst_aistreammux_prop.ixx"
#include "gst_aistreammux_init.ixx"
#include "gst_aistreampad.h"
#include "gst_ai_meta.h"
#include "bufsurface.h"
#include "vformat.h"
#include <algorithm>
#include <memory>
#include <thread>
#include "memory_pool.h"

#define MAX_DEVICE_MEM_POOL_COUNT 200
static bool setDeviceContext(GstAIStreamMux *self);
static bool acquireMemoryAndCopy(GstAIStreamMux * self, DEVICE_MEM_POOL_TYPE * &memory_pool, ai::DeviceFrame* src_frame, DeviceFramePtr& dst_frame, int deviceId);


using std::chrono::steady_clock;
struct CachedStream
{
	bool grouped = false;
	steady_clock::time_point last_time;
	std::queue<DeviceFramePtr> cached;

    DeviceFramePtr request()
	{
		if (cached.empty())
		{
			return nullptr;
		}
		else
		{
			auto frame = cached.front();
			cached.pop();
			return frame;
		}
	}

	bool cache(DeviceFramePtr frame)
	{
		cached.push(frame);
		bool full = cached.size() > MAX_CACHED_SIZE;
		while (cached.size() > MAX_CACHED_SIZE)
		{
            auto drop_frame = request();
            if (drop_frame)
            {
                auto memory_pool = static_cast<DEVICE_MEM_POOL_TYPE*>(drop_frame->handle);
                if (memory_pool)
                {
                    memory_pool->free(drop_frame->ptrs[0]);
                    memory_pool->free(drop_frame->ptrs[1]);
                    memory_pool->free(drop_frame->ptrs[2]);
                }
                else
                {
                    drop_frame->handle = nullptr;
                }
            }
            drop_frame.reset();
		}
		return full;
	}

	void dispose()
	{
		while (!cached.empty())
		{
			auto frame = request();
            frame.reset();
		}
	}
};

static gboolean gst_plugin_init(GstElement* elem)
{
	auto self = PLUGIN_CAST(elem);

	self->srcpad = gst_pad_new_from_static_template(&plugin_src_template, "src");
	GST_PAD_SET_PROXY_CAPS(self->srcpad);
	gst_element_add_pad(GST_ELEMENT(self), self->srcpad);

	self->allocator = NULL;
	self->dynamic_batchsize = self->batchsize = 1;
	self->timeout = DEFAULT_BATCH_PUSH_TIMEOUT;
    self->group_frames = std::make_shared<std::list<DeviceFramePtr>>();
	self->pad_indexes = g_hash_table_new(NULL, NULL);
	self->stream_buffers = new CachedStreams();
	self->timer = g_timer_new();
    self->context = nullptr;
	return TRUE;
}

static void gst_plugin_finalize(GObject* object)
{
	auto self = PLUGIN_CAST(object);
	G_OBJECT_CLASS(parent_class)->finalize(object);

	g_timer_destroy(self->timer);
	if (self->allocator)
		gst_object_unref(self->allocator);
    if (self->group_frames)
    {
        self->group_frames->clear();
        self->group_frames.reset();
    }

	if (self->pad_indexes)
		g_hash_table_unref(self->pad_indexes);

	if (self->stream_buffers)
	{
		for (const auto&[index, stream] : *self->stream_buffers)
		{
			stream->dispose();
		}
		delete self->stream_buffers;
	}
    if (self->context)
        destroyContext(self->context);
}

static GstFlowReturn gst_plugin_chain(GstPad* pad, GstObject* parent, GstBuffer* buffer)
{
	auto self = PLUGIN_CAST(parent);
    DeviceFrameListPtr grouped_batch = nullptr;
    DEVICE_MEM_POOL_TYPE* memory_pool = nullptr;
	auto time_now = steady_clock::now();
	{
		guint index = GST_STREAM_PAD_CAST(pad)->index;
		GstMapInfo in_map_info;
		memset(&in_map_info, 0, sizeof(in_map_info));
		if (!gst_buffer_map(buffer, &in_map_info, GST_MAP_READ))
		{
			GST_ERROR_OBJECT(self, " Failed to map gst buffer");
			return GST_FLOW_ERROR;
		}
		auto frame = (ai::DeviceFrame*)in_map_info.data;
		frame->stream_id = index;
		gst_buffer_unmap(buffer, &in_map_info);

        auto device_frame = std::make_shared<ai::DeviceFrame>();
        *device_frame = *frame;
        for (auto& ptr : device_frame->ptrs)
            ptr = nullptr;
        device_frame->user_data = nullptr;

        bool isAcquired = false;
        for (int tryCount = 0; tryCount < 5 && !isAcquired; ++tryCount)
        {
            isAcquired = acquireMemoryAndCopy(self, memory_pool, frame, device_frame, frame->device_id);
            if (!isAcquired)
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        if (!isAcquired)
        {
            if (strcmp(in_map_info.memory->allocator->mem_type, "DeviceFrameMemory") == 0)
                frame->handle = nullptr;  ///< 内存由转换插件分配的, 让转换插件立即释放
            gst_buffer_unref(buffer);
#ifdef CAMBRICON_MLU370
//            GST_ERROR_OBJECT(self, "Exiting process due to memory error\n" );
//            exit(0);
            //GstStructure *s = gst_structure_new("mux-flush-request", "reason", G_TYPE_STRING, "queue-overflow", NULL);
            //gst_pad_push_event(self->srcpad, gst_event_new_flush_start());
            //gst_pad_push_event(self->srcpad, gst_event_new_flush_stop(TRUE));

            GstStructure *s = gst_structure_new_empty("mux-flush-request");
            GstMessage *msg = gst_message_new_element(GST_OBJECT(self), s);
            gst_element_post_message(GST_ELEMENT(self), msg);
#endif
            GST_ERROR_OBJECT(self, "Device memory unavailable, flush requested downstream.");
            return GST_BASE_TRANSFORM_FLOW_DROPPED;
        }

        if (strcmp(in_map_info.memory->allocator->mem_type, "DeviceFrameMemory") == 0)
            frame->handle = nullptr;  ///< 内存由转换插件分配的, 让转换插件立即释放
        gst_buffer_unref(buffer);

        auto buffers_len = self->group_frames->size();
		if (buffers_len == 0)
			g_timer_start(self->timer);

		std::lock_guard<std::mutex> guard(self->mutex_);
        auto it = self->stream_buffers->try_emplace(index, std::make_shared<CachedStream>()).first;
        auto cached_stream = it->second;

		cached_stream->last_time = time_now;
        if (cached_stream->cache(std::move(device_frame)))
        {
            GST_WARNING_OBJECT(self, "Stream %d batch cache full", index);
        }

        /// 从缓存中取出一帧，放入group_frames中组batch，并标记该通道正在被组batch
		if (!cached_stream->grouped)
		{
            self->group_frames->emplace_back(std::move(cached_stream->request()));
			cached_stream->grouped = true;
		}

        /// 统计有多少通道在1秒内有帧出现，长时间没有帧出现的通道，将不会被组batch，只组realtime_size个帧即可
		int realtime_size = 0;
		for (const auto& [index, stream] : *self->stream_buffers)
		{
			auto passed = std::chrono::duration_cast<std::chrono::seconds>(time_now - stream->last_time);
			if (passed.count() < 1)
			{
				realtime_size++;
			}
		}
		self->dynamic_batchsize = realtime_size;
        buffers_len = self->group_frames->size();

        /// 在没有组batch的通道缓存中取出一帧放入group_frames中组batch，并标记该通道正在被组batch
		for (const auto& [index, stream] : *self->stream_buffers)
		{
			if (buffers_len >= static_cast<guint>(self->dynamic_batchsize))
				break;

			if (!stream->grouped && !stream->cached.empty())
			{
                self->group_frames->emplace_back(stream->request());
                buffers_len = self->group_frames->size();
				stream->grouped = true;
			}
		}

        /// 超时或者group_frames要组的帧数满足，将group_frames中的帧组成batch，将未组batch的缓存帧的grouped标记为false，等待下一次组batch
		gdouble elapsed = 1000000.0 * g_timer_elapsed(self->timer, NULL);
		if (buffers_len >= static_cast<guint>(self->dynamic_batchsize) || elapsed >= self->timeout)
		{
            grouped_batch = self->group_frames;
            self->group_frames = std::make_shared<std::list<DeviceFramePtr>>();

			g_timer_stop(self->timer);

			for (const auto& [index, stream] : *self->stream_buffers)
			{
				stream->grouped = false;
			}
		}
	}

    if (grouped_batch && !grouped_batch->empty())
	{
        int batchnum = (int)grouped_batch->size();

		if (!self->allocator)
			self->allocator = gst_bufsurface_allocator_new();

		GstBuffer*  buffer = gst_buffer_new();
		BufSurface* bufsurface = new BufSurface();
		bufsurface->batchSize = batchnum;
		bufsurface->memType = BUF_MEM_DEVICE;
		bufsurface->surfaceList = new BufSurfaceParams[batchnum]();

        BatchMeta* batch_meta = create_batch_meta(batchnum);
        AIMeta* meta = gst_buffer_add_ai_meta(buffer, batch_meta, NULL, batch_meta_copy_func, batch_meta_release_func);
        meta->meta_type = BATCH_GST_META;

		std::vector<std::pair<int, std::string>> debug_infos;
		for (guint idx=0; idx < batchnum; idx++)
		{
            auto frame = grouped_batch->front();
            grouped_batch->pop_front();

			bufsurface->gpuId = frame->device_id;
			auto& surface = bufsurface->surfaceList[idx];
			surface.buf_id = frame->buf_id;
			surface.stream_id = frame->stream_id;
			surface.frame_num = frame->frame_num;
			surface.pts = frame->pts; //frame->pts;
			surface.width = frame->width;
			surface.height = frame->height;
			surface.frame_size = frame->frame_size;
			surface.colorFormat = getSurfaceFmt(frame->pformat);
			surface.n_planes = frame->n_planes;
			memcpy(surface.strides, frame->strides, sizeof(frame->strides));
			memcpy(surface.ptrs, frame->ptrs, sizeof(frame->ptrs));

            FrameMeta *frame_meta = (FrameMeta *)g_malloc0(sizeof(FrameMeta));
            frame_meta->bInferDone = false;
            frame_meta->num_obj_meta = 0;
            frame_meta->source_id = surface.stream_id;
            frame_meta->frame_num = surface.frame_num;
            frame_meta->source_frame_width = surface.width;
            frame_meta->source_frame_height = surface.height;
            add_frame_meta_to_batch(batch_meta, frame_meta);

			char profiler[64];
			sprintf(profiler, " %d:%d", surface.stream_id, frame_meta->frame_num);
			debug_infos.emplace_back(surface.stream_id, profiler);
		}

		static int print_index = 0;
		if (print_index++ % 120 == 0) // Debug batch info
		{
			std::sort(debug_infos.begin(), debug_infos.end(), [](auto& a, auto& b) {return a.first < b.first; });
			std::string debug_info_str = " |";
			for (auto& info : debug_infos)
			{
				debug_info_str += info.second;
			}
			if (batchnum >= self->dynamic_batchsize)
			{
				debug_info_str += " |";
			}

			GST_WARNING_OBJECT(self, "batch(%d / %d):%s\n", self->dynamic_batchsize, self->batchsize, debug_info_str.c_str());
		}
		
		// Push next
        grouped_batch->clear();
		auto mem = gst_bufsurface_allocator_alloc(self->allocator, bufsurface, memory_pool);
		gst_buffer_replace_all_memory(buffer, mem);

		return gst_pad_push(self->srcpad, buffer);
	}
	else
	{
		return GST_BASE_TRANSFORM_FLOW_DROPPED;
	}
}

static GstPad* gst_plugin_request_new_pad(GstElement* element, GstPadTemplate* templ, const gchar* name, const GstCaps* caps)
{
	auto self = PLUGIN_CAST(element);
	guint index = 0;
	if (name && sscanf(name, "sink_%u", &index) == 1)
	{
		GST_LOG_OBJECT(element, "name: %s (index %d)", name, index);
		if (g_hash_table_contains(self->pad_indexes, GUINT_TO_POINTER(index))) {
			GST_ERROR_OBJECT(self, "pad name %s is not unique", name);
			return NULL;
		}
	}
	else
	{
		GST_ERROR_OBJECT(self, "pad name %s is not satisfied", name);
		return NULL;
	}

	g_hash_table_insert(self->pad_indexes, GUINT_TO_POINTER(index), NULL);
	GstPad* pad = GST_PAD_CAST(g_object_new(GST_TYPE_STREAM_PAD,
							"name", name, "direction", templ->direction, "template", templ, NULL));
	GST_STREAM_PAD_CAST(pad)->index = index;
	GST_OBJECT_FLAG_SET(pad, GST_PAD_FLAG_PROXY_CAPS);
	gst_pad_set_chain_function(pad, GST_DEBUG_FUNCPTR(gst_plugin_chain));
	gst_element_add_pad(element, pad);
	return pad;
}

static void gst_plugin_release_pad(GstElement* element, GstPad* pad)
{
	gst_element_remove_pad(element, pad);
}

static bool setDeviceContext(GstAIStreamMux *self, int deviceId)
{
    auto ret = Dev_RET_SUCCESS;
    if (self->context == nullptr)
    {
        ret = createContext(&self->context, deviceId);
        if (ret != Dev_RET_SUCCESS)
        {
            GST_ERROR_OBJECT(self, "failed create device context, error code %d \n", ret);
            return false;
        }
    }
    else
    {
        ContextHandle context = nullptr;
        getCurrContext(&context);
        if (context != self->context)
        {
            ret = setCurrContext(self->context);
            if (ret != Dev_RET_SUCCESS)
            {
                GST_ERROR_OBJECT(self, "failed set current context, error code %d \n", ret);
                return false;
            }
        }
    }
    return true;
}

static bool acquireMemoryAndCopy(GstAIStreamMux * self, DEVICE_MEM_POOL_TYPE * &memory_pool, ai::DeviceFrame* src_frame, DeviceFramePtr& dst_frame, int deviceId)
{
    if (self == nullptr || src_frame == nullptr || dst_frame == nullptr || src_frame->frame_size != dst_frame->frame_size)
    {
        GST_ERROR_OBJECT(self, "failed to acquire memory and copy, invalid parameter\n");
        return false;
    }

    if (!setDeviceContext(self, deviceId))
        return false;

    if (!memory_pool)
    {
        memory_pool = DEVICE_MEM_POOL_TYPE::getDeviceMemPoolInstance(dst_frame->frame_size, MAX_DEVICE_MEM_POOL_COUNT * self->batchsize);
        if (!memory_pool)
        {
            GST_ERROR_OBJECT(self, "failed to get memory pool instance\n");
            return false;
        }
    }

    for (size_t i = 0; i < DEVICE_MAXIMUM_PLANE; ++i)
    {
        if (src_frame->ptrs[i] == nullptr)
            continue;

        dst_frame->ptrs[i] = memory_pool->allocate(dst_frame->frame_size);
        if (!dst_frame->ptrs[i])
        {
            // 内存分配失败，清理已分配的内存
            for (size_t j = 0; j < i; ++j)
            {
                if (dst_frame->ptrs[j])
                {
                    memory_pool->free(dst_frame->ptrs[j]);
                    dst_frame->ptrs[j] = nullptr;
                }
            }
            dst_frame->handle = nullptr;

            static time_t last_print_time = 0;
            time_t curr_time = time(NULL);
            if (curr_time - last_print_time > 300) ///< 5分钟打印一次日志
            {
                last_print_time = curr_time;
                char time_str[64] = {0};
                strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", localtime(&curr_time));
                GST_ERROR_OBJECT(self, "failed to allocate memory from memory pool (alloc size %lu , used count %zu) %s\n", dst_frame->frame_size, memory_pool->getUsedMemoryCount(), time_str);
            }
            return false;
        }
        dst_frame->handle = memory_pool; ///< 设备内存由内存池管理，不需要释放
        auto ret = devMemcpy(dst_frame->ptrs[i], src_frame->ptrs[i], src_frame->frame_size, DEV_MEM_TRANS_DIR_DEV2DEV);
        if (ret != Dev_RET_SUCCESS)
        {
            GST_ERROR_OBJECT(self, "failed to copy memory, error code %d \n", ret);
            return false;
        }
    }
    return true;
}
