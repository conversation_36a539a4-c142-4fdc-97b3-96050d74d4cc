cmake_minimum_required (VERSION 3.5.2)

## 目标生成
set(TARGET_LIBRARY "ivacommon")
# 输出目录
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/out/lib)

add_compile_options(-std=c++17 -fPIC -fstack-protector-all -Wno-unknown-pragmas -Wno-unused-variable -Werror=return-type -Wall)

# boost
set(BOOST_HOME "/opt/boost")


# 头文件
include_directories(
    ${BOOST_HOME}/include/
    ${PROJECT_SOURCE_DIR}/iva-log
    ${PROJECT_SOURCE_DIR}/ini
    ${OpenCV_INCLUDE_DIRS}
)

# 库路径
link_directories(
    ${LIBRARY_OUTPUT_PATH}
)

FILE(GLOB src "*.cpp")

#file(GLOB_RECURSE sources CONFIGURE_DEPENDS "*.cpp")
SET(ALL_SRC ${include} ${src} )

# 生成动态库
ADD_LIBRARY(${TARGET_LIBRARY} SHARED ${ALL_SRC})
target_link_libraries(${TARGET_LIBRARY} ${OpenCV_LIBS} ivalog)

