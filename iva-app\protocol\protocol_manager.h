/**
 * Project IVA
 */

#pragma once
#include <atomic>
#include <boost/serialization/singleton.hpp>
#include <boost/signals2.hpp>
#include "protocol/all.h"
#include "protocol/fvm.h"
#include "network_type.h"


/**
 * @brief: 协议管理器
 */
namespace iva::protocol
{

    #define PROTOCOL_SIGNAL(T) boost::signals2::signal<void(T)>

    class ProtocolManager :public boost::noncopyable
    {

    public:
        /**
         * 初始化（启动服务端及相关客户端）
         * @param processID 进程号
         */
        bool init(int processID);

		/**
		 * 销毁
		 */
		void dispose();

        /**
         * 设置ip相关参数
         */
        void setSystemIp(const std::string& localIp, const std::string& platIp);

        /**
         * 发送消息至IVA
         * @param msgType 消息类型
         * @param data 消息内容
         * @param processID 进程ID 默认为进程1
         */
        template<typename T>
        bool sendToFVM(network::ProtocolType msgType, const T& data, int processID= network::DEFAULT_CLIENT_ID);

        /**
         * 发送消息至WEB
         * @param msgType 消息类型
         * @param data 消息内容
         * @param isPlatform 是否平台 默认本地
         */
        template<typename T>
        bool sendToWEB(network::ProtocolType msgType, const T& data, bool isPlatform = false);

        /**
         * 发送数据至WEB通过udp
         * @param msgType 消息类型
         * @param data 消息内容
         */
        template<class T>
        bool sendToWEBViaUDP(network::ProtocolType msgType, const T &data);

        /**
         * 发送数据 （默认UDP）
         * @param host 目标地址
         * @param port 目标端口
         * @param msgType 消息类型
         * @param data 消息内容
         */
        template<typename T>
        bool sendTo(const std::string& host, unsigned short port, network::ProtocolType msgType, const T& data);

        inline std::string getLocalIP(){return localIP;};
        inline std::string getPlatIP(){return platIP;};
        inline std::string getWebURL(){ return webURL.empty() ? "http://127.0.0.1:8080/" : webURL;};

    public: //SIGNALs
        PROTOCOL_SIGNAL(network::ResetChannel) onResetChannel;
        PROTOCOL_SIGNAL(network::SystemConfig) onSystemConfig;
        PROTOCOL_SIGNAL(network::AlgorithmParam) onAlgorithmParam;
        PROTOCOL_SIGNAL(network::VecChannelBasicConf) onChannelBasicConf;
        PROTOCOL_SIGNAL(network::ChannelDetectParam) onChannelDetectParam;
        PROTOCOL_SIGNAL(network::ChannelRestoreConf) onChannelRestore;
        PROTOCOL_SIGNAL(network::ChannelPauseConf) onChannelPause;
        PROTOCOL_SIGNAL(network::RequestIvaVideo) onRequestIvaVideo;

    private:
        /**
         * 消息数据处理
         * @param msgData 消息内容
         */
        void handleProtocolReceived(std::string& msgData);

        /**
         * 初始化web客户端
         */
        void initWEBClient();
    private:
        std::string localIP;
        std::string platIP;
        std::string webURL;
        std::atomic_bool initWebClient = false;
        std::atomic_bool initUdpSession = false;
    };

    typedef boost::serialization::singleton<ProtocolManager> SingletonProtocolManager;
    #define PROTOCOL_MANAGER SingletonProtocolManager::get_mutable_instance()

}
#include "protocol_manager.ixx"








