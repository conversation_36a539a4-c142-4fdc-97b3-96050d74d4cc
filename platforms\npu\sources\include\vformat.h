#pragma once
#include <cstdint>
#include <iostream>
#include <string.h>
#include "devmemory.h"

#define GST_CAPS_FEATURE_DEVICE_MEMORY "memory:mlu"
#define GST_SUPPORT_VIDEO_FORMATS "{ NV12 }"
#define GST_CONVERT_VIDEO_FORMATS "{ NV12, RGB, RGBA }"

#define DEVICE_MAXIMUM_PLANE 3

namespace ai
{
	/**
		@brief Enumeration to describe image colorspace.
	*/
	enum class PixelFmt
	{
		NV12 = 0,  ///< NV12, YUV family
		NV21,      ///< NV21, YUV family
		I420,
		YV12,
		YUYV,
		UYVY,
		YVYU,
		VYUY,
		P010,
		YUV420_10BIT,
		YUV444_10BIT,
		ARGB,
		ABGR,
		BGRA,
		RGBA,
		AYUV,
		RGB565,
		RAW,  ///< No format
		BGR24,
		RGB24,
		I010,
		<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
		TOTAL_COUNT
	};

	/**
		@brief Enumeration to describe data codec type
		@note Type contains both video and image
	*/
	enum class CodecType
	{
		MPEG2 = 0,
		MPEG4,  ///< MPEG4 video codec standard
		H264,   ///< H.264 video codec standard
		H265,   ///< H.265 video codec standard, aka HEVC
		VP8,
		VP9,
		AVS,
		MJPEG,  ///< Motion JPEG video codec standard
		JPEG    ///< JPEG image format
	};

	/**
		@brief Structure contains raw data and informations
		@note Used as output in decode and input in encode
	*/
	struct DeviceFrame
	{
		/**
			Used to release buffer in EasyDecode::ReleaseBuffer
			when frame memory from decoder will not be used. Useless in encoder.
		*/
		uint64_t buf_id{ 0 };
		/// stream id
		unsigned int stream_id{ 0 };
		/// frame num
		uint64_t frame_num{ 0 };
		/// Presentation time stamp
		uint64_t pts{ 0 };
		/// Frame height in pixel
		uint32_t height{ 0 };
		/// Frame width in pixel
		uint32_t width{ 0 };
		/// Frame data size, unit: byte
		uint64_t frame_size{ 0 };
		/// Frame color space, @see edk::PixelFmt
		PixelFmt pformat{ PixelFmt::NV12 };
		/// MLU device identification, -1 means data is on cpu
		int device_id{ -1 };
		/// MLU channel in which memory stored
		int channel_id{ 0 };
		/// Plane count for this frame
		uint32_t n_planes{ 0 };
		/// Frame strides for each plane
		uint32_t strides[DEVICE_MAXIMUM_PLANE]{ 0, 0, 0 };
		/// Frame data pointer
		void* ptrs[DEVICE_MAXIMUM_PLANE]{ nullptr, nullptr, nullptr };
		/// get user data passed to codec, only support on Mlu300 decoder
		void* user_data{ nullptr };
		/// handle
		void* handle = nullptr;
		/// lock DeviceFrame
		//std::mutex mtxlock;
		uint64_t tick{0};


		~DeviceFrame()
		{
			if (handle == nullptr)
			{
				//std::lock_guard lock(mtxlock);
				// 增加安全性检查，避免释放空指针或无效指针
				for (uint32_t i = 0; i < DEVICE_MAXIMUM_PLANE; ++i)
				{
					if (this->ptrs[i] != nullptr)
					{
						devRet_t ret = devFree(this->ptrs[i]);
						if (ret != Dev_RET_SUCCESS)
						{
							// 记录释放失败的情况，但不抛出异常（析构函数中）
							std::cerr << "[DeviceFrame] Failed to free device memory at plane "
									  << i << ", ptr: " << this->ptrs[i]
									  << ", error code: " << ret << std::endl;
						}
						this->ptrs[i] = nullptr;
					}
				}
			}
		};

		uint32_t GetPlaneSize(uint32_t plane) const
		{
			if (plane >= DEVICE_MAXIMUM_PLANE)
			{
				std::cerr << "[DeviceFrame] Plane index (" << plane << ") out of range (" << DEVICE_MAXIMUM_PLANE << ")" << std::endl;
				return 0;
			}
			uint32_t plane_size = 0;
			if (pformat == PixelFmt::NV12 || pformat == PixelFmt::NV21 || pformat == PixelFmt::I420 ||
				pformat == PixelFmt::YV12 || pformat == PixelFmt::P010)
				plane_size = plane == 0 ? (strides[plane] * height) : (strides[plane] * (height >> 1));
			else
				plane_size = strides[plane] * height;
			return plane_size;
		}

 		DeviceFrame* clone()
        {
            //std::lock_guard lock(mtxlock);
            ai::DeviceFrame* dst = new ai::DeviceFrame(*this);
            //memcpy(dst, this, sizeof(ai::DeviceFrame));

            for (uint32_t i = 0; i < n_planes; i++)
            {
                size_t srcSize = this->GetPlaneSize(i);
                dst->ptrs[i] = nullptr;
				devMallocWithId(device_id, (void**) & (dst->ptrs[i]), srcSize);
				devMemcpy(dst->ptrs[i], this->ptrs[i], srcSize, DEV_MEM_TRANS_DIR_DEV2DEV);
			}
			dst->handle = nullptr;
			return dst;
		}
	};
}
