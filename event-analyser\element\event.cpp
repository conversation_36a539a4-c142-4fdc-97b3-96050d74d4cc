/**
 * Project AI事件分析模块
 */

#include "event.h"
#include "util/scene_utility.h"

/**
 * Event implementation
 * 
 * 事件
 */
namespace evt
{
	Event::Event():target(NULL), confirmState(EventConfirm_None),
		periodCheckCount(0), periodPassedCount(0),
		stateLife(0.f), padding(0.f), detector(NULL), ignored(false)
	{
		this->info.occurTime = 0;
		this->info.removeTime = 0;
	}

	/**
	 * @param id
	 * @param type
	 * @param state
	 */
	Event::Event(std::string _id, EventType _type, EventState _state, Rect _rect): Event(){
		this->info.id = _id;
		this->info.type = _type;
		this->setState(_state);
		this->updateOccurRect(_rect);
	}

	/**
	 * 切换事件状态
	 * @param state
	 */
	void Event::setState(EventState _state) {
		this->info.state = _state;
		if (_state == EventState_Confirming)
		{
			setOccurTime(systemTimestamp());
			if (this->target != NULL)
				this->target->updateEvent(this);
		}
	}

	/**
	 * 设置事件子状态
	 * @param state
	 */
	void Event::setConfirmState(EventConfirmState _state) {
		this->confirmState = _state;
	}

	/**
	 * 设置发生时间
	 * @param t
	 */
	void Event::setOccurTime(long long t) {
		this->info.occurTime = t;
	}

	/**
	 * 设置移除时间
	 * @param t
	 */
	void Event::setRemoveTime(long long t) {
		this->info.removeTime = t;
	}

	/**
	 * 设置状态持续时间（秒）
	 * @param life
	 */
	void Event::setStateLife(float life) {
		this->stateLife = life;
	}

	/**
	 * 增加状态持续时间（秒）
	 * @param life
	 */
	void Event::addStateLife(float life)
	{
		this->stateLife += life;
	}

	/**
	 * 更新事件区域
	 * @param r
	 */
	void Event::updateOccurRect(Rect r) {
		this->info.occurRect = r;
	}


	/**
	 * 更新事件区域 多边形框 (拥堵显示子区域)
	 * @param p
	 */
	void Event::updateOccurArea(Polygon p)
	{
		this->info.occurArea = p;
	}

	void Event::updateExtraRect(std::vector<Rect> rts)
	{
		this->info.extraRects = rts;
	}

	void Event::insertExtraRect(Rect rt)
	{
		this->info.extraRects.emplace_back(rt);
	}
	/**
	 * 更新事件图像特征
	 * @param mat
	 */
	void Event::updateThumb(cv::Mat mat) {
		this->occurThumb = mat;
	}

	/**
	 * 区间检查计数
	 */
	void Event::setPeriodCheckCount(int val)
	{
		this->periodCheckCount = val;
	}

	int Event::getPeriodCheckCount()
	{
		return periodCheckCount;
	}

	/**
	 * 区间计数通过
	 */
	void Event::setPeriodPassCount(int val)
	{
		this->periodPassedCount = val;
	}

	int Event::getPeriodPassCount()
	{
		return periodPassedCount;
	}

	/**
	 * 设置事件检查（影响）额外距离
	 * @param r
	 */
	void Event::setPadding(float pad)
	{
		this->padding = pad;
	}

	/**
	 * 获取当前外接矩形框 (通过 SceneObject 继承)
	 */
	Rect Event::bound()
	{
		auto baseRect = this->info.occurRect.isValid() ? this->info.occurRect : this->info.occurArea.boundingBox();
		return Rect(baseRect.x- padding, baseRect.y- padding, baseRect.width+ 2* padding, baseRect.height+ 2 * padding);
	}

	/**
	 * 设置事件目标
	 */
	 void Event::setTarget(TargetPtr t)
	 {
		 if (this->target != NULL && this->target != t)
		 {
			 this->target->updateEvent(NULL);
		 }

		 this->target = t;
		 if (t != NULL)
		 {
			 this->info.targetID = t->getID();
			 this->info.targetType = t->getType();
             this->info.targetClassTypes = t->getClassTypes();
			 t->updateEvent(this);
		 }
		 else
		 {
			 this->info.targetID = 0;
		 }
	 }

	 /**
	  * 获取事件目标
	  */
	 TargetPtr Event::getTarget()
	 {
		 return this->target;
	 }
}
