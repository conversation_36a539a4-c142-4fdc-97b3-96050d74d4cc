#pragma once

#include <string>
#ifdef NVIDIA
#include <cuda_runtime_api.h>
#elif CAMBRICON
#include <cndev.h>
#elif HUAWEI
#include "acl/acl.h"
#endif

namespace iva
{
	struct DeviceInfo
	{
		int id = -1;
		int status = 0;
		std::string name;
	};

#ifdef NVIDIA
	static DeviceInfo getDeviceInfo(int deviceId=0)
	{
		DeviceInfo info;
		struct cudaDeviceProp prop {};
		auto cudaError = cudaGetDeviceProperties(&prop, deviceId);

		info.id = deviceId;
		info.name = prop.name;
		info.status = static_cast<int>(cudaError);
		return info;
	}
#elif CAMBRICON
	static DeviceInfo getDeviceInfo(int deviceId = 0)
	{
		DeviceInfo info; 
		cndevCardName_t nameInfo;
		nameInfo.version = CNDEV_VERSION_5;
		cndevRet_t ret = cndevInit(0);
		if (CNDEV_SUCCESS != ret) {
			printf("cndev init failed: %s.\n", cndevGetErrorString(ret));
			info.status = -1;
			return info;
		}

		auto cndevError = cndevGetCardName(&nameInfo, deviceId);
		info.id = deviceId;
		info.status = static_cast<int>(cndevError);
		info.name = info.status ? "unknown" : cndevGetCardNameString(nameInfo.id);
		cndevRelease();
		return info;
	}
#elif HUAWEI
	static DeviceInfo getDeviceInfo(int deviceId = 0)
	{
		DeviceInfo info;
        info.id = deviceId;
        auto socName = aclrtGetSocName();
        info.status = (socName == nullptr) ? 1 : 0;
        info.name = socName;
		return info;
	}
#endif
}



