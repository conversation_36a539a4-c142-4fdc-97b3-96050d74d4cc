#include <iostream>
#include <future>
#include <utility>
#include <stdlib.h>
#include "video_record.h"
#include "log.h"
#include "module_option.h"
#include "ivaconfig.hpp"

using namespace std;
using namespace std::chrono;

namespace record
{

    std::mutex videoRecordsLock;
    std::map<guint, std::shared_ptr<VideoRecord>> videoRecords;           //!< 通道对应的录像context key:pipeline序号
    std::string videoRelativePath;

    /**
     * 创建对应通道的录像context
     * @param[in] index   pipeline序号
     * @param[in] params  录像配置参数
     */
    void create(guint index, guint before, guint after, const std::string& relativePath, NvDsSRInitParams params)
    {
        std::lock_guard lk(videoRecordsLock);
        videoRelativePath = relativePath;
        if (videoRecords.find(index) == videoRecords.end())
        {
            auto videoRecordPtr = std::make_shared<VideoRecord>(index, before, after);
            if (videoRecordPtr)
            {
                videoRecordPtr->create(params);
                videoRecords[index] = videoRecordPtr;
            }
        }
    }

    /**
     * 获取对应通道的录像context
     * @param[in] index       通道号
     */
    std::shared_ptr<VideoRecord> getVideoRecord(guint index)
    {
        std::lock_guard lk(videoRecordsLock);
        return (videoRecords.find(index) == videoRecords.end()) ? nullptr : videoRecords[index];
    }

    /**
     * 创建录像context, 设置录像参数
     * @param params
     */
    void VideoRecord::create(NvDsSRInitParams params)
    {
        if (recordContext)
            return;
        duration = SETTINGS->recordDuration();
        params.callback = onComplete;
        if (NvDsSRCreate(&recordContext, &params) != NVDSSR_STATUS_OK)
        {
            IVA_LOG_ERROR ("Failed to create smart record bin, channel :{}", channel)
            return;
        }
    }

    /**
     * 销毁所有通道录像context
     */
    void VideoRecord::destroy()
    {
        if (recordContext)
        {
            if (NvDsSRDestroy (recordContext) != NVDSSR_STATUS_OK)
                IVA_LOG_WARN("Unable to destroy recording instance of channel {}", channel)
        }
    }

    /**
     * 开启录像
     * @param[in]  before     相对于当前时刻之前的时间
     * @param[in]  duration   录制持续时长
     * @param[out] position   如果是过线录像任务，则表示过线目标在当前录像的时间位置。如果只是单纯录像，该参数可忽略
     * @param[in]  task       过线录像任务，如果只是单纯录像，该参数可忽略
     * @return     成功开启录像任务，则返回true, 否则 false
     */
    bool VideoRecord::start(guint before, guint duration, int& position, const std::optional<VideoRecordTask>& task)
    {
        if (!recordContext->recordOn && completed)
        {
            if (task.has_value())
                currentRecord = task.value();

            this->duration = duration;
            NvDsSRSessionId sessId = 0;
            if (NvDsSRStart (recordContext, &sessId, before, duration, (gpointer)this) != NVDSSR_STATUS_OK)
            {
                IVA_LOG_ERROR("Unable to start video recording on channel {}", channel)
                return false;
            }
            completed = false;
            gchar *location;
            g_object_get(G_OBJECT(recordContext->filesink), "location", &location, NULL);
            std::string fullPath = location;
            auto pos = fullPath.find_last_of('/');
            if(pos != std::string::npos)
                fileName = videoRelativePath + fullPath.substr(pos, fullPath.size() - 1);
            else
                IVA_LOG_ERROR ("invalid file path : {}", location);

            position = (int)before;
            return true;
        }
        return false;
    }

    /**
     * 停止录像
     */
    bool VideoRecord::stop()
    {
        if (recordContext && recordContext->recordOn && !completed)
        {
            if (NvDsSRStop (recordContext, 0) != NVDSSR_STATUS_OK)
            {
                IVA_LOG_ERROR ("Unable to stop recording", channel)
                return false;
            }
        }
        return true;
    }

    /**
     * 提交录像任务到任务队列
     * @param info       过线信息
     * @param position   如果当前录像正在录像，且录像任务的起始时间在该录像时长内，则之间返回当前目标相对于录像文件的时间位置
     * @return 正在录像则返回true，将过线信息提交到任务队列中；如果当前需要开启新录像，则返回false
     */
    bool VideoRecord::submit(const PassedTargetInfo& info)
    {
        std::unique_lock lk(recordLock);
        if (recordContext->recordOn && !completed) //!< 正在录像
        {
            //!< 在时间范围内，记录当前目标的时间
            if (info.startTime < currentRecord.start.startTime + std::chrono::seconds(duration - afterTime))
            {
                if (opt::getOptionEnabled(opt::VIDEO_RECORD_OPT))
                    currentRecord.passedTargets.emplace_back(info); //!< 用于日志记录每个目标的录制时间点，调试用

                if (onRecordCallback != nullptr)
                {
                    int position = (int) (round <seconds> (info.startTime - currentRecord.start.startTime).count() + beforeTime);
                    lk.unlock();
                    onRecordCallback(fileName, position, info);
                }
                return true;
            }
            else  //!< 不在录像时间范围内，停止当前录像，准备从新开始新的录像
            {
                if (NvDsSRStop(recordContext, 0) != NVDSSR_STATUS_OK)
                    IVA_LOG_ERROR ("Unable to stop recording, channel {}", channel)

                if (videoRecordTasks.empty())
                    videoRecordTasks.emplace_back(VideoRecordTask{info,{}});   
                else
                    videoRecordTasks.front().passedTargets.emplace_back(info); //!< 已有录像任务，新的目标添加到当前录像任务的“只上报”队列中
            }
        }
        else  //!< 没有录像
        {
            if (videoRecordTasks.empty())
            {
                int position = 0;
                if (start(beforeTime, duration, position, VideoRecordTask{info, {}}))
                {
                    lk.unlock();
                    if (onRecordCallback != nullptr)
                        onRecordCallback(this->fileName, position, info);
                }
            }
            else
            {
                videoRecordTasks.front().passedTargets.emplace_back(info);
            }
        }
        return false;
    }

    /**
     * 注册录像回调，成功开启录像或者录像正在进行时，调用该回调，返回当前录像的文件信息
     * @param[in] func      用户回调
     */
    void VideoRecord::registerRecordCallback(RecordCallback func)
    {
        onRecordCallback = std::move(func);
    }



    /**
     * 返回录像是否完成，即是否空闲，可开启新录像
     * @note 录像接口不支持并发，必须等到录像完成且调用完成回调后，才能开启新录像
     */
    bool VideoRecord::isCompleted()
    {
        return (!recordContext->recordOn && completed);
    }

    /**
     * 设置录像完成标志位，并调用用户的录像完成回调
     */
    void VideoRecord::setCompleted()
    {
        completed = true;

        //! @note 部分录制的mp4文件用chrome打不开，需要将相关视频格式信息移到mp4文件头部
        //! @see https://superuser.com/questions/819079/ffmpeg-transcoded-h-264-video-does-not-play-in-chrome https://bbs.huaweicloud.com/blogs/303900
        auto f = std::async(std::launch::async,[=](){
            std::string fullPath = std::string(WEB_SERVER_ROOT) + fileName;

            std::stringstream convertCmd;
            convertCmd << "ffmpeg -i " << fullPath << " -c copy -movflags +faststart -loglevel quiet " << "tmp_" <<  channel << ".mp4 " << "-y";
            system(convertCmd.str().c_str());

            std::stringstream mvCmd;
            mvCmd << "mv " << "tmp_" <<  channel << ".mp4 " << fullPath;
            system(mvCmd.str().c_str());
        });

        if (!videoRecordTasks.empty())
        {
            auto future = std::async(std::launch::async,[this](){
                std::unique_lock lk(recordLock);
                guint revisedBeforeTime = 0; //!< 重新计算缓存中的beforeTime, 因为缓存重点startTime可能已经距离当前时间较长，需要更长的beforeTime
                for (auto it = videoRecordTasks.begin(); it != videoRecordTasks.end(); )
                {
                    auto now = std::chrono::system_clock::now();
                    revisedBeforeTime = (guint)round<seconds>(now - it->start.startTime).count() + beforeTime;
                    if (revisedBeforeTime > CACHE_SIZE_SEC)   //!< 重新计算的beforeTime如果超过缓存时长，则该录像任务被延误了，无法录像
                    {
                        IVA_LOG_WARN("record task is overdue, channel {}", channel)
                        it = videoRecordTasks.erase(it);
                        continue;
                    }
                    break;
                }
                if (videoRecordTasks.empty())
                    return;

                auto task = videoRecordTasks.front();
                int position = 0;
                if (start(revisedBeforeTime, duration, position, task))
                {
                    videoRecordTasks.erase(videoRecordTasks.begin());
                    if (onRecordCallback != nullptr)
                    {
                        lk.unlock();
                        onRecordCallback(fileName, position, task.start);

                        for (auto& targetInfo : task.passedTargets) //!< 如果当前视频录像时间段有其他缓存过线目标，则直接上传
                        {
                            position = (int) (round <seconds> (targetInfo.startTime - currentRecord.start.startTime).count() + revisedBeforeTime);
                            onRecordCallback(fileName, position, targetInfo);
                        }
                    }
                }
            });
        }
    }

    /**
     * 注册用户的录像完成回调函数
     */
    void VideoRecord::registerCompleteCallback(const CompleteCallback& func)
    {
        completeCallback = func;
    }

    /**
     * 录像完成回调，写日志，调用用户注册的回调函数
     */
    gpointer VideoRecord::onComplete(NvDsSRRecordingInfo * info, gpointer userData)
    {
        if (!info || !userData)
            return nullptr;

        auto videoRecordPtr = (VideoRecord*)userData;

        if (opt::getOptionEnabled(opt::VIDEO_RECORD_OPT))
        {
            std::string logFileName = "smart_record_" + std::to_string(videoRecordPtr->getChanel()) + ".log";

            std::ofstream of(logFileName, ios::app);
            of << endl << "[record file]:" << endl;
            of << "duration:" << info->duration << " filename:" << info->dirpath << info->filename << endl;
            of << "position:" << videoRecordPtr->getBeforeTime() << " target:";

            for (const auto& target :videoRecordPtr->getRecordInfo().start.targets)
                of << " id:" << target.id;

            for (auto& [startTime, targets,frame] : videoRecordPtr->getRecordInfo().passedTargets)
            {
                int position = (int)(round<seconds>(startTime - videoRecordPtr->getRecordInfo().start.startTime).count() + videoRecordPtr->getBeforeTime());
                of << endl << "position:" << position << " target:";

                for (const auto& target : targets)
                    of << " id:" << target.id;
            }
            of.close();
        }

        videoRecordPtr->setCompleted();
        return nullptr;
    }
}
