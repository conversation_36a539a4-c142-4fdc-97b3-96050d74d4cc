#pragma once
#include "ini/inibase.h"

/*
 * 路障 检测配置 (INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(ia::roadblock, "config/road_block.ini",
(bool,   enableTracker,	    tracker,   true, u8"是否打开跟踪过滤,默认为true")
(int,    framesNum,		    tracker,   4,	 u8"跟踪缓存帧数")
(double, iouThres,		    tracker,   0.8, u8"目标的当前框与历史框IOU阈值")
(double, scoreThres1,	    tracker,   0.28, u8"置信度阈值1")
(double, scoreThres2,	    tracker,   0.6,	 u8"置信度阈值1")
(int,    countThres1,		tracker,   1,	 u8"置信度阈值1所需满足计数个数")
(int,	 countThres2,		tracker,   3,	 u8"置信度阈值2所需满足计数个数")
(int,	 interval,			tracker,   150,	 u8"跟踪帧的交替频率，用于模拟抽帧")
(bool,   isFilterObject,    roadblock, true, u8"是否过滤检出框,默认为true")
(double, ratioThres,	    roadblock, 0.8,	 u8"目标框和子区域重叠的IOU阈值")
(bool,   enablePostprocess, roadblock, true, u8"是否开启后处理,默认为true")
)

#define ROADBLOCK_CFG ia::roadblock::Config::instance()
