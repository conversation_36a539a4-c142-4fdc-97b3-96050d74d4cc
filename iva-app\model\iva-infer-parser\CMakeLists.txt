﻿cmake_minimum_required (VERSION 3.5.2)
## 目标生成
set(TARGET_LIBRARY "ivainfer_parser")
# 输出目录
set(LIBRARY_OUTPUT_PATH /data/opt/models/lib)

add_compile_options(-Wall -std=c++11 -fPIC)

# TensorRT
set(TensorRT_HOME "/usr/local/TensorRT")
set(TensorRT_LIB
    nvinfer
    nvparsers
)

# DEEPSTREAM
set(DEEPSTREAM_HOME "/opt/nvidia/deepstream/deepstream")

# 头文件
include_directories(
    ${TensorRT_HOME}/include/
    ${DEEPSTREAM_HOME}/sources/includes/
)

# 库路径
link_directories(
    ${TensorRT_HOME}/lib/
)

# cpp
FILE(GLOB src "*.cpp")
SET(ALL_SRC ${include} ${src})

# 生成动态库
ADD_LIBRARY(${TARGET_LIBRARY} SHARED ${ALL_SRC})
target_link_libraries(${TARGET_LIBRARY} ${TensorRT_LIB})
