/**
 * Project AI事件分析模块
 */

#ifndef _LANE_H
#define _LANE_H

#include "region.h"
#include "area/lane_info.h"
#include "enum/lane_type.h"

/**
* 车道
*/
namespace evt {

	class Lane : public Region {
	public:

		/**
		 * @param info
		 * @param roiID
		 */
		Lane(LaneInfo info, int roiID);

		/**
		 * 获取车道类型
		 */
		LaneType getLaneType();

	private:

		/**
		 * 车道类型
		 */
		LaneType laneType;
	};
}
#endif //_LANE_H