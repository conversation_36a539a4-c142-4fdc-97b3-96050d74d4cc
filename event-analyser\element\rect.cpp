/**
 * Project AI事件分析模块
 */

#include "element/rect.h"
#include "util/scene_utility.h"
#include <algorithm>
#include <cmath>
/**
 * Rect implementation
 * 
 * 矩形框
 */
namespace evt
{
	Rect::Rect():x(0.0f),y(0.0f),width(0.0f),height(0.0f)
	{

	}

	/**
	 * @param lt 左上点
	 * @param br 右下点
	 */
	Rect::Rect(Point lt, Point br)
	{
		this->x = lt.x;
		this->y = lt.y;
		this->width = br.x - lt.x;
		this->height = br.y - lt.y;
	}

	/**
	 * @param x
	 * @param y
	 * @param w
	 * @param h
	 */
	Rect::Rect(float x, float y, float w, float h){
		this->x = x;
		this->y = y;
		this->width = w;
		this->height = h;
	}

	/**
	 * 是否包含矩形框
	 */
	bool Rect::contains(const Rect &other) {
		if (x > other.x) return false;
		if (y > other.y) return false;
		if (x + width < other.x + other.width) return false;
		if (y + height < other.y + other.height) return false;
		return true; // within bounds
	}

	/**
	 * 是否包含点
	 */
	bool Rect::contains(const Point &p) {
		if (x > p.x) return false;
		if (y > p.y) return false;
		if (x + width < p.x) return false;
		if (y + height < p.y) return false;
		return true;
	}

	/**
	 * 是否相交矩形框
	 */
	bool Rect::intersects(const Rect &other) {
		if (x > other.x + other.width)  return false;
		if (x + width < other.x)        return false;
		if (y > other.y + other.height) return false;
		if (y + height < other.y)       return false;
		return true; // intersection
	}

	/**
	 * 计算与其他矩形框 距离
	 */
	float Rect::distanceFrom(Rect& other)
	{
		float x1 = x;
		float y1 = y;
		float x1b = getRight();
		float y1b = getBottom();

		float x2 = other.x;
		float y2 = other.y;
		float x2b = other.getRight();
		float y2b = other.getBottom();

		bool left = x2b < x1;
		bool right = x1b < x2;
		bool bottom = y2b < y1;
		bool top = y1b < y2;

		if (top && left)
			return calculateDistance(Point(x1, y1b), Point(x2b, y2));
		else if (left && bottom)
			return calculateDistance(Point(x1, y1), Point(x2b, y2b));
		else if (bottom && right)
			return calculateDistance(Point(x1b, y1), Point(x2, y2b));
		else if (right && top)
			return calculateDistance(Point(x1b, y1b), Point(x2, y2));
		else if (left)
			return x1 - x2b;
		else if (right)
			return x2 - x1b;
		else if (bottom)
			return y1 - y2b;
		else if (top)
			return y2 - y1b;
		else
			return 0.f;
	}

	/**
	 * 获取矩形框左边值
	 */
	float Rect::getLeft() { return x; }

	/**
	 * 获取矩形框顶部值
	 */
	float Rect::getTop()  { return y; }

	/**
	 * 获取矩形框右边值
	 */
	float Rect::getRight() { return x + width; }

	/**
	 * 获取矩形框底边值
	 */
	float Rect::getBottom() { return y + height; }

	/**
	 * 获取矩形框中心X
	 */
	float Rect::getCenterX() { return x + 0.5f * width; }

	/**
	 * 获取矩形框中心Y
	 */
	float Rect::getCenterY() { return y + 0.5f * height; }

	/**
	 * 获取矩形框注册点
	 */
	Point Rect::getPosition(float offsetX, float offsetY) { return Point(x + offsetX * width, y + offsetY * height); }

	/**
	 * 获取矩形框中心点
	 */
	Point Rect::getCenterPosition() { return Point(x + 0.5f * width, y + 0.5f* height); }

	/**
	 * 获取矩形边长（平均边长）
	 */
	float Rect::getSideLength()	{return (width + height) / 2.0f;}

	/**
	 * 获取矩形边长（最大边长）
	 */
	float Rect::getMaxLength() { return std::max(width, height); }

	/**
	 * 获取矩形 高宽比
	 */
	float Rect::getHWRatio(){return width != 0.0f ? height / width :0.0f;}

	/**
	* 获取矩形框对角线长度
	*/
	float Rect::getDiagonalLen() {return sqrtf(std::pow(width, 2) + std::pow(height, 2)); }
	/**
	 * 框子是否合法
	 */
	bool Rect::isValid() { return width > 0.0f && height > 0.0f ; }
}
