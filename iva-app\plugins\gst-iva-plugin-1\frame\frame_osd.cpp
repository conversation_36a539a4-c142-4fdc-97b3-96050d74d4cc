#include "frame_osd.h"
#include "ivaconfig.hpp"
#include "ivautils.h"
#include "log.h"

/*
*  结构化页面 绘制参数 初始化
*/
void OSDParam::init()
{
	line_params = g_new0(OSD_LineParams, MAX_OSD_ELEMS);
	frame_line_params = g_new0(OSD_FrameLineParams, 1);
	rect_params = g_new0(OSD_RectParams, MAX_OSD_ELEMS);
	frame_rect_params = g_new0(OSD_FrameRectParams, 1);
	text_params = g_new0(OSD_TextParams, MAX_OSD_ELEMS);
	frame_text_params = g_new0(OSD_FrameTextParams, 1);
	font_name = g_strdup("Ubuntu"); 
}

/*
*  结构化页面 绘制参数 销毁
*/
void OSDParam::destroy()
{
	g_free(line_params);
	g_free(frame_line_params);
	g_free(rect_params);
	g_free(frame_rect_params);
	g_free(text_params);
	g_free(frame_text_params);
	g_free(font_name);
}


/*
* 获取事件状态颜色
*/
OSD_ColorParams getEventStateColor(evt::EventState evt_state, double alpha)
{
	switch (evt_state)
	{
	case evt::EventState_Proposal:		 // 候选状态
		return (OSD_ColorParams) { 0, 0, 1, alpha };
	case evt::EventState_Confirming:		// 确认
	case evt::EventState_Maintaining:		//维持
		return (OSD_ColorParams) { 1, 0, 0, alpha };
	case evt::EventState_Released:			//解除
		return (OSD_ColorParams) { 0, 1, 0, alpha };
	default:
		return (OSD_ColorParams) { 0, 1, 1, alpha };  //blue
	}
}

/*
*  结构化页面 绘制图像
*/
gboolean modify_frame(BufSurface* surface, void * nvosd_ctx, evt::TargetInfoList& targets, evt::EventInfoList& jam_infos,
	evt::EventObjectList& evt_objects, OSDParam& param, const OSDFrameInfo& frame_info)
{
	int surfindex = frame_info.surfindex;
	float scale_x = frame_info.scale_x;
	float scale_y = frame_info.scale_y;
	bool show_track = frame_info.show_track;
	int frame_num = frame_info.frame_num;

	int thicknessRatio = 1;
#ifdef USE_SEARCH_MODULE
	int fontSize = 20;
#else
	int fontSize = 12;
#endif

	auto& frame_line_params = param.frame_line_params;
	auto& frame_rect_params = param.frame_rect_params;
	auto& frame_text_params = param.frame_text_params;

	frame_line_params->line_params_list = param.line_params;
	frame_line_params->buf_ptr = &surface->surfaceList[surfindex];
	frame_line_params->num_lines = 0;
	frame_text_params->num_strings = 0;
	frame_rect_params->num_rects = 0;

#ifdef USE_HARDWARE_OSD
	frame_line_params->mode = MODE_HW;
	frame_text_params->mode = MODE_HW;
	frame_rect_params->mode = MODE_GPU;
#else
	frame_rect_params->mode = MODE_CPU;
#endif

	int existing_tracks_num = targets.size();

	for (auto& evt: jam_infos)
	{
		int flash_cd = evt.state == evt::EventState_Maintaining ? 20 : 40;
		double alpha = (double)(frame_num % flash_cd) / flash_cd;
		OSD_ColorParams color = getEventStateColor(evt.state, alpha);
		auto& poly = evt.occurArea;
		int point_count = poly.pointCount();

		int offset = frame_line_params->num_lines;
		for (guint k = 0; k < point_count - 1; ++k)
		{
			if (offset + k >= MAX_OSD_ELEMS)
				break;

			auto p1 = poly.getPoint(k);
			auto p2 = poly.getPoint(k + 1);
			OSD_LineParams *line_params = &param.line_params[offset + k];
			line_params->x1 = p1.x / scale_x;
			line_params->y1 = p1.y / scale_y;
			line_params->x2 = p2.x / scale_x;
			line_params->y2 = p2.y / scale_y;
			line_params->line_width = 2* thicknessRatio;
			line_params->line_color = color;
			frame_line_params->num_lines++;
		}
	}

	frame_rect_params->buf_ptr = &(surface->surfaceList[surfindex]);
	frame_rect_params->rect_params_list = param.rect_params;

	frame_text_params->buf_ptr = &(surface->surfaceList[surfindex]);
	frame_text_params->text_params_list = param.text_params;

	if (SETTINGS->drawImageEvent()) // 绘制图像事件
	{
		for (auto& obj : evt_objects)
		{
			if (frame_rect_params->num_rects > MAX_OSD_ELEMS)
			{
				break;
			}
			OSD_ColorParams color;
			switch (obj.type)
			{
			case evt::EventType_RoadBlock:
				color = (OSD_ColorParams) { 1.0, 0.6, 0.0, 1.0 };
				break;
			case evt::EventType_FireSmoke:
				color = (OSD_ColorParams) { 0.8, 0.8, 0.8, 1.0 };
				break;
			case evt::EventType_Obstacle:
				color = (OSD_ColorParams) { 1.0, 0.0, 0.6, 1.0 };
				break;
            case evt::EventType_Landslide:
                color = (OSD_ColorParams) { 1.0, 0.8, 0.6, 1.0 };
			default:break;
			}

			OSD_RectParams *rect_params = &param.rect_params[frame_rect_params->num_rects++];

			auto& rect = obj.rect;
			rect_params->left = rect.getLeft() / scale_x;
			rect_params->top = rect.getTop() / scale_y;
			rect_params->width = rect.width / scale_x;
			rect_params->height = rect.height / scale_y;

			rect_params->border_width = 1* thicknessRatio;
			rect_params->border_color = color;
			rect_params->has_bg_color = 1;
			rect_params->bg_color = color;
			rect_params->bg_color.alpha = 0.3;
			rect_params->has_color_info = 0;
		}
	}
		
	long long sys_time = get_system_timestamp();
	for (guint j = 0; j < existing_tracks_num; j++)
	{
		if (frame_rect_params->num_rects > MAX_OSD_ELEMS)
		{
			break;
		}
		auto& target = targets[j];

		OSD_ColorParams color;
		if ( !target.showable )  //表示在免检区
		{ 
			color = (OSD_ColorParams){ 0.5, 0.5, 0.5, 1 };
		}
		else
		{
			double alpha = target.eventState != evt::EventState_Confirming && target.eventState != evt::EventState_Maintaining ? 1.0 :
						sys_time - target.eventOccurTime > 0 && sys_time - target.eventOccurTime < 30000 ?
						(double)(frame_num % 8 + 2) / 10 : 0.8;

			color = getEventStateColor(target.eventState, alpha);
		}
		OSD_RectParams *rect_params = &param.rect_params[frame_rect_params->num_rects++];

		auto& rect = target.tracks.back().rect;
		rect_params->left = rect.getLeft() / scale_x;
		rect_params->top = rect.getTop() / scale_y;
		rect_params->width = rect.width / scale_x;
		rect_params->height = rect.height / scale_y;

		rect_params->border_width = target.eventState != evt::EventState_None && target.showable ? 2 : 1;
		rect_params->border_width *= thicknessRatio;
		rect_params->border_color = color;
		rect_params->has_bg_color = 0;
		rect_params->has_color_info = 0;

		int traces_num = target.tracks.size();
		if (SETTINGS->drawTrackLine() && traces_num >= 2)
		{
			color.alpha = 1.0;
			// optimize line count 
			// PS: deepstream crashed sometimes when with lots of lines or invalid data

			std::vector<evt::Point> track_to_draw;
			int max_len_scan = std::min(70, (int)traces_num);

			track_to_draw.push_back(target.tracks[traces_num - 1].rect.getPosition(target.registX, target.registY));
			for (int i = 1; i < max_len_scan; i++)
			{
				auto& last = track_to_draw.back();
				auto check = target.tracks[traces_num - 1 - i].rect.getPosition(target.registX, target.registY);

				if ((last.x - check.x)*(last.x - check.x) + (last.y - check.y)*(last.y - check.y) > 16)
					track_to_draw.push_back(check);

				if (track_to_draw.size() > 14)
					break;
			}

			if (track_to_draw.size() > 1)
			{
				int offset = frame_line_params->num_lines;
				for (guint k = 0; k < track_to_draw.size() - 1; ++k)
				{
					if (offset + k >= MAX_OSD_ELEMS) 
						break;

					OSD_LineParams *line_params = &param.line_params[offset + k];
					line_params->x1 = track_to_draw[k].x / scale_x;
					line_params->y1 = track_to_draw[k].y / scale_y;
					line_params->x2 = track_to_draw[k + 1].x / scale_x;
					line_params->y2 = track_to_draw[k + 1].y / scale_y;
					line_params->line_width = 1 *thicknessRatio;
					line_params->line_color = color;
					frame_line_params->num_lines++;
				}
			}
		}

		if ((SETTINGS->drawSpeed() || SETTINGS->drawType() || SETTINGS->drawTargetID() || SETTINGS->drawClassType()))
		{
			int txtnum = frame_text_params->num_strings;
			if (txtnum >= MAX_OSD_ELEMS)
				break;

			std::vector<gchar* > titles;
			if (!target.extraData.empty())
				titles.push_back(g_strdup(target.extraData.c_str()));

			if (SETTINGS->drawType())
			{
				titles.push_back(g_strdup_printf(" %s ", TARGET_NAMES[target.type].c_str()));
			}
            if (SETTINGS->drawTargetID())
            {
                titles.push_back(g_strdup_printf(" %d ", target.id));
            }
            if (SETTINGS->drawClassType())
            {
                for (const auto& [type,label] : target.classTypes.getClassTypes())
                    titles.push_back(g_strdup(label.c_str()));
            }
			if (show_track)
			{
				titles.push_back(g_strdup_printf("[%d %d %d]", target.roiID, target.laneID, target.regionID));
			}
			if (SETTINGS->drawSpeed() && target.speed > 0)
			{
				titles.push_back(g_strdup_printf("%d km/h", target.speed));
			}

			int txtrow = titles.size();
			if (txtrow > 0)
			{
				titles.push_back(NULL);
				auto showstr = g_strjoinv("\n", titles.data());
				OSD_TextParams *text_params = &param.text_params[txtnum];
				text_params->x_offset = rect_params->left - 10;
				text_params->y_offset = rect_params->top - txtrow * 14 - 12 ;
				text_params->display_text = showstr;
				text_params->font_params.font_name = param.font_name;
				text_params->font_params.font_size = fontSize;
				text_params->font_params.font_color = { 0.5,1,0,1 };
				text_params->set_bg_clr = 1;
				text_params->text_bg_clr = { 0,0,0,0.1 };
				frame_text_params->num_strings++;

				for (auto& c : titles)
				{
					if(c != NULL)
						g_free(c);
				}
			}
		}
	}

	int txtnum = frame_text_params->num_strings;
	if (frame_info.state != IVA_CHANNEL_RESTORED && txtnum < MAX_OSD_ELEMS)
	{
		OSD_TextParams* text_params = &param.text_params[txtnum];
		text_params->x_offset = 0;
		text_params->y_offset = 0;
		text_params->display_text = frame_info.state == IVA_CHANNEL_PAUSED_DEFAULT ? g_strdup("PAUSED") : g_strdup("OFFSET");
		text_params->font_params.font_name = param.font_name;
		text_params->font_params.font_size = 12;
		text_params->font_params.font_color = (frame_info.state == IVA_CHANNEL_PAUSED_DEFAULT) ? (OSD_ColorParams) {1,0.1,0,1}
																								: (OSD_ColorParams) {0.6,0.6,0,1};
		text_params->set_bg_clr = 1;
		text_params->text_bg_clr = { 0,0,0,0.2 };
		frame_text_params->num_strings++;
	}

	try {
		if (frame_line_params->num_lines > 0 && ai_osd_draw_lines(nvosd_ctx, frame_line_params))
		{
			printf("failure to draw lines");
		}
		if (ai_osd_draw_rectangles(nvosd_ctx, frame_rect_params))
		{
			printf("failure to draw rectangle");
		}
		if (frame_text_params->num_strings > 0 && ai_osd_put_text(nvosd_ctx, frame_text_params))
		{
			printf("failure to draw text");
		}

		for (int i = 0; i < frame_text_params->num_strings; i++)
		{
			OSD_TextParams *text_params = &param.text_params[i];
			g_free(text_params->display_text);
		}
#ifndef NVIDIA
        ai_osd_finish(nvosd_ctx);
#endif
	}
	catch (...)
	{
		printf("frame osd error");
	}
	return true;
}
