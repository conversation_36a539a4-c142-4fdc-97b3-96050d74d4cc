
/**
 * Project IVA (image analyser)
 */
#include "roi_object_detector.h"
#include "config/detect_config.hpp"
#include "config/roiobject_config.hpp"
#include "config/roiobject_model_config.hpp"
#include "roi_object_detect/roi_object_parms.h"
#include <string>

namespace ia
{
    ROIObjectDetector::ROIObjectDetector(int interval, int deviceID, int channelID) : ObjectDetector(true, interval)
    {
        setParams();
        deviceId = deviceID;
        m_channelID = std::to_string(channelID);
    }

    ImageObjectList ROIObjectDetector::detect(const FrameData& frameData)
    {
        extraDetectModelProcess(frameData);

        if (!shouldDetect())
            return historyObjects;

        if (m_featureModel == nullptr)
        {
            if (!initModels())
            {
                return historyObjects;
            }
        }

        cv::Mat frame = frameData.frame;
        if (frame.empty())
            return historyObjects;


        struct timeval tv;
        gettimeofday(&tv, NULL);
        long t0 = tv.tv_sec * 1000 + tv.tv_usec / 1000;
        //std::string fdId = "sourceId"+std::to_string(fd.sourceID)+"_presentId"+std::to_string(fd.presentID);

        //create struct wihch contain the parameters in algorithm processing, and init
        ROIObjectParam roiObjectParam;
        initRoiObjectParam(&roiObjectParam, frame);

        // do img proprecessing for currImg
        featureModelImagePreprocess(&roiObjectParam);

        // calculate featrues of current img
        // calcuate the feature distances between currImg and many previous imgs
        featureModelForward(&roiObjectParam);
        if (!is_gen_feature_success)
            return historyObjects;

        // if background img has been generated, do background img proprecessing for featureModel
        bgFeatureModelImagePreprocess(&roiObjectParam);

        // if background img has been generated, calculate featrues of background img
        // calcuate the feature distance between currImg and  background img  (bgFrontDis)
        bgFeatureModelForward(&roiObjectParam);

        // get single distance between currImg and first forward img (singleDis1)
        // get single distance between currImg and second forward img (singleDis2)
        // get single distance between currImg and many previous imgs (multiDis)
        solveDistance(&roiObjectParam);

        // generate bounding boxes
        // filtering by boxSize/boxIou/boxContains and so on
        getBoxes(&roiObjectParam);

        // filtering the static boxes by trace the bounding boxes
        traceBoxes(&roiObjectParam);

        // filtering boxes by a interest mask
        filterByInterestMask(&roiObjectParam);
        int alarmRectNum = roiObjectParam.alarmRects.size();
#ifdef USING_DETECTMODEL

        gettimeofday(&tv, NULL);
        long t1 = tv.tv_sec * 1000 + tv.tv_usec / 1000;
        //filtering boxes by a class model
        filterByClassify(&roiObjectParam);
        // filtering boxes by a detect model
        filterByDetector(&roiObjectParam);
#else
        filterByClassify(roiObjectParam);
#endif

        // put the result of throwaway_Coords to vector throwaways
        putThrowawaysResults(&roiObjectParam);

        // update the background img
        updateBg(&roiObjectParam);

        //if opening saveFlag, draw and save the images in the processing.
        drawAndSaveImgs(&roiObjectParam, m_channelID);

        m_frameNum++;
        if (m_frameNum >= 50000)m_frameNum = m_frameNum % 50000;

        gettimeofday(&tv, NULL);
        long t2 = tv.tv_sec * 1000 + tv.tv_usec / 1000;
        if (m_saveAverageTimeFlag) {
            m_averageTime += t2 - t0;
            if (m_frameNum % 99 == 0) {
                std::string logValue = "throwaway using : average time is {} ms, time for curr frame is {} ms,class and detect time is {} ms which static objects nums is {}  , ";
                logValue = logValue + m_channelID;
                //Log::warn("throwaway average time for one frame is {} ms",m_averageTime/1);
               // IVA_LOG_WARN();
               // Log::warn(logValue.data(), m_averageTime / 100.0, t2 - t0, t1 - t0, alarmRectNum);
                m_averageTime = 0.0;
            }
        }
		ImageObjectList roadblockRects;
		ImageObjectList objects;
		if (frameData.inputObjects.find(DetectorType::Roadblock) != frameData.inputObjects.end()) {
			auto& detectedObjects = frameData.inputObjects.at(DetectorType::Roadblock);
			roadblockRects = detectedObjects;
		}
		for (auto& t : throwaways)
		{
			bool isStore = true;
			t.x = t.x * frameData.width;
			t.y = t.y * frameData.height;
			t.width = t.width * frameData.width;
			t.height = t.height * frameData.height;
			for (int j = 0; j < roadblockRects.size(); j++) {
				ObjBox throwawayBox;
				ObjBox roadblockBox;
				throwawayBox.x = t.x;
				throwawayBox.y = t.y;
				throwawayBox.width = t.width;
				throwawayBox.height = t.height;
				roadblockBox.x = roadblockRects[j].x;
				roadblockBox.y = roadblockRects[j].y;
				roadblockBox.width = roadblockRects[j].width;
				roadblockBox.height = roadblockRects[j].height;
				if (rectIou(throwawayBox, roadblockBox) > 0.01) {
					isStore = false;
					break;
				}
			}

            if (isStore && m_detectModelParam.detectThrowawayMode == DetectThrowawayMode::FUSION_MODEL)
            {
                isStore = filterByExtraDetector(t);
            }

			if (isStore) {
				objects.emplace_back(ImageObject{ 0, (float)t.x, (float)t.y, (float)t.width, (float)t.height });
			}
		}
		historyObjects = objects;
        return objects;
    }

    /**
     * @brief 额外的检测模型目标处理
     * @param[in] object 检测到的目标
     */
    void ROIObjectDetector::extraDetectModelProcess(const FrameData &frameData)
    {
        if (m_detectModelParam.detectThrowawayMode == DetectThrowawayMode::FUSION_MODEL)
        {
            auto objectsItr = frameData.inputObjects.find(DetectorType::Roiobject);
            if (objectsItr != frameData.inputObjects.end())
            {
                ImageObjectList objects = objectsItr->second;
                if (!m_interestedMask.empty())
                    objects = selectRectByInterestMask(objects, m_interestedMask);

                if (++printDetectObjectCount >= (std::numeric_limits<decltype(printDetectObjectCount)>::max() - 1))
                    printDetectObjectCount = 0;
                if (printDetectObjectCount % ROIOBJECT_CFG->printInterval() == 0)
                {
                    for (auto& object: objects)
                    {
                        IVA_LOG_INFO("throwaway object : channel:{}, frameIndex:{}, score:{}, x:{}, y:{}, w:{}, h:{}",m_channelID, frameData.frameIndex, object.confidence, object.x, object.y, object.width, object.height)
                    }
                }
                extraDetectCaches.emplace_back(frameData.frameIndex, frameData.inputObjects.at(DetectorType::Roiobject));
                if (extraDetectCaches.size() > m_detectModelParam.cacheSize)
                    extraDetectCaches.pop_front();
            }
            else
            {
                if (!extraDetectCaches.empty())
                {
                    auto& last = extraDetectCaches.front();
                    if (frameData.frameIndex - last.first > m_detectModelParam.cacheSize * ROI_OBJECT_DETECT_INTERVAL)
                        extraDetectCaches.pop_front();
                }
            }
        }
    }

    bool ROIObjectDetector::filterByExtraDetector(const cv::Rect2d& box)
    {
        ObjBox throwawayBox{(int)box.x, (int)box.y, (int)box.width, (int)box.height, 0.0, 0, {}};

        float iou = 0.0;
        for (const auto&[index, frameObjects] : extraDetectCaches)
        {
            for (const auto& object : frameObjects)
            {
                ObjBox extraDetectBox = {(int)object.x, (int)object.y, (int)object.width, (int)object.height,  0.0, 0, {}};
                iou = rectIou(extraDetectBox, throwawayBox);
                if (iou > 0)
                {
                    IVA_LOG_INFO("throwaway object IOU {} matched successful , confidence {} , extraDetectCaches size {},  channelID {}", iou, object.confidence, extraDetectCaches.size(), m_channelID);
                    return true;
                }
            }
        }

        IVA_LOG_INFO("throwaway object IOU {} matched fail , extraDetectCaches size {},  channelID {}", iou , extraDetectCaches.size(), m_channelID);
        return false;
    }

    bool ROIObjectDetector::initModels()
    {
        int ret = 0;
        try {
            m_featureModel = new RoiObjectFeatureModel(m_featureModelParam, ret, deviceId);
            m_featureModel->initResource();
        }
        catch (char* str)
        {
            std::cout << str << endl;
        }
        if (ret != 0) {
            delete m_featureModel;
            m_featureModel = nullptr;
            IVA_LOG_ERROR("init roiObject FeatureModel error , ret : {},modelPath:{}", ret, m_featureModelParam.modelPath);
            return false;
        }

        setFeatureParams();
#ifdef USING_DETECTMODEL
        m_detectModel = new RoiObjectDetectModel(m_detectModelParam);
        m_classModel = new RoiObjectClassModel(m_classModelParam);
#else
        m_classModel = new RoiObjectClassModel(m_classModelParam);
#endif
        m_distanceSolver = new DistanceSolver(m_distanceSolverParam);
        m_bgImgGenerator = new BgImgGenerator(m_featureModelParam, m_bgImgGeneratorParam);
        m_rectGenerator = new RectGenerator(m_selectRectParam);
        m_staticObjGenerator = new StaticObjGenerator();
        return true;
    }

    void ROIObjectDetector::setParams()
    {
        m_featureModelParam.modelPath = DETECT_CFG->modelPath() + "/" + ROIOBJECT_MODEL_CFG->featureModelPath();
        m_featureModelParam.maxFeatureNum = ROIOBJECT_CFG->featureNum();

        m_detectModelParam.modelPath = DETECT_CFG->modelPath() + "/" + ROIOBJECT_MODEL_CFG->detectModelPath();
        m_detectModelParam.inputW = ROIOBJECT_CFG->detectInputW();
        m_detectModelParam.inputH = ROIOBJECT_CFG->detectInputH();
        m_detectModelParam.scaleForSeg = ROIOBJECT_CFG->detectScaleForSeg();
        m_detectModelParam.segMode = ROIOBJECT_CFG->detectSegMode();
        m_detectModelParam.nmsIouThres = ROIOBJECT_CFG->detectNmsIouThres();
        m_detectModelParam.scoresThres = ROIOBJECT_CFG->detectScoresThres();
        m_detectModelParam.rectIouThres = ROIOBJECT_CFG->detectRectIouThres();
        m_detectModelParam.batch = ROIOBJECT_CFG->detectBatch();
        m_detectModelParam.paddingValue = ROIOBJECT_CFG->detectPaddingValue();
        m_detectModelParam.detectThrowawayMode = static_cast<DetectThrowawayMode>(ROIOBJECT_CFG->detectThrowawayMode());
        m_detectModelParam.cacheSize = ROIOBJECT_CFG->cacheSize();

        m_classModelParam.modelPath = DETECT_CFG->modelPath() + "/" + ROIOBJECT_MODEL_CFG->classModelPath();
        m_classModelParam.scaleForSeg = ROIOBJECT_CFG->classScaleForSeg();
        m_classModelParam.scaleForPadding = ROIOBJECT_CFG->classScaleForPadding();
        m_classModelParam.segMode = ROIOBJECT_CFG->classSegMode();
        m_classModelParam.scoresGip = ROIOBJECT_CFG->classScoresGip();
        m_classModelParam.batch = ROIOBJECT_CFG->classBatch();
        m_classModelParam.paddingValue = ROIOBJECT_CFG->classPaddingValue();

        m_bgImgGeneratorParam.motionThres = ROIOBJECT_CFG->motionThres();
        m_bgImgGeneratorParam.bgFrontThres = ROIOBJECT_CFG->bgFrontThres();
        m_bgImgGeneratorParam.unFillRatioThres = ROIOBJECT_CFG->unFillRatioThres();

        m_distanceSolverParam.maxDistanceNum = m_featureModelParam.maxFeatureNum - 1;
        m_distanceSolverParam.validDistanceNum = ROIOBJECT_CFG->distanceValidNum();
        m_distanceSolverParam.moveDistanceNum = ROIOBJECT_CFG->distanceMoveNum();
        m_distanceSolverParam.updataDisThres = ROIOBJECT_CFG->distanceDisThres();

        m_selectRectParam.minH = ROIOBJECT_CFG->selectMinH();
        m_selectRectParam.minW = ROIOBJECT_CFG->selectMinW();
        m_selectRectParam.maxH = ROIOBJECT_CFG->selectMaxH();
        m_selectRectParam.maxW = ROIOBJECT_CFG->selectMaxW();
        m_selectRectParam.maxHForFirstFilter = ROIOBJECT_CFG->selectMaxHForFirstFilter();
        m_selectRectParam.maxWForFirstFilter = ROIOBJECT_CFG->selectMaxWForFirstFilter();
        m_selectRectParam.carLeastWidth = ROIOBJECT_CFG->selectCarLeastWidth();
        m_selectRectParam.scoreThresForBinary = ROIOBJECT_CFG->selectScoreThresForBinary();
        m_selectRectParam.iouThresForAlarm = ROIOBJECT_CFG->selectIouThresForAlarm();
        m_selectRectParam.iouThresForSelect = ROIOBJECT_CFG->selectIouThresForSelect();
        m_selectRectParam.isOpening = ROIOBJECT_CFG->selectIsOpening();
        m_selectRectParam.farRatio = ROIOBJECT_CFG->selectFarRatio();
        m_selectRectParam.roadNums = ROIOBJECT_CFG->selectRoadNums();
        m_selectRectParam.carNumForRoads.push_back(ROIOBJECT_CFG->selectCarNumForRoad1());
        m_selectRectParam.carNumForRoads.push_back(ROIOBJECT_CFG->selectCarNumForRoad2());
        m_selectRectParam.carNumForRoads.push_back(ROIOBJECT_CFG->selectCarNumForRoad3());
        m_selectRectParam.carNumForRoads.push_back(ROIOBJECT_CFG->selectCarNumForRoad4());
        m_selectRectParam.carNumForRoads.push_back(ROIOBJECT_CFG->selectCarNumForRoad5());

        m_roiObjectTrackerParam.staticFrontTimesThres = ROIOBJECT_CFG->staticFrontTimesThres();
        m_roiObjectTrackerParam.maxDecreaseTimes = ROIOBJECT_CFG->maxDecreaseTimes();
        m_roiObjectTrackerParam.compareIouThres1 = ROIOBJECT_CFG->compareIouThres1();
        m_roiObjectTrackerParam.compareIouThres2 = ROIOBJECT_CFG->compareIouThres2();
        m_roiObjectTrackerParam.isUseRectFlag = ROIOBJECT_CFG->isUseRectFlag();
        m_roiObjectTrackerParam.compareStaticThres = ROIOBJECT_CFG->compareStaticThres();
        m_roiObjectTrackerParam.newTraceAppendFlag = ROIOBJECT_CFG->newTraceAppendFlag();

        m_imgSavePathParam.imgSaveDir = ROIOBJECT_CFG->imgSaveDir();
        m_imgSavePathParam.bgDir = ROIOBJECT_CFG->imgSaveDir() + "/" + ROIOBJECT_CFG->bgDir();
        m_imgSavePathParam.checkBgDir = ROIOBJECT_CFG->imgSaveDir() + "/" + ROIOBJECT_CFG->checkBgDir();
        m_imgSavePathParam.binaryDir = ROIOBJECT_CFG->imgSaveDir() + "/" + ROIOBJECT_CFG->binaryDir();
        m_imgSavePathParam.interestedDir = ROIOBJECT_CFG->imgSaveDir() + "/" + ROIOBJECT_CFG->interestedDir();
        m_imgSavePathParam.alarmDir = ROIOBJECT_CFG->imgSaveDir() + "/" + ROIOBJECT_CFG->alarmDir();
        m_imgSavePathParam.classifyDir = ROIOBJECT_CFG->imgSaveDir() + "/" + ROIOBJECT_CFG->classifyDir();
        m_imgSavePathParam.detectDir = ROIOBJECT_CFG->imgSaveDir() + "/" + ROIOBJECT_CFG->detectDir();
        m_imgSavePathParam.classSegDir = ROIOBJECT_CFG->imgSaveDir() + "/" + ROIOBJECT_CFG->classSegDir();
        m_imgSavePathParam.detectSegDir = ROIOBJECT_CFG->imgSaveDir() + "/" + ROIOBJECT_CFG->detectSegDir();
        m_imgSavePathParam.saveBgImgFlag = ROIOBJECT_CFG->saveBgImgFlag();
        m_imgSavePathParam.saveBinaryImgFlag = ROIOBJECT_CFG->saveBinaryImgFlag();
        m_imgSavePathParam.saveOtherImgsFlag = ROIOBJECT_CFG->saveOtherImgsFlag();
    }

    ROIObjectDetector::~ROIObjectDetector()
    {
        if (m_featureModel)delete m_featureModel;
#ifdef USING_DETECTMODEL
        if (m_detectModel)delete m_detectModel;
        if (m_classModel)delete m_classModel;
#else
        if (m_classModel)delete m_classModel;
#endif
        if (m_distanceSolver)delete m_distanceSolver;
        if (m_rectGenerator)delete m_rectGenerator;
        if (m_staticObjGenerator)delete m_staticObjGenerator;
        if (m_bgImgGenerator)delete m_bgImgGenerator;
    }

    void ROIObjectDetector::setFeatureParams() {
        m_featureModel->getInputSize(m_featureModelParam.imgInputW, m_featureModelParam.imgInputH, m_featureModelParam.downScale);
        m_featureModelParam.gridW = m_featureModelParam.imgInputW / m_featureModelParam.downScale;
        m_featureModelParam.gridH = m_featureModelParam.imgInputH / m_featureModelParam.downScale;
    }

    //set interested mask 
    bool ROIObjectDetector::setInterestedMaskByPoint(ROIObjectParam* roiObjectParam) {
        if (!this->regionMask.updated() && !this->regionMask.get().empty())
        {
            cv::Mat scaledMask = this->regionMask.mask(roiObjectParam->imgW, roiObjectParam->imgH);
          
            if (m_imgSavePathParam.saveBgImgFlag) {
                std::string command = "mkdir -p " + m_imgSavePathParam.checkBgDir;
                system(command.c_str());
                cv::imwrite(m_imgSavePathParam.checkBgDir + "/" + m_channelID + "_interestImgBg" + std::to_string(m_frameNum) + ".jpg", scaledMask);
            }

            m_interestedMask = scaledMask.clone() / 255;

            m_rectGenerator->updateParams(m_selectRectParam);
            return true;
        }
        return false;
    }

    //init struct wihch contain the parameters in algorithm processing
    void ROIObjectDetector::initRoiObjectParam(ROIObjectParam* roiObjectParam, cv::Mat img) {
        roiObjectParam->imgW = img.size().width;
        roiObjectParam->imgH = img.size().height;
        roiObjectParam->imgFeatureInputW = m_featureModelParam.imgInputW;
        roiObjectParam->imgFeatureInputH = m_featureModelParam.imgInputH;
        bool setMaskFlag = setInterestedMaskByPoint(roiObjectParam);
        roiObjectParam->roiObjectTrackerParam = &m_roiObjectTrackerParam;
        if (!m_interestedMask.empty()) {
            cv::Mat gray = (1 - m_interestedMask) * 128;
            std::vector<cv::Mat> imgChannels(3);
            std::vector<cv::Mat> vaildChannels;
            split(img, imgChannels);
            for (int i = 0; i < 3; i++) {
                cv::Mat imgTmp;
                cv::multiply(imgChannels[i], m_interestedMask, imgTmp);
                imgTmp = imgTmp + gray;
                vaildChannels.push_back(imgTmp);
            }
            cv::merge(vaildChannels, roiObjectParam->orginImg);
            if (setMaskFlag && m_imgSavePathParam.saveBgImgFlag) {
                cv::imwrite(m_imgSavePathParam.checkBgDir + "/" + m_channelID + "_paddingImg" + std::to_string(m_frameNum) + ".jpg", roiObjectParam->orginImg);
            }
            gray.release();
        }
        else {
            roiObjectParam->orginImg = img;
        }
        if (!m_interestedMask.empty()) {
            roiObjectParam->interestedMask = m_interestedMask;
            // roiObjectParam->selectRectParam->interestedMask = m_interestedMask;
        }
        roiObjectParam->selectRectParam = &m_selectRectParam;
        cv::resize(roiObjectParam->orginImg, roiObjectParam->resizeImg, cv::Size(m_featureModelParam.imgInputW, m_featureModelParam.imgInputH), (0.0), (0.0), cv::INTER_LINEAR);
        roiObjectParam->downScale = m_featureModelParam.downScale;
        roiObjectParam->ratioW = roiObjectParam->imgW * 1.0 / roiObjectParam->imgFeatureInputW;
        roiObjectParam->ratioH = roiObjectParam->imgH * 1.0 / roiObjectParam->imgFeatureInputH;
        roiObjectParam->maxFeatureNum = m_featureModelParam.maxFeatureNum;
        roiObjectParam->featureBatch = 1;
        roiObjectParam->cudaScoresIndex = 0;
        roiObjectParam->distances.clear();
        roiObjectParam->singleDis1.clear();
        roiObjectParam->singleDis2.clear();
        roiObjectParam->multiDis.clear();
        roiObjectParam->bgFrontDis.clear();
        roiObjectParam->singleFrameRects.clear();
        roiObjectParam->singleFrameRectAlarmFlags.clear();
        roiObjectParam->alarmRects.clear();
        roiObjectParam->towBgRects.clear();
        roiObjectParam->isNewBgFinished = -1;
        roiObjectParam->isNewBgGen = false;
        roiObjectParam->selectRectParam = &m_selectRectParam;
        roiObjectParam->staticFrontTimesThres = 10;
        roiObjectParam->maxDecreaseTimes = 3;
    }

    // do img proprecessing for currImg
    void ROIObjectDetector::featureModelImagePreprocess(ROIObjectParam* roiObjectParam) {
        m_featureModel->imagePreprocess(roiObjectParam->resizeImg, roiObjectParam->imageData);
    }

    // calculate featrues of current img
    // calcuate the feature distances between currImg and many previous imgs
    void ROIObjectDetector::featureModelForward(ROIObjectParam* roiObjectParam) {
        if (m_featureModel->isInitModel()) {
            m_featureModel->predict(roiObjectParam);
            is_gen_feature_success = true;
        }
        else {
            if (m_featureModel)delete m_featureModel;
            int ret = 0;
            try {
                m_featureModel = new RoiObjectFeatureModel(m_featureModelParam, ret, deviceId);
                m_featureModel->initResource();
            }
            catch (char* str)
            {
                std::cout << str << endl;
            }
            if (ret != 0) {
                IVA_LOG_ERROR("init roiObject FeatureModel error , ret : {},modelPath:{}", ret, m_featureModelParam.modelPath);
            }
            else {
                IVA_LOG_INFO("Throwaway FeatureModel init success");
            }
            is_gen_feature_success = false;
        }

    }

    // do img proprecessing for new bgImg
    void ROIObjectDetector::bgFeatureModelImagePreprocess(ROIObjectParam* roiObjectParam) {
        roiObjectParam->isBgChanged = m_bgImgGenerator->isFilledEnough() && m_bgImgGenerator->isBgChanged();
        if (m_bgImgGenerator->isFilledEnough()) {
            roiObjectParam->bgImg = m_bgImgGenerator->getBgPad(roiObjectParam->resizeImg);
            if (m_bgImgGenerator->isBgChanged()) {
                m_featureModel->imagePreprocess(roiObjectParam->bgImg, roiObjectParam->bgImageData);
            }
        }
    }

    // calcuate the feature distance between currImg and  background img  (bgFrontDis)
    void ROIObjectDetector::bgFeatureModelForward(ROIObjectParam* roiObjectParam) {
        if (m_bgImgGenerator->isFilledEnough())
        {
            if (roiObjectParam->isBgChanged) {
                // if background img has been generated, calculate featrues of background img
                m_featureModel->predictBgFeature(roiObjectParam);
            }
            // calcuate the feature distance between currImg and  background img  (bgFrontDis)
            m_featureModel->calBgFrontDistance(roiObjectParam);
        }
    }

    // get single distance between currImg and first forward img (singleDis1)
    // get single distance between currImg and second forward img (singleDis2)
    // get single distance between currImg and many previous imgs (multiDis)
    void ROIObjectDetector::solveDistance(ROIObjectParam* roiObjectParam) {
        if (roiObjectParam->distances.size() >= (roiObjectParam->maxFeatureNum - 1))
        {
            m_distanceSolver->solveDis(roiObjectParam);
        }
    }

    // generate bounding boxes
    // selecting by boxSize/boxIou/boxContains and so on
    void ROIObjectDetector::getBoxes(ROIObjectParam* roiObjectParam) {
        m_rectGenerator->getBoxesGrid(roiObjectParam, m_imgSavePathParam.saveBinaryImgFlag);
    }

    //  Filter some rects out with the iou traceing.
    void ROIObjectDetector::traceBoxes(ROIObjectParam* roiObjectParam) {
        m_staticObjGenerator->appendRects(roiObjectParam);
        m_staticObjGenerator->getStaticRegions(roiObjectParam);
    }

    //  Filter some rects out with the iou InterestMask.
    void ROIObjectDetector::filterByInterestMask(ROIObjectParam* roiObjectParam) {
        if (!roiObjectParam->interestedMask.empty()) {
            roiObjectParam->alarmRects = selectRectByInterestMask(roiObjectParam->alarmRects, roiObjectParam->interestedMask);
        }
    }

    //  Filter some rects out with the iou detect model.
    void ROIObjectDetector::filterByDetector(ROIObjectParam* roiObjectParam) {
        int ret = 0;
        m_detectModel->detect(roiObjectParam, deviceId, ret);
        if (ret != 0) {
            IVA_LOG_ERROR("init roiObject DetectModel error , ret : {},modelPath:{}", ret, m_detectModelParam.modelPath);
        }
    }

    //  Filter some rects out with the iou class model.
    void ROIObjectDetector::filterByClassify(ROIObjectParam* roiObjectParam) {
        int ret = 0;
        m_classModel->classify(roiObjectParam, deviceId);
        if (ret != 0) {
            IVA_LOG_ERROR("init roiObject ClassModel error , ret : {},modelPath:{}", ret, m_classModelParam.modelPath);
        }
    }

    // put the results of throwaway_Coords to vector throwaways
    void ROIObjectDetector::putThrowawaysResults(ROIObjectParam* roiObjectParam) {
        throwaways.clear();
        if (roiObjectParam->detectRects.size() > 0) {
            for (int i = 0; i < roiObjectParam->detectRects.size(); i++) {
                cv::Rect2d rect;
                rect.x = roiObjectParam->detectRects[i].x * 1.0 / roiObjectParam->imgW;
                rect.y = roiObjectParam->detectRects[i].y * 1.0 / roiObjectParam->imgH;
                rect.width = roiObjectParam->detectRects[i].width * 1.0 / roiObjectParam->imgW;
                rect.height = roiObjectParam->detectRects[i].height * 1.0 / roiObjectParam->imgH;
                throwaways.push_back(rect);
            }
        }
    }

    // update the background img
    void ROIObjectDetector::updateBg(ROIObjectParam* roiObjectParam) {
        if (roiObjectParam->multiDis.size() > 0) {
            m_bgImgGenerator->update(roiObjectParam);
            if (roiObjectParam->isNewBgGen) {
                roiObjectParam->newBgImg = m_bgImgGenerator->getNewBg();
                m_featureModel->imagePreprocess(roiObjectParam->newBgImg, roiObjectParam->newBgImageData);
                m_featureModel->calTowBgDistance(roiObjectParam);
                m_rectGenerator->getBoxesForTowBg(roiObjectParam);
                roiObjectParam->isNewBgFinished = 30;//10;
                roiObjectParam->isNewBgGen = false;
                m_bgImgGenerator->setTowBgRects(roiObjectParam);
            }
            //select regions not updated for new bg
            m_bgImgGenerator->updateGridForNewBg(roiObjectParam);
        }
    }

    //if opening saveFlag, draw and save the images in the processing.
    void ROIObjectDetector::drawAndSaveImgs(ROIObjectParam* roiObjectParam, std::string strFd) {
        // gen floders for save images 
        if (m_imgSavePathParam.saveBgImgFlag || m_imgSavePathParam.saveBinaryImgFlag || m_imgSavePathParam.saveOtherImgsFlag) {
            std::string command;
            std::vector<std::string> paths;
            paths.clear();
            paths.push_back(m_imgSavePathParam.bgDir);
            paths.push_back(m_imgSavePathParam.binaryDir);
            paths.push_back(m_imgSavePathParam.alarmDir);
            paths.push_back(m_imgSavePathParam.classifyDir);
            paths.push_back(m_imgSavePathParam.detectDir);
            paths.push_back(m_imgSavePathParam.classSegDir);
            paths.push_back(m_imgSavePathParam.detectSegDir);
            for (int i = 0; i < paths.size(); i++) {
                if (0 != access(paths[i].c_str(), 0)) {
                    command = "mkdir -p " + paths[i];
                    system(command.c_str());
                }
            }
        }
        // draw and save the imgs
        if (m_imgSavePathParam.saveBgImgFlag && roiObjectParam->isBgChanged && !roiObjectParam->bgImg.empty())
        {
            std::string bgPath = m_imgSavePathParam.bgDir + "/" + strFd + "_" + std::to_string(m_frameNum) + ".jpg";
            cv::imwrite(bgPath, roiObjectParam->bgImg);
        }
        if (m_imgSavePathParam.saveBinaryImgFlag && !roiObjectParam->binaryImg.empty())
        {
            std::string binaryPath = m_imgSavePathParam.binaryDir + "/" + strFd + "_" + std::to_string(m_frameNum) + ".jpg";
            cv::imwrite(binaryPath, roiObjectParam->binaryImg);
        }
        if (m_imgSavePathParam.saveOtherImgsFlag) {
            std::string alarmPath = m_imgSavePathParam.alarmDir + "/" + strFd + "_" + std::to_string(m_frameNum) + ".jpg";
            std::string classifyPath = m_imgSavePathParam.classifyDir + "/" + strFd + "_" + std::to_string(m_frameNum) + "_" + std::to_string(roiObjectParam->scoreDiv) + ".jpg";
            std::string detectPath = m_imgSavePathParam.detectDir + strFd + "/" + "_" + std::to_string(m_frameNum) + ".jpg";
            for (int i = 0; i < roiObjectParam->detectRects.size(); i++) {
                detectPath = detectPath + "_" + std::to_string(roiObjectParam->detectRects[i].score);
            }
            detectPath = detectPath + ".jpg";
            std::string classSegPath = m_imgSavePathParam.classSegDir + strFd + "/" + "_" + std::to_string(m_frameNum) + "_" + std::to_string(roiObjectParam->scoreDiv) + ".jpg";
            std::string detectSegPath = m_imgSavePathParam.detectSegDir + "/" + strFd + "_" + std::to_string(m_frameNum) + ".jpg";
            if (roiObjectParam->alarmRects.size() > 0) {
                for (int i = 0; i < roiObjectParam->alarmRects.size(); i++) {
                    cv::Mat frame = roiObjectParam->orginImg * 0.7;
                    cv::imwrite(alarmPath, drawRectangle(frame, roiObjectParam->alarmRects, cv::Scalar(255, 255, 255)));
                }
            }
            if (roiObjectParam->classifyRects.size() > 0) {
                for (int i = 0; i < roiObjectParam->classifyRects.size(); i++) {
                    cv::Mat frame = roiObjectParam->orginImg * 0.7;
                    cv::imwrite(classifyPath, drawRectangle(frame, roiObjectParam->classifyRects, cv::Scalar(255, 255, 255)));
                }
            }
            if (roiObjectParam->detectRects.size() > 0) {
                for (int i = 0; i < roiObjectParam->detectRects.size(); i++) {
                    cv::Mat frame = roiObjectParam->orginImg * 0.7;
                    cv::imwrite(detectPath, drawRectangle(frame, roiObjectParam->detectRects, cv::Scalar(255, 255, 255)));
                }
            }
            if (!roiObjectParam->segClassImg.empty()) {
                cv::imwrite(classSegPath, roiObjectParam->segClassImg);
            }
            if (!roiObjectParam->segDetectImg.empty()) {
                cv::imwrite(detectSegPath, roiObjectParam->segDetectImg);
            }
        }
    }

    void ROIObjectDetector::reset()
	{
        polyMaskInited = false;
        m_frameNum = 1;
        cv::Mat tmp;
        m_interestedMask = tmp;
		if(m_featureModel)
			m_featureModel->reset();

		if (m_distanceSolver)
			m_distanceSolver->reset();

		if (m_bgImgGenerator)
			m_bgImgGenerator->reset();

		if (m_staticObjGenerator)
			m_staticObjGenerator->reset();

		if (m_rectGenerator)
			m_rectGenerator->reset();

		historyObjects.clear();
        extraDetectCaches.clear();
        printDetectObjectCount = 0;
    }
}
