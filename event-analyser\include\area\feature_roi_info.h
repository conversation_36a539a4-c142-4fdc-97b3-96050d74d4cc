/**
 * Project AI事件分析模块
 */

#pragma once

#include "region_info.h"
/**
 * 车道配置信息
 */
namespace evt
{
    class FeatureRoiInfo : public RegionInfo {
    public:

        std::string beginTime;   //!< 目标提取起始时间

        //! 起始时间时、分、秒，方便tm time_t时间比较
        int beginHour   = 0;
        int beginMinute = 0;
        int beginSecond = 0;

        std::string endTime;     //!< 目标提取结束时间

        //! 结束时间时、分、秒，方便tm time_t时间比较
        int endHour   = 0;
        int endMinute = 0;
        int endSecond = 0;

    };
}
