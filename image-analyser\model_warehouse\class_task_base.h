#pragma once
#include "model_task.h"
#include "class_model.h"
#include "utils.h"
#include "config/detect_config.hpp"

namespace ia
{
    using namespace model_warehouse;

    /**
     * 通用分类任务行为基类
     */
    class ClassifierActionBase : public BaseModelAction<CLASS_RESULT>
    {
    public:
        explicit ClassifierActionBase(int deviceID = 0, int classifierType = 3, std::string modelSubPath = "") 
            : BaseModelAction(deviceID), classType(classifierType), modelSubPath(std::move(modelSubPath)) {}

        ~ClassifierActionBase() override = default;

        /**
         * 特征推理处理
         * @param input 输入数据
         */
        CLASS_RESULT process(ModelInputData& input) override 
        {
            initModels();
            CLASS_RESULT result = classModel->infer({ input.image }, true);
            return result;
        }

        /**
         * 检测模型初始化
         * 子类可以覆盖此方法以提供特定的初始化逻辑
         */
        virtual void initModels()
        {
            if (classModel == nullptr)
            {
                std::string configFilePath = DETECT_CFG->modelPath() + "/secondary/" + modelSubPath + "/";
                std::string configFile = configFilePath + "config_infer.txt";

                std::string modelName;
                if (!getConfigFileValue(configFile, "model-engine-file", modelName))
                {
                    IVA_LOG_ERROR( "Unable to get model-engine-file from {}", configFile.c_str() )
                    return;
                }

                std::string thresholdStr;
                if (!getConfigFileValue(configFile, "classifier-threshold", thresholdStr))
                {
                    IVA_LOG_ERROR( "Unable to get classifier-threshold from {}", configFile.c_str() )
                    return;
                }
                threshold = std::stof(thresholdStr);

                std::string labelsFile = DETECT_CFG->modelPath() + "/secondary/" + modelSubPath + "/labels.txt";
                labels = parseLabelsFiles(labelsFile);
                if (labels.empty())
                {
                    IVA_LOG_ERROR( "Unable to parse labels files")
                    return;
                }

                std::string classifierTypeConfigFile = DETECT_CFG->modelPath() + "/classifier_type.txt";
                if (!initClassifierTable(classifierTable, classifierTypeConfigFile))
                {
                    IVA_LOG_ERROR( "Unable to parse classifier table")
                    return;
                }
                std::string modelPath = configFilePath + modelName;
                classModel = std::make_shared<ClassModel>(modelPath, deviceId);
#ifdef NVIDIA
                classModel->setPreprocessFunc(preprocessImage);
                classModel->setNetworkInputType(NetworkInputType::CHW);
#endif
#ifdef CAMBRICON_MLU370
                classModel->setPreprocessFunc(preprocess);
#endif

                classModel->initResource();
            }
        }

        std::pair<std::string, std::string> getClassifierClassTypes(int classID)
        {
            std::pair<std::string, std::string> targetClassTypes;
            if (!classifierTable.count(classType) || !labels.count(classID))
            {
                IVA_LOG_WARN( "获取分类结果失败, class id： {}", classID)
                return targetClassTypes;
            }

            targetClassTypes.first = classifierTable[classType];
            targetClassTypes.second = labels[classID];
            return targetClassTypes;
        }

        inline float getThreshold() { return threshold; }

    protected:
        // 分类模型
        std::shared_ptr<ClassModel> classModel = nullptr;
        const int classType;
        float threshold = 0.1;
        std::string modelSubPath;
        
        // 标签
        std::map<int, std::string> labels;
        std::map<int, std::string> classifierTable;
    };

    /**
     * 通用分类器管理类模板
     * @tparam ACTION_TYPE 具体的分类器行为类型
     */
    template<typename ACTION_TYPE>
    class ClassifierManagerBase
    {
    public:
        static ClassifierManagerBase* getInstance(int deviceId, int threadNum)
        {
            static ClassifierManagerBase instance(deviceId, threadNum);
            return &instance;
        }

        explicit ClassifierManagerBase(int deviceID, int threadNum)
        {
            this->deviceId = deviceID;
            classifierAction = new ACTION_TYPE(deviceID);
            threshold = classifierAction->getThreshold();
            modelTask = std::make_shared<MODEL_TASK>(classifierAction, threadNum);
        }

        void submit(cv::Mat img, void* userData, int channel)
        {
            modelTask->submit(std::move(img), userData, channel);
        }

        void setOutputCallback(int channel, std::function<void(CLASS_RESULT)> callback)
        {
            modelTask->setOutputCallback(std::move(callback), channel);
        }

        static std::optional<int> getMaxKlass(std::vector<float>& results, float threshold)
        {
            auto itMax = std::max_element(results.begin(), results.end());
            if ((*itMax) > threshold) {
                int klass = (int)(itMax - results.begin());
                return klass;
            }
            return std::nullopt;
        }

        inline std::pair<std::string, std::string> getClassifierClassTypes(int classID)
        {
            if (classifierAction)
                return classifierAction->getClassifierClassTypes(classID);
            else
                return {};
        }

        inline float getThreshold() { return threshold; }

    private:
        int deviceId = 0;
        float threshold = 0.5;
        ACTION_TYPE* classifierAction = nullptr;
        using MODEL_TASK = ModelTask<CLASS_RESULT>;
        std::shared_ptr<MODEL_TASK> modelTask = nullptr;
    };
} 