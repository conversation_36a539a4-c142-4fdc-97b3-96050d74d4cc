﻿cmake_minimum_required (VERSION 3.5.2)

# 目标生成
set(TARGET_LIBRARY "gpu_util")
# 输出目录
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/out/lib)

find_package(CUDA)
INCLUDE_DIRECTORIES(/usr/local/cuda/include)
link_directories("/usr/local/cuda/lib64")
FILE(GLOB_RECURSE LIBS_CUDA /usr/local/cuda/lib64/*.so)

cuda_add_library(${TARGET_LIBRARY} SHARED func.cu)
TARGET_LINK_LIBRARIES(${TARGET_LIBRARY} ${LIBS_CUDA})

