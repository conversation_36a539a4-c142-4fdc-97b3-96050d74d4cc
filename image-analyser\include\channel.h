/**
 * Project IVA (image analyser)
 */
#pragma once

#include <map>
#include <mutex>
#include <atomic>
#include <omp.h>
#include "frame_data.h"

namespace ia
{
	class BaseDetector;
	class StateDetector;
	class ObjectDetector;

	/**
	* 图像检测通道
	*/
	class Channel
	{
	public:
		Channel(int id, int deviceID);
		~Channel();

		/**
		* 核心检测流程接口
		* @param frameData 输入数据
		*/
		OutputData process(const FrameData& frameData);

		/**
		* 重置通道
		*/
		void reset();

		/**
		* 设置检测器开关
		* @param type 检测器类型
		* @param enable 开关值
		*/
		void setEnable(DetectorType type, bool enable);

		/**
		* 获取检测器开关
		* @param type 检测器类型
		*/
		bool getEnable(DetectorType type);

		/**
		* 设置检测器 子区域掩码
		* @param pts 区域坐标集
		* @param detectType 检测器类型
		*/
		void setCheckAreaMask(const RegionPositions& pts, DetectorType detectType);

		/**
		* 当前帧是否需要画面帧数据
		* （提供给上游任务 帧采样频率，降低设备侧数据搬运成本）
		*/
		bool needFrameMat();

	private:
		/**
		* 获取检测器
		* @param type 检测器类型
		*/
		BaseDetector* getDetector(DetectorType type);

		/**
		* 检测器 重置标签
		*/
		std::atomic_bool dirtyFlagReset;

		/**
		* 通道号
		*/
		int channelID;

        /**
         * GPU ID
         */
        int deviceId = 0;

		std::map<DetectorType, BaseDetector*> detectors;
		std::map<DetectorType, bool> detectorStates;
		std::map<DetectorType, bool> detectorConfigStates;
		std::mutex mutexStates;

		omp_lock_t ompLock;
	};
}
