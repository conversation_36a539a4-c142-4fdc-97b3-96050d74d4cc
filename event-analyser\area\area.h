/**
 * Project AI事件分析模块
 */

#ifndef _AREA_H
#define _AREA_H

#include <memory>
#include <vector>

#include "element/polygon.h"
#include "element/vector2d.h"
#include "element/event_object.h"
#include "config/region_config.h"
#include "enum/event_type.h"
#include "enum/target_type.h"
#include "enum/area_type.h"
#include "detector/event_detector.h"
#include "detector/class_event_detector.h"
#include "map/event_map.h"
#include "map/target_map.h"
#include "element/target.h"
#include "util/mask_value.h"

/**
 * 区域 （基类）
 *
 * 派生 车道，子区域，ROI等
 */
namespace evt
{
	using std::chrono::steady_clock;

	class TargetEventDetector;
	class ObjectEventDetector;

	class SceneState;
	class Area {
	public:
		friend class ROI;

		Area();
		virtual ~Area();

		/**
		 * 获取ID
		 */
		int getID();

		/**
		 * 检测事件
		 */
		void detectEvents();

		/**
		 * 清除目标
		 */
		virtual void clearTargets();

		/**
		 * 清除事件物体 (抛洒物等)
		 */
		virtual void clearEventObjects();

		/**
		 * 匹配目标
		 */
		virtual bool matchTarget(TargetPtr target);

		/**
		 * 增加目标
		 * @param target
		 */
		bool addTarget(TargetPtr target);

		/**
		 * 增加事件物体 (抛洒物等)
		 * @param object
		 */
		bool addEventObject(EventObject& object);

		/**
		 * 当前区域目标数量
		 */
		int targetCount();

		/**
		 * 设置事件地图
		 * @param map
		 */
		virtual void setEventMap(EventMap* map);

		/**
		 * 设置目标地图
		 * @param map
		 */
		virtual void setTargetMap(TargetMap* map);

		/**
		 * 设置检测尺寸
		 */
		virtual void setFrameSize(int w, int h);

		/**
		 * 设置开始检测时间
		 */
		virtual void setStartDetectTime(steady_clock::time_point time);

		/**
		 * 获取区域热力值
		 */
		float getHeatValue();

		/**
		 * 获取区域占比
		 */
		float getOccupiedValue();

		/**
		 * 是否是免检区
		 */
		bool isIgnoreArea();

        /**
         * 是否是图像子检测区
         */
        bool isImageDetectAreaOnly();

		/**
		 * 是否检测事件类型
		 * @param type
		 */
		bool hasEventType(EventType type);

		/**
		 * 是否检测目标类型
		 * @param type
		 */
		bool hasTargetType(TargetType type);

		/**
		 * 是否检测ROI事件类型
		 * @param type
		 */
		bool hasROIEventType(EventType type);

		/**
		 * 更新ROI事件掩码
		 * 如果子区域勾选了 ROI事件类型 则更新ROI事件掩码、
		 * 【即】：子区域仅决定目标是否用于检测、事件由ROI上报
		 * @param roiMask
		 */
		void updateROIEventMask(MaskValue& roiMask);

		/**
		 * 是否包含目标
		 * @param target
		 */
		bool containTarget(TargetPtr target);

		/**
		 * 是否包含坐标
		 * @param point
		 */
		bool containPosition(Point point);

		/**
		 * 是否包含热力图序号
		 * @param index
		 */
		bool containHeatmapIndex(int index);

		/**
		 * 获取周围目标
		 * @param pos 目标位置
		 * @param padding 查找范围
		 */
		TargetList getAdjacentTargets(Point pos, int padding);

		/**
		 * 获取多边形区域
		 */
		Polygon getFramePolygon() { return framePolygon; };

		/**
		 * 获取配置方向
		 */
		Vector2D getDirection() { return direction; };

		/**
		 * 场景是否偏移 (图像)
		 */
		bool isOffset() { return offsetState; }

		/**
		 * 场景是否偏移 (交通流)
		 */
		bool inShifting();

		/**
		 * 获取开始检测时间
		 */
		const steady_clock::time_point& getStartDetectTime() { return startTime; };

		/**
		 * 设置施工状态
		 */
		virtual void setConstructionState(bool inConstruction) { constructionState = inConstruction; }

	protected:

		/**
		 * 预处理已有事件
		 */
		void preprocessEvents();

		/**
		 * 初始化检测器
		 * @param globalTypes 全局考虑的类型
		 */
		virtual void initDetectors(std::vector<EventType>& globalTypes);

		/**
		 * 检查目标尺寸是否满足
		 * @param target
		 */
		bool checkTargetSize(TargetPtr target);

		/**
		 * 初始化通用参数
		 */
		void onConfigUpdated();

		/**
		 * 更新已有事件匹配
		 */
		void onTargetEventUpdate(TargetEventDetector* detector);

		/**
		 * 更新物体事件匹配
		 */
		void onObjectEventUpdate(ObjectEventDetector* detector);

        /**
         * 更新分类事件匹配
         */
        void onClassEventUpdate(ClassEventDetector* detector);

		/**
		* 唯一ID
		*/
		int id;

		/**
		* 所属ROI ID
		*/
		int roiID;

		/**
		 * 事件掩码
		 */
		MaskValue eventMask;

		/**
		 * ROI事件掩码
		 */
		MaskValue roiEventMask;

		/**
		 * 目标掩码
		 */
		MaskValue targetMask;

		/**
		 * 多边形区域坐标
		 */
		Polygon polygon;

		/**
		 * 多边形区域实际尺寸
		 */
		Polygon framePolygon;

		/**
		 * 配置方向
		 */
		Vector2D direction;

		/**
		 * 个性化参数配置
		 */
		RegionConfig config;

		/**
		 * 所属事件地图
		 */
		EventMap* eventMap;

		/**
		 * 所属目标地图
		 */
		TargetMap* targetMap;

		/**
		 * 区域包含的目标列表
		 */
		std::vector<TargetPtr> targetList;

		/**
		 * 区域包含的事件目标列表
		 */
		EventObjectList eventObjList;

		/**
		 * 区域包含的事件检测器
		 */
		std::vector<EventDetector*> detectors;

		/**
		 * 检测宽高
		 */
		int frameWidth;
		int frameHeight;

		/**
		 * 目标注册点 X、Y
		 */
		float targetRegistX = 0.5f;
		float targetRegistY = 0.9f;

		/**
		 * 是否是免检区
		 */
		bool ignored;

		/**
		 * 区域类型
		 */
		AreaType areaType;

		/**
		 * 区域是否拥堵
		 */
		bool jamState;

		/**
		 * 区域是否在施工
		 */
		bool constructionState = false;

		/**
		 * 区域是否偏移
		 */
		bool offsetState = false;

		/**
		 * 场景状态
		 */
		SceneState* sceneState;

		/**
		 * 区域参与ROI事件计算的目标列表
		 */
		std::map<EventType, std::vector<TargetPtr>> roiEventTargets;

		/**
		 * 热力图序号索引表
		 */
		std::vector<int> heatmapIndexs;

		/**
		 * 开始检测时间
		 */
		steady_clock::time_point startTime;

		//------------------通用参数------------------//
		// 机动车最大尺寸 画面比
		float maxSizeVehicle = 0.4f;

		// 机动车最小尺寸
		float minSizeVehicle = 0.03f;

		// 行人最大尺寸
		float maxSizePedstrain = 0.2f;

		// 行人最小尺寸
		float minSizePedstrain = 0.02f;
	};
}
#endif //_AREA_H
