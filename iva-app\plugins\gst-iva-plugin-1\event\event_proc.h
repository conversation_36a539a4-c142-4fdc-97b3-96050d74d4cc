#pragma once
#include "ivameta.h"
#include "event_analyser.h"
#include "opencv_frame_osd.h"

/*
*  更新事件 截图和上传
*/
void updateEvents(int channelID, std::vector<evt::EventInfo>* events, cv::Mat mat, int framenum, float scale_x, float scale_y, guint gpu_id);

/*
*  目标过线回调
*/
void onTargetPassedCallback(evt::TargetInfoList& targets);

/*
* 撤销事件回调
*/
void onEventWithdrawCallback(evt::EventInfo ei);

/**
 *  车辆目标上报回调
 */
void onVehiclesInfoCallback(const std::vector<evt::TargetInfo>& targets);

/*
* 获取事件类型的中文名字
* @param[int] type 事件类型枚举
* @return 	  事件类型的中文名字
*/
std::string getEventTypeName(evt::EventType type);

/*
 *  截图
 */
void snapImg(const cv::Mat& mat, int videoId, int index, int presetId, float scale_x, float scale_y);
