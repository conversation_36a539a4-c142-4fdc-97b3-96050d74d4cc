/**
 * Project AI事件分析模块
 */


#ifndef _POLYGON_H
#define _POLYGON_H

#include "module_def.h"
#include <vector>
#include "point.h"
#include "rect.h"

/**
* 多边形
*/
namespace evt
{
	class EVT_EXPORT Polygon {
	public:
		Polygon();

		Polygon(std::vector<Point>& pointList);

		void init(std::vector<Point>& pointList);
		/**
		 * 按实际尺寸缩放
		 * @param w
		 * @param h
		 */
		void scale(int w, int h);

		/**
		 * 多边形数据是否正确
		 */
		bool isValid();

		/**
		 * 是否包含点
		 * @param p
		 */
		bool containPoint(Point p);

		/**
		 * 外接矩形
		 */
		Rect boundingBox();

		/**
		 * 获取坐标点
		 * @param index 坐标点序号
		 */
		Point getPoint(int index);

		/**
		 * 多边形点数量
		 */
		int pointCount();

	private :

		/**
		 * 更新外接矩形
		 */
		void updateBoundingBox();

		/**
		 * 多边形坐标点集合
		 */
		std::vector<Point> points;

		Rect boundingRect;
	};
}
#endif //_POLYGON_H