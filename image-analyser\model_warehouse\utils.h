#pragma once
#include <map>
#include <iostream>
#include <fstream>
#include <experimental/filesystem>
#include "log.h"

namespace model_warehouse
{

    const float IMAGENET_MEAN[3] = {0.485f, 0.456f, 0.406f};
    const float IMAGENET_STD[3] = {0.229f, 0.224f, 0.225f};

    /**
     * @brief 天气分类图像预处理函数
     * @param[in] images  输入帧
     * @param[in] dstHeight  输出帧高度
     * @param[in] dstWidth  输出帧宽度
     * @param[out] imageDat 预处理后的输出帧
     */
    inline void preprocessImage(std::vector<cv::Mat>& images, int dstHeight, int dstWidth, std::vector<float>& imageDat)
    {
        size_t batchSize = images.size();
        size_t inputSizePerImage = 3 * dstHeight * dstWidth;

        imageDat.resize(batchSize * inputSizePerImage);

        for (size_t b = 0; b < batchSize; ++b)
        {
            cv::Mat img = images[b];
            cv::Mat imgResized;

            // img_resized
            cv::resize(img, imgResized, cv::Size(dstWidth, dstHeight), 0, 0, cv::INTER_LINEAR);
            cv::cvtColor(imgResized, imgResized, cv::COLOR_BGR2RGB);

            imgResized.convertTo(imgResized, CV_32FC3, 1.0 / 255);

            // Subtract mean and divide by std
            std::vector<cv::Mat> channels(3);
            cv::split(imgResized, channels);

            channels[0] = (channels[0] - IMAGENET_MEAN[0]) / IMAGENET_STD[0]; // R channel
            channels[1] = (channels[1] - IMAGENET_MEAN[1]) / IMAGENET_STD[1]; // G channel
            channels[2] = (channels[2] - IMAGENET_MEAN[2]) / IMAGENET_STD[2]; // B channel

            // Convert HWC to CHW and store in imageDat
            size_t channelSize = dstHeight * dstWidth;
            float* dataPtr = imageDat.data() + b * inputSizePerImage;

            for (int c = 0; c < 3; ++c)
                memcpy(dataPtr + c * channelSize, channels[c].ptr<float>(), channelSize * sizeof(float));
        }
    }


    inline cv::Mat preprocess(cv::Mat srcImg, int dstHeight, int dstWidth, bool keepAspectRatio = false)
    {
        if (srcImg.empty() || dstHeight <= 0 || dstWidth <= 0)
        {
            IVA_LOG_ERROR("Invalid input image or target size, dstHeight {} dstWidth {}", dstHeight, dstWidth);
            return {};
        }

        cv::Mat processed;
        if (keepAspectRatio)
        {
            // 保持宽高比进行缩放并填充
            int imh = srcImg.rows;
            int imw = srcImg.cols;

            double scale = std::min(static_cast<double>(dstWidth) / imw, static_cast<double>(dstHeight) / imh);
            int newWidth = std::max(1, static_cast<int>(imw * scale));
            int newHeight = std::max(1, static_cast<int>(imh * scale));

            // 缩放图像
            cv::resize(srcImg, processed, cv::Size(newWidth, newHeight), 0, 0, cv::INTER_LINEAR);

            // 创建目标尺寸的黑色背景
            cv::Mat padded = cv::Mat::zeros(dstHeight, dstWidth, processed.type());

            // 将缩放后的图像居中粘贴到 padded 中
            int x = std::max(0, (dstWidth - newWidth) / 2);
            int y = std::max(0, (dstHeight - newHeight) / 2);
            processed.copyTo(padded(cv::Rect(x, y, newWidth, newHeight)));

            // 颜色空间转换
            // cv::cvtColor(padded, padded, cv::COLOR_BGR2RGB);
            return padded;
        }
        else
        {
            cv::Mat imgResized;
            cv::resize(srcImg, imgResized, cv::Size(dstWidth, dstHeight), 0, 0, cv::INTER_LINEAR);
            cv::cvtColor(imgResized, imgResized, cv::COLOR_BGR2RGB);
            return imgResized;
        }
    }

    inline std::optional<int> getMaxKlass(std::vector<float>& results, float threshold)
    {
        auto itMax = std::max_element(results.begin(), results.end());
        if ((*itMax) > threshold) {
            int klass = (int)(itMax - results.begin());
            return klass;
        }
        return std::nullopt;
    }

    inline std::map<int, std::string> parseLabelsFiles(const std::string &labelsFile)
    {
        std::map<int, std::string> labels;

        try {

            if (!std::experimental::filesystem::exists(labelsFile) || (std::experimental::filesystem::is_empty(labelsFile))) {
                IVA_LOG_ERROR("\n\n\n分类标签配置文件{}不存在或者配置为空\n\n\n", labelsFile)
                return labels;
            }

            std::ifstream infile(labelsFile);
            std::string lineContent;
            int classId = 0;
            while (std::getline(infile, lineContent)) {
                if (!lineContent.empty()) {
                    labels[classId] = lineContent;
                    classId++;
                }
            }

            if (labels.empty()) {
                IVA_LOG_ERROR("\n\n\n标签配置文件{} 配置错误\n\n\n", labelsFile)
                return labels;
            }

        }
        catch (const std::exception &e) {
            IVA_LOG_ERROR("标签配置文件解析错误: {}", e.what())
            return labels;
        }
        return labels;
    }

    inline bool getConfigFileValue(const std::string &filename, const std::string &fieldName, std::string &value)
    {
        std::ifstream file(filename);
        std::string line;
        if (!file.is_open()) {
            IVA_LOG_ERROR("Unable to open file: {}", filename.c_str());
            return false;
        }

        while (std::getline(file, line)) {
            // 去除行首尾的空格
            line = line.substr(line.find_first_not_of(" \t"));

            // 如果是注释或空行，则跳过
            if (line.empty() || line[0] == '#')
                continue;

            // 查找字段名
            if (line.find(fieldName) == 0) {
                std::string::size_type pos = line.find('=');
                if (pos != std::string::npos) {
                    // 提取等号后面的部分并去除首尾空格
                    value = line.substr(pos + 1);
                    value = value.substr(value.find_first_not_of(" \t"));
                    value = value.substr(0, value.find_last_not_of(" \t") + 1);
                    return true;
                }
            }
        }

        file.close();
        return false;
    }

    inline void split(const std::string& input, std::vector<std::string>& tokens, const std::string& delimiters = " ")
    {
        auto lastPos = input.find_first_not_of(delimiters, 0);
        auto pos = input.find_first_of(delimiters, lastPos);
        while (std::string::npos != pos || std::string::npos != lastPos)
        {
            tokens.emplace_back(input.substr(lastPos, pos - lastPos));
            lastPos = input.find_first_not_of(delimiters, pos);
            pos = input.find_first_of(delimiters, lastPos);
        }
    }

    /**
     *	从config文件读取classifierId与classifierType的映射关系
     */
    inline bool initClassifierTable(std::map<int, std::string>& classifierTable, const std::string& configFile)
    {
        if (!std::experimental::filesystem::exists(configFile) || (std::experimental::filesystem::is_empty(configFile)))
        {
            IVA_LOG_ERROR("\n\n\n分类器类型配置文件{}不存在或者配置为空\n\n\n", configFile);
            return false;
        }

        std::ifstream infile(configFile);
        std::string lineContent;
        int fileLine = 0;
        while (std::getline(infile, lineContent))
        {
            ++fileLine;
            std::vector<std::string> token;
            split(lineContent,token,",");
            if (token.size() != 2)
            {
                IVA_LOG_ERROR("\n\n\n分类器类型配置文件{},{}行:{} 配置数量错误，请按此格式配置:3,personClothes\n\n\n" ,configFile, fileLine, lineContent);
                continue;
            }

            try{
                int classifierId = std::stoi(token[0]);
                classifierTable[classifierId] = token[1];
            }
            catch (std::exception& e){
                IVA_LOG_ERROR("\n\n\n读取分类器配置文件{},{}行:{} 失败：非法分类器ID:{}, 错误码:{}, 请按此格式配置:3,personClothes \n\n\n" , configFile, fileLine, lineContent, token[0], e.what());
                return false;
            }
        }
        return true;
    }

}

