/**
 * Project AI事件分析模块
 */


#ifndef _CHANNEL_STATE_H
#define _CHANNEL_STATE_H

#include <vector>
#include <list>
#include <map>
#include <set>
#include "element/target.h"
#include "area/roi.h"
#include "area/region.h"
#include "element/vector2d.h"

 /**
 * 通道场景状态
 *
 * 1. 处理偏移
 * 2. 处理拥堵过滤
 */
namespace evt
{

	// 统计周期 最小可信 数量
#define TRAFFIC_FLOW_MIN_COUNT 9

// 交通流正常 最小可信 速度
#define TRAFFIC_FLOW_NORMAL_MIN_VELOCITY 3.f

// 交通流低速 最小可信 速度
#define TRAFFIC_FLOW_SLOW_MIN_VELOCITY 1.f

// 交通流 偏移 夹角
#define TRAFFIC_FLOW_ANGLE_SHIFTED 60

// 交通流 夹角 容差
#define TRAFFIC_FLOW_ANGLE_TOLERANCE 30 

// 偏移 处理时长（帧数）
#define TRAFFIC_FLOW_SHIFTED_PERIOD 500

// 偏移恢复 满足计数
#define TRAFFIC_FLOW_RESET_COUNT 100

// 偏移计算 最小贡献单元 数量
#define TRAFFIC_FLOW_MIN_UNIT_COUNT 2

//帧，防止久远目标影响交通流计算
#define TARGET_FLOW_FRAME_DIFF 750 //约30秒

//交通流大小统计周期
#define TRAFFIC_FLOW_CHECK_PERIOD 1500 //约60秒

/**
 * 目标方向流信息
 */
	struct TargetFlowInfo
	{
		//目标id
		int targetId;

		// 方向
		Vector2D direction;

		// 序号
		uint index;

		//与配置方向夹角
		float angle;

		TargetFlowInfo(int targetId, Vector2D direction, uint index, float angle) :
			targetId(targetId), direction(direction), index(index), angle(angle) {}
	};

	/**
	 * 车流信息
	 */
	struct TrafficFlow
	{
		// 配置方向
		Vector2D cfgDirection;

		// 高速交通流数据
		std::list<TargetFlowInfo> flowListFast;

		//低速交通流数据
		std::list<TargetFlowInfo> flowListSlow;

		//统计一定时间车流量
		std::map<int, int> flowSum;

		// 交通流方向
		Vector2D direction;

		// 单位数量
		std::set<int> unitCount;

		//正常情况下交通流激活
		bool fastActive = false;

		//低速情况下交通流激活
		bool slowActive = false;

		/**
		 * 此roi偏移时序号
		 */
		uint shiftedIndex = 0;
		/**
		 * 当前roi是否疑似偏移
		 */
		bool shifted = false;
		/**
		 * 当前roi偏移恢复计数
		 */
		int resetCount = 0;
		/**
		 * 有目标穿越roi,偏移判定优先级高于shifted
		 */
		bool passRoi = false;
		/**
		 * 穿越roi数量
		 */
		int passCount = 0;

		int trafficFlowMinCount = TRAFFIC_FLOW_MIN_COUNT;
	};

	/**
	* 通道场景状态
	*/
	class SceneState {
		friend class Scene;
	public:

		SceneState();

		/**
		 * 初始化ROI相关信息
		 */
		void init(std::vector<ROI*>& roiList);

		/**
		 * 当前场景是否疑似偏移
		 */
		bool isShifted();

		/**
		 * 当前场景是否刚发生偏移
		 */
		bool isShifting();

		/**
		 * 当前场景ROI是否拥堵
		 */
		bool inJam(int roiID);

	private:
		/**
		 * 更新场景状态开始
		 */
		void onUpdateStart();

		/**
		 * 更新场景状态
		 * @param target
		 * @param roi
		 */
		void onUpdate(TargetPtr target, ROI* roi);

		/**
		 * 更新场景拥堵情况
		 * @param evt
		 * @param roi
		 */
		void onUpdateJam(EventPtr evt);

		/**
		 * 更新场景状态结束
		 */
		void onUpdateEnd();

		/**
		 * ROI 车流状态
		 */
		std::map<int, TrafficFlow> roiFlows;

		/**
		 * ROI 拥堵状态
		 */
		std::map<int, bool> roiInJam;

		/**
		 * 当前累计序号
		 */
		uint currentIndex;
	};
}
#endif //_CHANNEL_STATE_H
