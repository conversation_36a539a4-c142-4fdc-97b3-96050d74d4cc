
#include <cstring>
#include <iostream>
#include "nvdsinfer_custom_impl.h"
#include <cassert>
#include <cmath>

#define MIN(a,b) ((a) < (b) ? (a) : (b))
#define MAX(a,b) ((a) > (b) ? (a) : (b))
#define CLIP(a,min,max) (MAX(MIN(a, max), min))
#define DIVIDE_AND_ROUND_UP(a, b) ((a + b - 1) / b)

extern "C"
bool NvDsInferParseCustomDetection(std::vector<NvDsInferLayerInfo> const& outputLayersInfo,
    NvDsInferNetworkInfo  const& networkInfo,
    NvDsInferParseDetectionParams const& detectionParams,
    std::vector<NvDsInferObjectDetectionInfo> &objectList) {
    if (outputLayersInfo.size() != 2)
    {
        std::cerr << "Mismatch in the number of output buffers."
            << "Expected 2 output buffers, detected in the network :"
            << outputLayersInfo.size() << std::endl;
        return false;
    }

    float* p_scores = (float*)outputLayersInfo[0].buffer;
    float* p_bboxes = (float*)outputLayersInfo[1].buffer;

    int output_num = outputLayersInfo[1].inferDims.d[0];
    int class_num = outputLayersInfo[0].inferDims.d[1];
    int box_num = outputLayersInfo[1].inferDims.d[2];

    const float threshold = detectionParams.perClassThreshold[0];

    //const int keep_top_k = 10000;
    const char* log_enable = std::getenv("ENABLE_DEBUG");

    //static bool showOnce = false;
    for (int i = 0; i < output_num /*&& objectList.size() <= keep_top_k*/ ; i++)
    {
        int index = -1;
        float scoreMax = -1;
        for (int j = 0; j < class_num; j++) {
            if (scoreMax < p_scores[i * class_num + j]) {
                scoreMax = p_scores[i * class_num + j];
                index = j;
            }
        }

        if (scoreMax <= threshold) continue;

       // assert(class_num < detectionParams.numClassesConfigured);

        float x1 = p_bboxes[4 * i];
        float y1 = p_bboxes[4 * i + 1];
        float x2 = p_bboxes[4 * i + 2];
        float y2 = p_bboxes[4 * i + 3];

        //if (!showOnce)
        //{
        //    std::cout << "scoreMax : " << scoreMax << " x1: " << p_bboxes[4 * i]
        //        << " y1: " << p_bboxes[4 * i + 1]
        //        << " x2: " << p_bboxes[4 * i + 2]
        //        << " y2: " << p_bboxes[4 * i + 3]
        //        << std::endl;
        //}

        if (p_bboxes[4 * i + 2] < p_bboxes[4 * i] || p_bboxes[4 * i + 3] < p_bboxes[4 * i + 1]) continue;

        NvDsInferObjectDetectionInfo object;
        object.classId = index;
        object.detectionConfidence = scoreMax;

        /* Clip object box co-ordinates to network resolution */
        object.left = CLIP(p_bboxes[4 * i], 0, networkInfo.width - 1);
        object.top = CLIP(p_bboxes[4 * i + 1], 0, networkInfo.height - 1);
        object.width = CLIP((p_bboxes[4 * i + 2] - p_bboxes[4 * i]), 0, networkInfo.width - 1);
        object.height = CLIP((p_bboxes[4 * i + 3] - p_bboxes[4 * i + 1]), 0, networkInfo.height - 1);

        objectList.push_back(object);
    }
    // showOnce = true;
    return true;
}

/* Check that the custom function has been defined correctly */
CHECK_CUSTOM_PARSE_FUNC_PROTOTYPE(NvDsInferParseCustomDetection);

