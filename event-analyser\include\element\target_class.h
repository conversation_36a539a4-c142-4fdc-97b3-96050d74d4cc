//
// Created by Administrator on 2022/6/27.
//

#pragma once
#include <iostream>
#include <map>
#include <vector>
#include <mutex>
#include <memory>

namespace evt
{
    constexpr auto VEHICLE_COLOR_CLASSIFIER = "vehicleColor";  //!< 车辆颜色分类器
    constexpr auto VEHICLE_TYPE_CLASSIFIER = "vehicleType";    //!< 车辆类型分类器

    /**
     * 分类信息
     */
    struct ClassInfo
    {
        std::string classifierType; //!< 分类器类型
        std::string label;          //!< 分类标签
        int count = 0;              //!< 分类检出次数
    };

    /**
     * 目标对象的分类信息
     */
    class TargetClass
    {
    public:

        /**
         * 更新目标的分类信息
         */
        void updateClassType(const std::map<std::string, std::string>& classTypes);

        /**
         * 获取目标所有分类信息
         * @param[in] realtime 是否获取实时类型
         * @return key: classifierType value: label
         */
        inline std::map<std::string, std::string> getClassTypes(bool realtime = false){return realtime ? realtimeClassTypes : topClassTypes;}

        /**
         * 获取目标在分类器下的分类信息 eg: 分类器vehicleColor下的分类信息：车颜色red
         * @param[in] realtime 是否获取实时类型
         * @return label
         */
         std::string getClassType(std::string classifierType, bool realtime = false);

    private:
        std::map<std::string, std::string>  topClassTypes;            //!< 经过过滤后的目标分类信息，取出现次数最高的分类类型
        std::map<std::string, std::vector<ClassInfo>> allClassTypes;  //!< 当前目标下出现的所有分类类型 key: classifierType
        std::map<std::string, std::string>  realtimeClassTypes;       //!< 实时分类类型，未经过投票的
    };
}




