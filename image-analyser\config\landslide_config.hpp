#pragma once
#include "ini/inibase.h"

/*
 * 塌方 检测配置 (INI)
 *
 *   (属性类型, 属性名称, 组名称, 默认值, "注释")
 *   属性类型支持：int bool string double
 */
DEFINE_INI_CONFIG(ia::landslide, "config/landslide.ini",
                          (double, ratioThres,	    landslide, 0.1,	 u8"目标框和子区域重叠的IOU阈值")
                          (double, iouThres,	    landslide, 0.85,  u8"目标框和子区域重叠的IOU阈值")
                          (int,    matchedCount,	landslide,   3,	 u8"在缓存帧中出现该目标多少次")
                          (bool,   enablePostprocess, landslide, true, u8"是否开启后处理,默认为true")
                          (int,    cacheSize,		 landslide,    5, u8"检测结果历史缓存")
                          (int,    inferInterval,	 landslide,    25*10, u8"推理配置的抽帧数")
)

#define LANDSLIDE_CFG ia::landslide::Config::instance()
