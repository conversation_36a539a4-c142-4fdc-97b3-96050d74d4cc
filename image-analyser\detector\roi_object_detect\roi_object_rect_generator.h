#pragma once

#include "roi_object_parms.h"
#include "util/algorithm_util.h"
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

using namespace algorithm_util;

namespace roi_object_detect{

    class RectGenerator{
        public:
            RectGenerator(SelectRectParam& selectRectParam);

            //updata params
            void updateParams(SelectRectParam& selectRectParam);
            void reset();

            //calculate the foreground rect of the current frame by combining the information of the previous frame
            void getBoxes(ROIObjectParam* roiObjectParam,bool catBinaryFlag);
            void getBoxesGrid(ROIObjectParam* roiObjectParam,bool catBinaryFlag);

            //calculate the diffirent regions between the new bgImg and the old bgImg
            void getBoxesForTowBg(ROIObjectParam* roiObjectParam);

        private:
            //Generating pictures with distance vectors
            void genImgfromDistanceScore(ROIObjectParam* roiObjectParam);

            //draw and cat pictures,use  to observe the process for debug
            void catBinary(ROIObjectParam* roiObjectParam);

            //get rects through calculation
            void calBoxes(ROIObjectParam* roiObjectParam);
            void calBoxesGrids(ROIObjectParam* roiObjectParam);

            //get roi by seg imgs
            void getRoiImgs(ROIObjectParam* roiObjectParam);

            //Analyze whether the object has been thrown, and record the Status to alarmFlag
            void calAlarmFlags();
            void calAlarmFlagsGrid(ROIObjectParam* roiObjectParam);

            //filter rectangular boxes by scale / aspect ratio / region of interest / iou , etc
            std::vector<ObjBox> selectRects();

            //filter rectangular boxes by scale / aspect ratio / region of interest / iou , etc
            void selectRectGrid(ROIObjectParam* roiObjectParam);

            //scale the coords
            void resizeRects(ROIObjectParam* roiObjectParam);

        private:
            // parameters needed in this class
            SelectRectParam m_selectRectParam;

            // how many lanes is each road
            std::vector<float> m_carRoadNumsList;

            //Make sure the base zero img is generated only once
            bool m_isFirstGenImg;

            //imgs generating  from distance vectors , use to generate binarys
            cv::Mat m_imgBgDis;
            cv::Mat m_imgSingle1;
            cv::Mat m_imgSingle2;
            cv::Mat m_imgSingle4;
            cv::Mat m_imgSingle5;
            cv::Mat m_imgMulti;

            //bianrys: used to calculate rects of current frame
            cv::Mat m_binaryBgDis;
            cv::Mat m_binarySingle1;
            cv::Mat m_binarySingle2;
            cv::Mat m_binarySingle4;
            cv::Mat m_binarySingle5;
            cv::Mat m_binaryMulti;
            cv::Mat m_diffBinary;
            cv::Mat m_binary;

            // Used to store regions represented by gridCoords
            std::vector<std::vector<GridCoord>> m_indicesRes;

            //output values
            std::vector<ObjBox> m_currRects; // output rects
            std::vector<cv::Mat> m_currRois; // output rois seg from binary
            std::vector<bool> m_rectAlarmFlags; // output the alarmFlag
    };
}
