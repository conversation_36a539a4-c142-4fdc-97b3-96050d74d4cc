#include "base_model.h"
#ifdef  CAMBRICON_MLU370
#include "cnrt.h"
#endif
#include "log.h"


#define SHARED_PTR_U8_BUF(buf) (std::shared_ptr<uint8_t>((uint8_t *)(buf), [](uint8_t* p) { delete[](p); }))

namespace model_warehouse
{
    static void printModelInfo(magicmind::IModel *model , std::vector<magicmind::IRTTensor *> inputTensors)
    {
        std::cout << "================== Model Info  ====================" << std::endl;
        std::cout << "Input number : " << model->GetInputNum() << std::endl;
        for (int i = 0; i < model->GetInputNum(); ++i)
        {
            std::cout << "Layout[" << i << "] : " << magicmind::LayoutEnumToString(inputTensors[i]->GetLayout()) << std::endl;
            std::cout << "input [" << i << "] : dimensions " << model->GetInputDimension(i) << ", data type [" << model->GetInputDataType(i) << "]" << std::endl;
        }
        std::cout << "Output number : " << model->GetOutputNum() << std::endl;
        for (int i = 0; i < model->GetOutputNum(); ++i)
            std::cout << "output[" << i << "] : dimensions " << model->GetOutputDimension(i) << ", data type [" << model->GetOutputDataType(i) << "]" << std::endl;
    }

    /**
     * 加载序列化模型
     * @param path 模型路径
     * @return
     */
    bool CudaPredictor::init(std::string path, int deviceID, bool verbose)
    {
        if (isInit)
            return true;

        CNRT_CHECK(cnrtSetDevice(deviceID));
        CNRT_CHECK(cnrtQueueCreate(&queue));
        model = magicmind::CreateIModel();
        ASSERT_LOG(nullptr != model, "CreateIModel failure");

        auto status = model->DeserializeFromFile(path.c_str());
        ASSERT_LOG(status == magicmind::Status::OK(), "deserializeFromFile: " + path + " failure: " + status.ToString());

        auto inputDims = model->GetInputDimensions();
        engine = model->CreateIEngine();
        ASSERT_LOG(nullptr != engine, "CreateIEngine failure");

        context = engine->CreateIContext();
        ASSERT_LOG(nullptr != context, "CreateIContext failure");

        status = context->CreateInputTensors(&inputTensors);
        ASSERT_LOG(status == magicmind::Status::OK(), "CreateInputTensors failure: " + status.ToString());

        status = context->CreateOutputTensors(&outputTensors);
        ASSERT_LOG(status == magicmind::Status::OK(), "CreateOutputTensors failure: " + status.ToString());

        //maxBatchSize = maxBatch;
        this->deviceId = deviceID;
        isInit = true;

        printModelInfo(model, inputTensors);
        return true;
    }

    void CudaPredictor::finalize()
    {
        if (queue)
        {
            cnrtQueueDestroy(queue);
            queue = nullptr;
        }

        for (auto & inputTensor : inputTensors)
        {
            if (inputTensor)
                inputTensor->Destroy();
        }
        inputTensors.clear();

        for (auto & outputTensor : outputTensors)
        {
            if (outputTensor)
                outputTensor->Destroy();
        }
        outputTensors.clear();

        if (context)
        {
            context->Destroy();
            context = nullptr;
        }

        if (engine)
        {
            engine->Destroy();
            engine = nullptr;
        }
        if (model)
        {
            model->Destroy();
            model = nullptr;
        }
        isInit = false;
    }


    int CudaPredictor::getBatchSize()
    {
        assert(model);
        assert(inputTensors[0]);
        switch (inputTensors[0]->GetLayout())
        {
            case magicmind::Layout::NCHW:
            case magicmind::Layout::NHWC:
            case magicmind::Layout::NDHWC:
            case magicmind::Layout::NCDHW:
                return (int)model->GetInputDimension(0)[0];
            case magicmind::Layout::HWC:
            case magicmind::Layout::CHW:
                return 1;
            case magicmind::Layout::HWCN:
                return (int)model->GetInputDimension(0)[3];
            case magicmind::Layout::DHWCN:
                return (int)model->GetInputDimension(0)[4];
            default:
                return 1;
        }
    }

    /**
 * @brief 创建/绑定模型输入数据相关的tensor
 * @param [in] inferInputs: n个输入节点的输数据
 * @param [in] inputBatch: 输入帧batch数
 */
    bool CudaPredictor::createInput(std::vector<ModelInput>& inferInputs, int inputBatch)
    {
        //CNRT_CHECK(cnrtSetDevice(deviceId));
        if (inferInputs.empty())
        {
            IVA_LOG_ERROR("inferInputs is empty");
            return false;
        }

        auto batch = getBatchSize();
        /// 固定batch模型，输入数据batch必须与模型batch一致
        if (inputBatch != batch)
        {
            IVA_LOG_ERROR( "inputBatch{} not equal to model batch{}", inputBatch, batch);
            return false;
        }

        for (int i = 0; i < model->GetInputNum(); ++i)
        {
            if (inputTensors[i]->GetSize() != inferInputs[i].size)
            {
                IVA_LOG_ERROR( "inputTensors{}.size{} not equal to inferInputs{}.size{}", i, inputTensors[i]->GetSize(), i, inferInputs[i].size);
                return false;
            }

            auto layerDeviceBuffer = inferInputs[i].data;
            if (layerDeviceBuffer == nullptr)
            {
                IVA_LOG_ERROR( "failed to get input device buffer from pool");
                return false;
            }

            auto status = inputTensors[i]->SetData(layerDeviceBuffer);
            if (status != magicmind::Status::OK())
            {
                IVA_LOG_ERROR( "input tensors setData failed: {}", status.ToString().c_str());
                return false;
            }
        }
        return true;
    }

    bool CudaPredictor::createInput(std::vector<cv::Mat>& images, int inputBatch, std::vector<ModelInput>& inferInputs)
    {
        if (images.empty())
        {
            IVA_LOG_ERROR( "images is empty");
            return false;
        }

        for (auto & image : images)
        {
            auto imageSize = image.cols * image.rows * 3;
            ModelInput inferenceInput;

            auto ret = cnrtMalloc(&inferenceInput.data, imageSize * inputBatch);
            if (ret != cnrtSuccess)
            {
                IVA_LOG_ERROR("devMalloc failed, errorCode = {}", ret);
                return false;
            }

            ret = cnrtMemcpy((uint8_t*)inferenceInput.data,  image.data, imageSize, CNRT_MEM_TRANS_DIR_HOST2DEV);
            if (ret != cnrtSuccess)
            {
                cnrtFree(inferenceInput.data);
                IVA_LOG_ERROR( "failed to copy image data to batch buffer, errorCode = {}", ret);
                return  false;
            }
            inferenceInput.size = imageSize;
            inferInputs.emplace_back(inferenceInput);
        }
        return createInput(inferInputs, inputBatch);
    }


    /**
     * @brief 创建/绑定模型输出数据相关的tensor
     */
    bool CudaPredictor::createOutput()
    {
        auto status = context->InferOutputShape(inputTensors, outputTensors);
        if (status != magicmind::Status::OK())
        {
            IVA_LOG_ERROR( " InferOutputShape failed: {}", status.ToString().c_str());
            return false;
        }

        for (int i = 0; i < (int)outputTensors.size(); ++i)
        {
            if (modelOutputs.find(i) == modelOutputs.end() || modelOutputs[i] == nullptr)
            {
                void *outputData = nullptr;
                auto ret = cnrtMalloc(&outputData, outputTensors[i]->GetSize());
                if (ret != CNRT_RET_SUCCESS)
                {
                    IVA_LOG_ERROR( "devMalloc failed: {}", ret);
                    return false;
                }
                modelOutputs[i] = outputData;
            }

            status = outputTensors[i]->SetData(modelOutputs[i]);
            if (status != magicmind::Status::OK())
            {
                IVA_LOG_ERROR( "SetDimensions failure: {}", status.ToString().c_str());
                return false;
            }
        }
        return true;
    }


    InferDims CudaPredictor::getBindingDimensions(int index)
    {
        InferDims dims;
        magicmind::Dims tensorDim = model->GetConstTensorDimension(index);
        magicmind::Dims testDim = model->GetInputDimension(index);
        dims.numDims = tensorDim.GetDimsNum();
        for (auto i = 0; i < dims.numDims; ++i)
            dims.d[i] = tensorDim[i];

        return dims;
    }

    InferDims CudaPredictor::getInputDims(int index)
    {
        InferDims dims;
        dims.d[0] = model->GetInputDimension(index)[0];
        dims.d[1] = model->GetInputDimension(index)[1];
        dims.d[2] = model->GetInputDimension(index)[2];

        switch (inputTensors[index]->GetLayout())
        {
            case magicmind::Layout::HWC:
            case magicmind::Layout::CHW:
                dims.numDims = 3;
                break;
            case magicmind::Layout::NCHW:
            case magicmind::Layout::HWCN:
            case magicmind::Layout::NHWC:
                dims.numDims = 4;
                dims.d[3] = model->GetInputDimension(0)[3];
                break;
            case magicmind::Layout::DHWCN:
            case magicmind::Layout::NDHWC:
            case magicmind::Layout::NCDHW:
                dims.numDims = 5;
                dims.d[3] = model->GetInputDimension(0)[3];
                dims.d[4] = model->GetInputDimension(0)[4];
                break;
            default:
                break;
        }
        return dims;
    }

    InferDims CudaPredictor::getOutputDims(int index)
    {
        InferDims dims;
        dims.numDims = model->GetOutputDimension(index).GetDimsNum();
        for (auto i = 0; i < dims.numDims; ++i)
        {
            dims.d[i] = model->GetOutputDimension(index)[i];
        }

        switch (outputTensors[index]->GetLayout())
        {
            case magicmind::Layout::HWC:
            case magicmind::Layout::CHW:
            case magicmind::Layout::NCHW:
            case magicmind::Layout::HWCN:
            case magicmind::Layout::NHWC:
                dims.d[3] = model->GetOutputDimension(0)[3];
                break;
            case magicmind::Layout::DHWCN:
            case magicmind::Layout::NDHWC:
            case magicmind::Layout::NCDHW:
                dims.d[3] = model->GetOutputDimension(0)[3];
                dims.d[4] = model->GetOutputDimension(0)[4];
                break;
            default:
                break;
        }
        return dims;
    }


    void freeInferInputsMemory(const std::vector<ModelInput>& inferInputs)
    {
        for (const auto& inferInput : inferInputs)
        {
            cnrtFree(inferInput.data);
        }
    }

    /**
     * 模型推理
     * @param buffers 待推理的帧数据
     * @param batch   batch size
     * @return
     */
    bool CudaPredictor::infer(std::vector<ModelOutput> &inferOutputs, std::vector<cv::Mat>& images, int batch)
    {
        CNRT_CHECK(cnrtSetDevice(deviceId));
        std::vector<ModelInput> inferInputs;
        if (!createInput(images, batch, inferInputs))
        {
            IVA_LOG_ERROR( "createInput failed");
            freeInferInputsMemory(inferInputs);
            return false;
        }
        if (!createOutput())
        {
            IVA_LOG_ERROR( "createOutput failed");
            freeInferInputsMemory(inferInputs);
            return false;
        }
        auto status = context->Enqueue(inputTensors, outputTensors, queue);
        if (status != magicmind::Status::OK())
        {
            IVA_LOG_ERROR( "Enqueue failed: {}", status.ToString().c_str());
            freeInferInputsMemory(inferInputs);
            return false;
        }
        auto ret = cnrtQueueSync(queue);
        if (ret != CNRT_RET_SUCCESS)
        {
            IVA_LOG_ERROR("cnrtQueueSync failed: {}", ret);
            freeInferInputsMemory(inferInputs);
            return false;
        }
        for (auto & outputTensor : outputTensors)
        {
            ModelOutput inferOutput;
            inferOutput.size = outputTensor->GetSize();
            std::unique_ptr<uint8_t[]> inferOut(new uint8_t[inferOutput.size]);
            ret = cnrtMemcpy((void*)inferOut.get(), outputTensor->GetMutableData(), inferOutput.size, CNRT_MEM_TRANS_DIR_DEV2HOST);
            if (ret != CNRT_RET_SUCCESS)
            {
                IVA_LOG_ERROR( "cnrtMemcpy failed: {}", ret);
                freeInferInputsMemory(inferInputs);
                return false;
            }

            inferOutput.data = SHARED_PTR_U8_BUF(inferOut.release());
            inferOutputs.push_back(inferOutput);
        }

        freeInferInputsMemory(inferInputs);
        return true;
    }

    bool CudaPredictor::loadModel(std::string path)
    {
        return true;
    }

    NetworkInputType CudaPredictor::getInputType()
    {
        switch (inputTensors[0]->GetLayout())
        {
            case magicmind::Layout::HWC:
                return NetworkInputType::HWC;
            case magicmind::Layout::CHW:
                return NetworkInputType::CHW;
            case magicmind::Layout::NCHW:
                return NetworkInputType::NCHW;
            case magicmind::Layout::HWCN:
                return NetworkInputType::HWCN;
            case magicmind::Layout::NHWC:
                return NetworkInputType::NHWC;
            case magicmind::Layout::DHWCN:
                return NetworkInputType::DHWCN;
            case magicmind::Layout::NDHWC:
                return NetworkInputType::NDHWC;
            case magicmind::Layout::NCDHW:
                return NetworkInputType::NCDHW;
            default:
                return NetworkInputType::HWC;
        }
    }
}
