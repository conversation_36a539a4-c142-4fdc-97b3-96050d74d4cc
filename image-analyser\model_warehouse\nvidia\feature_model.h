#ifndef __IMAGE_ANALYSIS_FEATURE_MODEL_H__
#define __IMAGE_ANALYSIS_FEATURE_MODEL_H__

#include "base_model.h"
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

namespace model_warehouse
{
    using FEATURE_RESULT = std::vector<std::vector<float>>;

    class FeatureModel: public BaseModel<FEATURE_RESULT>
    {

    public:
		FeatureModel(std::string modelPath, int deviceID);
        ~FeatureModel() override = default;


        using BaseModel::infer;

        /**
         * 模型加载，资源初始化
         */
        void initResource() override;

        /**
         * @brief                  执行一次图像推理流程：预处理、推理、后处理输出
         * @param[in] images       原始输入图像
         * @param[in]  padding     是否进行图像padding
         * @return                 推理后处理输出结果
         */
        FEATURE_RESULT infer(std::vector<cv::Mat> images, bool padding) override;

        /**
         * @brief                  推理后处理，处理推理输出结果
         * @param[in] imageSize    推理图像数量
         * @return                 推理后处理输出结果
         */
        FEATURE_RESULT postprocess(int imageSize) override;

    };
}
#endif
