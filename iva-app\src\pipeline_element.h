#pragma once
#include "version.h"

#ifndef NVIDIA
	#define AI_TRACKER "aitracker"
	#define AI_CONVERTER "aivideoconverter"
	#define AI_STREAMMUX "aistreammux"
	#define AI_STREAMDEMUX "aistreamdemux"
#endif


#ifdef NVIDIA
	#define AI_DECODER "decodebin"
	#define AI_ENCODER "x264enc"
	#define AI_INFER "nvinfer"
	#define AI_TRACKER "nvtracker"
	#define AI_STREAMMUX "nvstreammux"
	#define AI_STREAMDEMUX "nvstreamdemux"
	#define AI_CONVERTER "nvvideoconvert"
#else
	#define AI_DECODER "aivideodecoder"
	#define AI_ENCODER "x264enc"
	#define AI_INFER "aiinfer"
#endif
