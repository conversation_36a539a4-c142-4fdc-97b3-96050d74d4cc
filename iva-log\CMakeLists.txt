cmake_minimum_required (VERSION 3.5.2)

project(ivalog)
## 目标生成
set(TARGET_LIBRARY "ivalog")
# 输出目录
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/../out/lib)

add_compile_options(-std=c++17 -fPIC -fstack-protector-all -Wall)

# 头文件
include_directories(
    ${PROJECT_SOURCE_DIR}
)

# 库路径
link_directories(
    /usr/lib/
)

FILE(GLOB src "*.cpp")

#file(GLOB_RECURSE sources CONFIGURE_DEPENDS "*.cpp")
SET(ALL_SRC ${include} ${src} )

# 生成动态库
ADD_LIBRARY(${TARGET_LIBRARY} SHARED ${ALL_SRC})
target_link_libraries(${TARGET_LIBRARY} pthread)