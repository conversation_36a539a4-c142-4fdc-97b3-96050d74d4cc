/**
 * Project AI事件分析模块
 */


#include "config/region_config.h"
#include "config/global_region_config.h"
#include <iostream>

/**
 * RegionConfig implementation
 * 
 * 区域配置（个性化参数）
 */
namespace evt
{
	RegionConfig::RegionConfig():cfgLevel(0)
	{

	}

	/**
	 * @param cfg 配置内容
	 * @param level 灵敏度等级
	 */
	RegionConfig::RegionConfig(map<string, string>& cfg, int level) {
		configs = cfg;
		cfgLevel = level;
	}

	/**
	 * 获取参数值
	 * @param val 配置值
	 * @param key 配置项
	 * @param fallbackGlobal 使用全局项
	 */
	template<>
	bool RegionConfig::getValue<int>(int& val, string key, bool fallbackGlobal) {
		if (configs.find(key) != configs.end())
		{
			auto& strVal = configs[key];
			try {
				val = std::stoi(strVal);
				return true;
			}
			catch (std::invalid_argument&) {
				std::cerr << "Region config has invalid argument: " << key << std::endl;
			}
			catch (std::out_of_range&) {
				std::cerr << "Region config out of range: " << key << std::endl;
			}
			catch (...) {
				std::cerr << "Region config parse error: " << key << std::endl;
			}
			return false;
		}
		else
		{
			return fallbackGlobal ? G_REGION_CFG->getValue<int>(val, key, cfgLevel) : false;
		}
	}

	/**
	 * 获取参数值
	 * @param val 配置值
	 * @param key 配置项
	 * @param fallbackGlobal 使用全局项
	 */
	template<>
	bool RegionConfig::getValue<string>(string& val, string key, bool fallbackGlobal) {
		if (configs.find(key) != configs.end())
		{
			auto& strVal = configs[key];
			if (!strVal.empty())
			{
				val = strVal;
				return true;
			}
			else
			{
				return false;
			}
		}
		else
		{
			return fallbackGlobal ? G_REGION_CFG->getValue<string>(val, key, cfgLevel) : false;
		}
	}

	/**
	 * 获取参数值
	 * @param val 配置值
	 * @param key 配置项
	 * @param fallbackGlobal 使用全局项
	 */
	template<>
	bool RegionConfig::getValue<float>(float& val, string key, bool fallbackGlobal) {
		if (configs.find(key) != configs.end())
		{
			auto& strVal = configs[key];

			try {
				val = std::stof(strVal);
				return true;
			}
			catch (std::invalid_argument&) {
				std::cerr << "Region config has invalid argument: " << key << std::endl;
			}
			catch (std::out_of_range&) {
				std::cerr << "Region config out of range: " << key << std::endl;
			}
			catch (...) {
				std::cerr << "Region config parse error: " << key << std::endl;
			}
			return false;
		}
		else
		{
			return fallbackGlobal ? G_REGION_CFG->getValue<float>(val, key, cfgLevel) : false;
		}
	}

	/**
	 * 获取参数值
	 * @param val 配置值
	 * @param key 配置项
	 * @param fallbackGlobal 使用全局项
	 */
	template<>
	bool RegionConfig::getValue<double>(double& val, string key, bool fallbackGlobal) {
		if (configs.find(key) != configs.end())
		{
			auto& strVal = configs[key];
			try {
				val = std::stod(strVal);
				return true;
			}
			catch (std::invalid_argument&) {
				std::cerr << "Region config has invalid argument: " << key << std::endl;
			}
			catch (std::out_of_range&) {
				std::cerr << "Region config out of range: " << key << std::endl;
			}
			catch (...) {
				std::cerr << "Region config parse error: " << key << std::endl;
			}
			return false;
		}
		else
		{
			return fallbackGlobal ? G_REGION_CFG->getValue<double>(val, key, cfgLevel) : false;
		}
	}
}