/**
 * Project AI事件分析模块
 */


#ifndef _TWOWHEELSEVENTDETECTOR_H
#define _TWOWHEELSEVENTDETECTOR_H

#include "target_event_detector.h"

/**
* 摩托车检测器
*/
namespace evt
{
	#define TWOWHEELS_SPEED_TRANS_COEFF 27	///< 摩托车像素/框速度转为实际速度系数值 1.5 * 3600 / 1000 * 5（1.5 摩托车实长 3600/1000 秒/米 5 修正系数）

	class TwoWheelsEventDetector : public TargetEventDetector {
	public:

		/**
		 * 检查目标类型
		 * @param type
		 */
		bool checkTargetType(TargetType type) override;

		/**
		 * 核心判断逻辑
		 * @param target
		 */
		EventPtr process(TargetPtr target) override;

		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;

	private:

		// 摩托车最低车速 (km/h)
		float minSpeed = 45.f;

		// 摩托车事件相似检查距离间隔(像素)
		int eventSpace = 100;
	};
}
#endif //_TWOWHEELSEVENTDETECTOR_H