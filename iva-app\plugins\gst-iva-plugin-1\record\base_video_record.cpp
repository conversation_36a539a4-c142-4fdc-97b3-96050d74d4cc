#include <iostream>
#include <future>
#include <utility>
#include <stdlib.h>
#include "base_video_record.h"
#include "log.h"
#include "module_option.h"
#include "ivaconfig.hpp"

using namespace std;
using namespace std::chrono;

namespace record
{
    /**
     * 创建录像context, 设置录像参数
     * @param params
     */
    void VideoRecordBase::create(NvDsSRInitParams params, const std::string& relativePath)
    {
        if (recordContext)
            return;
        duration = SETTINGS->recordDuration();
        params.callback = onComplete;
        videoRelativePath = relativePath;
        sessId = UINT32_MAX;
        if (NvDsSRCreate(&recordContext, &params) != NVDSSR_STATUS_OK)
        {
            IVA_LOG_ERROR ("Failed to create smart record bin, channel :{}", channel)
            return;
        }
    }

    /**
     * 销毁所有通道录像context
     */
    void VideoRecordBase::destroy()
    {
        if (recordContext)
        {
            if (NvDsSRDestroy (recordContext) != NVDSSR_STATUS_OK)
                IVA_LOG_WARN("Unable to destroy recording instance of channel {}", channel)
        }
    }

    /**
     * 开启录像
     * @param[in]  before     相对于当前时刻之前的时间
     * @param[in]  duration   录制持续时长 录像文件总时长 = before + duration
     * @param[out] position   如果是过线录像任务，则表示过线目标在当前录像的时间位置。如果只是单纯录像，该参数可忽略
     * @param[in]  task       过线录像任务，如果只是单纯录像，该参数可忽略
     * @return     成功开启录像任务，则返回true, 否则 false
     */
    bool VideoRecordBase::start(guint before, guint duration, gpointer userData)
    {
        if (!recordContext->recordOn && completed)
        {
            this->duration = duration;
            if (NvDsSRStart (recordContext, &sessId, before, duration, userData) != NVDSSR_STATUS_OK)
            {
                IVA_LOG_ERROR("Unable to start video recording on channel {}", channel)
                return false;
            }
            completed = false;
            gchar *location;
            g_object_get(G_OBJECT(recordContext->filesink), "location", &location, NULL);
            std::string fullPath = location;
            auto pos = fullPath.find_last_of('/');
            if(pos != std::string::npos)
                fileName = videoRelativePath + fullPath.substr(pos, fullPath.size() - 1);
            else
                IVA_LOG_ERROR ("invalid file path : {}", location);

            return true;
        }
        return false;
    }

    /**
     * 停止录像
     */
    bool VideoRecordBase::stop()
    {
        if (recordContext && recordContext->recordOn && !completed)
        {
            if (NvDsSRStop (recordContext, sessId) != NVDSSR_STATUS_OK)
            {
                IVA_LOG_ERROR ("Unable to stop recording", channel)
                return false;
            }
        }
        return true;
    }


    /**
     * 返回录像是否完成，即是否空闲，可开启新录像
     * @note 录像接口不支持并发，必须等到录像完成且调用完成回调后，才能开启新录像
     */
    bool VideoRecordBase::isCompleted()
    {
        return (!recordContext->recordOn && completed);
    }

    /**
     * 设置录像完成标志位，并调用用户的录像完成回调
     */
    void VideoRecordBase::setCompleted(guint realDuration)
    {
        completed = true;
        //! @note 部分录制的mp4文件用chrome打不开，需要将相关视频格式信息移到mp4文件头部
        //! @see https://superuser.com/questions/819079/ffmpeg-transcoded-h-264-video-does-not-play-in-chrome https://bbs.huaweicloud.com/blogs/303900
        auto f = std::async(std::launch::async,[=](){
            std::string fullPath = std::string(WEB_SERVER_ROOT) + fileName;

            std::stringstream convertCmd;
            convertCmd << "ffmpeg -i " << fullPath << " -c copy -movflags +faststart -loglevel quiet " << "tmp_" <<  channel << ".mp4 " << "-y";
            system(convertCmd.str().c_str());

            std::stringstream mvCmd;
            mvCmd << "mv " << "tmp_" <<  channel << ".mp4 " << fullPath;
            system(mvCmd.str().c_str());
        });
    }

    /**
     * 录像完成回调，写日志，调用用户注册的回调函数
     */
    gpointer VideoRecordBase::onComplete(NvDsSRRecordingInfo * info, gpointer userData)
    {
        if (!info || !userData)
            return nullptr;

        auto videoRecordPtr = (VideoRecordBase*)userData;
        videoRecordPtr->setCompleted(info->duration);
        return nullptr;
    }

}
