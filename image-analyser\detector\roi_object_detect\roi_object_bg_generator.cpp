#include "roi_object_bg_generator.h"

namespace roi_object_detect{

    BgImgGenerator::BgImgGenerator(FeatureModelParam& featureModelParam,BgImgGeneratorParam& bgImgGeneratorParam){
        m_gridW = featureModelParam.gridW;
        m_gridH = featureModelParam.gridH;
        m_motionThres = bgImgGeneratorParam.motionThres;
        m_bgFrontThres = bgImgGeneratorParam.bgFrontThres;
        m_ratio = int(featureModelParam.imgInputW/m_gridW);
        m_unFillRatioThres = bgImgGeneratorParam.unFillRatioThres;
        m_bg = cv::Mat::zeros(cv::Size(featureModelParam.imgInputW,featureModelParam.imgInputH),CV_8UC3);
        m_bgMask = cv::Mat::zeros(cv::Size(featureModelParam.imgInputW,featureModelParam.imgInputH),CV_8UC1);
        reset();
    }

    BgImgGenerator::~BgImgGenerator(){

    }

    void BgImgGenerator::reset(){
        resetParams();
        m_bgFinal.release();
    }

    void BgImgGenerator::resetParams(){
        m_isNewBgFinished = -1;
        m_isBgChanged = false;
        m_falseNums = m_gridW*m_gridH;
        m_unFillRatio = 1.0;
        m_bgFlags.clear();
        for(int i=0;i<m_gridW*m_gridH;i++)
        {
            m_bgFlags.push_back(false);
        }
        m_bg = m_bg*0;
        m_bgMask = m_bgMask*0;
    }

    void BgImgGenerator::update(ROIObjectParam* roiObjectParam){
        if(m_isBgEmpty){
            m_isBgEmpty = false;
            m_isBgChanged = true;
            m_bg = roiObjectParam->resizeImg.clone();
            changeBg();
            return ;
        }
        if(m_isNewBgFinished>0){
            return ;
        }
        m_isBgChanged = false;
        for(int i=0;i<m_gridH;i++){
            for(int j=0;j<m_gridW;j++){
                int index = m_gridW*i + j;
                if(roiObjectParam->multiDis[index] < m_motionThres){
                    cv::Rect rect=cv::Rect(int(j*m_ratio), int(i*m_ratio), int(m_ratio), int(m_ratio));
                    cv::Mat segImg = roiObjectParam->resizeImg(rect);
                    if(!m_bgFlags[index]){
                        if(m_bgFinal.empty()){
                            m_isBgChanged = true;
                        }
                        // fill the grid of backgroud img
                        segImg.copyTo(m_bg(rect));
                        // fill the grid of mask img
                        m_bgMask(rect).setTo(1);
                        // num of unFilled
                        m_falseNums--;
                        // record the filled states of every backgroud grid
                        m_bgFlags[index] = true;
                    }
                }
            }
        }
        roiObjectParam->bgFillFlags = m_bgFlags;
        m_unFillRatio = float(m_falseNums)/(m_gridW*m_gridH);
        if(m_unFillRatio==0){
            if(m_bgFinal.empty()){
                changeBg();
            }else{
                roiObjectParam->isNewBgGen = true;
            }
        }
    }

    void BgImgGenerator::setTowBgRects(ROIObjectParam* roiObjectParam){
        m_towBgRects.clear();
        m_isNewBgFinished = roiObjectParam->isNewBgFinished;
        for(int i=0;i<roiObjectParam->towBgRects.size();i++){
            m_towBgRects.push_back(roiObjectParam->towBgRects[i]);
        }
    }

    void BgImgGenerator::updateGridForNewBg(ROIObjectParam* roiObjectParam){
        if(m_isNewBgFinished<=0)
            return;
        for(int i=0;i<roiObjectParam->alarmRects.size();i++){
            ObjBox alarmRect = roiObjectParam->alarmRects[i];
            alarmRect.x = alarmRect.x/roiObjectParam->ratioW;
            alarmRect.y = alarmRect.y/roiObjectParam->ratioH;
            alarmRect.width = alarmRect.width/roiObjectParam->ratioW;
            alarmRect.height = alarmRect.height/roiObjectParam->ratioH;
            cv::Rect rect = cv::Rect(alarmRect.x,alarmRect.y,alarmRect.width,alarmRect.height);
            cv::Mat segImg = m_bgFinal(rect).clone();
            segImg.copyTo(m_bg(rect));
            ObjBox box;
            bool isRect = compareAlarmRectToBg(alarmRect,m_towBgRects,box);
            if(isRect){
                rect = cv::Rect(box.x,box.y,box.width,box.height);
                segImg = m_bgFinal(rect).clone();
                segImg.copyTo(m_bg(rect));
            }
        }
        if(m_isNewBgFinished==1){
            changeBg();
        }
        m_isNewBgFinished--;
    }

    void BgImgGenerator::changeBg(){
        if(m_bgFinal.empty()){
            m_bgFinal = m_bg.clone();
        }else{
            //m_bgFinal = m_bgFinal*0.95+m_bg*0.05;
            m_bgFinal = m_bg.clone();
        }
        resetParams();
        m_isBgChanged = true;
    }

    cv::Mat BgImgGenerator::pad(cv::Mat img){
        std::vector<cv::Mat> bgChannels(3);
        std::vector<cv::Mat> imgChannels(3);
        std::vector<cv::Mat> padChannels;
        split(m_bg, bgChannels);
        split(img, imgChannels);
        for(int i=0;i<3;i++){
            cv::multiply(bgChannels[i],m_bgMask,bgChannels[i]);
            cv::multiply(imgChannels[i],1-m_bgMask,imgChannels[i]);
            cv::Mat imgAdd;
            add(bgChannels[i],imgChannels[i],imgAdd);
            padChannels.push_back(imgAdd);
        }
        cv::Mat padImg;
        cv::merge(padChannels, padImg);
        return padImg;
    }
    cv::Mat BgImgGenerator::getNewBg(){
        return m_bg;
    }

    bool BgImgGenerator::isFilledEnough(){
        return !m_bgFinal.empty() || m_unFillRatio <= 0.0;
    }

    bool BgImgGenerator::isBgChanged(){
        return m_isBgChanged || (m_bgFinal.empty() && m_unFillRatio>0.0);
    }

    cv::Mat BgImgGenerator::getBgPad(cv::Mat img){
        if(!m_bgFinal.empty())
            return m_bgFinal;
        if(m_unFillRatio>0.0){
            return pad(img);
        }else{
            return m_bg;
        }
    }
    
}
