/**
 * Project AI事件分析模块
 */
#include "class_event_detector.h"
#include "util/scene_utility.h"
#include <iostream>


namespace evt
{
    ClassEventDetector::ClassEventDetector()
    {
        detectorType = DetectorType_Class;
    }
    /**
     * @param target
     */
    EventPtr ClassEventDetector::process(EventObject& object)
    {
        if (holdEvents.empty())
        {
            return proposeNew(EventState::EventState_Confirming, object.rect);
        }
        else
        {
            for (auto& evt : holdEvents)
            {
                if (evt->isIgnored() )
                    continue;

                if ( evt->getState() == EventState_Confirming)
                    evt->insertExtraRect( object.rect);
            }
        }
        return NULL;
    }

    /**
     * 持有事件更新
     */
    void ClassEventDetector::onUpdateEvent()
    {

        float delta = 1.0f / (float)frameRate;

        for (auto& evt : holdEvents)
        {
            evt->addStateLife(delta);

            if (evt->getStateLife() > removeTime)
            {
                evt->setState(EventState::EventState_Removed);
            }
        }
    }

    /**
     * 区域配置更新
     */
    void ClassEventDetector::onUpdateRegionConfig()
    {
        switch (eventType)
        {
            case EventType_Weather_Fog:
                regionConfig.getValue<float>(removeTime, FOG_REMOVE_TIME);
                break;
            case EventType_Weather_Rain:
                regionConfig.getValue<float>(removeTime, RAIN_REMOVE_TIME);
                break;
            case EventType_Weather_Snow:
                regionConfig.getValue<float>(removeTime, SNOW_REMOVE_TIME);
                break;
            default:
                break;
        }
    }
}

