#include <algorithm>
#include <memory>
#include "element/target_class.h"

namespace evt
{
    /**
     * 更新目标的分类信息
     */
    void TargetClass::updateClassType(const std::map<std::string, std::string>& classTypes)
    {
        if (classTypes.empty())
            return;

        //! 将推理获取的类别信息更新到allClassTypes
        for (const auto& [classifierType, label] : classTypes)
        {
            realtimeClassTypes[classifierType] = label;

            if ((allClassTypes.try_emplace(classifierType, std::vector<ClassInfo>{{classifierType, label, 1}})).second)
                continue;

            auto& eachClassifierClassTypes = allClassTypes[classifierType];
            auto itr = std::find_if(eachClassifierClassTypes.begin(), eachClassifierClassTypes.end(), [&,label = label](ClassInfo& info){
                return info.label == label;});

            if (itr != eachClassifierClassTypes.end())
                ++(itr->count);
            else
                eachClassifierClassTypes.emplace_back(ClassInfo{classifierType, label, 1});
        }

        //! 更新每个分类器下出现次数最多的类别
        std::map<std::string, std::string> topClass;
        for (auto& [classifierId, classInfos] : allClassTypes)
        {
            if (auto itr (std::max_element(classInfos.begin(), classInfos.end(),[&](auto& a, auto& b){return a.count < b.count;})); itr != classInfos.end())
                topClass.emplace(itr->classifierType, itr->label);
        }
        topClassTypes = topClass;
    }

    /**
     * 获取目标在分类器下的分类信息 eg: 分类器vehicleColor下的分类信息：车颜色red
     * @return label
     */
    std::string TargetClass::getClassType(std::string classifierType, bool realtime)
    {
        if (realtime)
            return (realtimeClassTypes.find(classifierType) != realtimeClassTypes.end()) ? realtimeClassTypes[classifierType] : "";
        else
            return (topClassTypes.find(classifierType) != topClassTypes.end()) ? topClassTypes[classifierType] : "";
    }


}
