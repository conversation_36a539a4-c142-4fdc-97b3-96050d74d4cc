/**
 * Project AI事件分析模块
 */

#ifndef _DRIVEAWAYEVENTDETECTOR_H
#define _DRIVEAWAYEVENTDETECTOR_H

#include "pass_event_detector.h"

 /**
  *
  * 驶离检测器
  */
namespace evt
{
	class DriveAwayEventDetector : public PassEventDetector {
	private:

		EventPtr process(TargetPtr target) override;

		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;

		// 变道事件相似检查距离间隔（像素）
		int eventSpace = 50;
	};
}
#endif //_DRIVEAWAYEVENTDETECTOR_H