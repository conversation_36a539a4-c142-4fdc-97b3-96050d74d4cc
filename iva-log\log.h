#pragma once
#include "spdlog/spdlog.h"

#define CHECK_IF(C, S)	 \
  if (C!=NULL) {	\
    S;      \
  }

#define LOG_NAME "iva"
#define SETUP_LOG setupLog
#define FINISH_LOG	spdlog::shutdown

#define IVALOG getLog(LOG_NAME)

#define IVA_LOG_TRACE(content,  args...) CHECK_IF(IVALOG, IVALOG->trace(content, ##args))
#define IVA_LOG_DEBUG(content,  args...) CHECK_IF(IVALOG, IVALOG->debug(content, ##args))
#define IVA_LOG_INFO(content,  args...) CHECK_IF(IVALOG, IVALOG->info(content, ##args))
#define IVA_LOG_WARN(content,  args...) CHECK_IF(IVALOG, IVALOG->warn(content, ##args))
#define IVA_LOG_ERROR(content,  args...) CHECK_IF(IVALOG, IVALOG->error(content, ##args))


enum IvaLogSwitch
{
	SHOW_IVA_TRACK,
	SHOW_IVA_FPS,
	SHOW_IVA_FRAME,
};


void setupLog(const char* name, std::string path = "");
std::shared_ptr<spdlog::logger> getLog(const char* name);

void setLogEnabled(IvaLogSwitch name, bool enable);
bool isLogEnabled(IvaLogSwitch name);
void switchLogStatus(IvaLogSwitch name);

