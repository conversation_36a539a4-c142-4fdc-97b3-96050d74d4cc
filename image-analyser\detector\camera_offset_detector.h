/**
 * Project IVA (image analyser)
 */
#pragma once
#include "base_detector.h"
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>




//Image analysis
namespace  ia{

    enum  CurrFrameState
	{
		FRAME_STATE_NORMAL,		// Camera in normal state
		FRAME_STATE_OFFSET,		// Camera has been offset
		FRAME_STATE_UNCERTAIN,		// other
	};
	enum  CameraState
	{
		CAM_STATE_NOT_INITED,
		CAM_STATE_NORMAL,		// Camera in normal state
		CAM_STATE_OFFSET,		// Camera has been offset
		CAM_STATE_RESETING,		// Camera try reseting (checking duration)
	};

    struct CameraOffsetParams
    {
        float modelScores;  //model scores

        //Multi-frame voting parameters 
        float validSegResThres;
        int validSegPointThres;
        int framesNum;
        float matchRatioOffsetThres1;
        float matchRatioNormalThres1;
        int countThres1;
        float matchRatioOffsetThres2;
        float matchRatioNormalThres2;
        int countThres2;

        //dilate size of template img
        int dilationSize;
        //erode size of Segmentation result map
        int erodeSize;
        //Threshold for selecting valid ROIs
        int validRoiWidthThres;
        // threshold to decide whether offset_id is transformed or not
        int idChangeTime;
        
        //whether to print the average time in the log
        int saveDebug;
        //whether to the skeleton img
        bool isSaveSkeleton;

    };

	struct LaneLine
	{
		double area;
		cv::RotatedRect rect;
		float quality;
		float matchedScore;
		int maxHits;
	};

	/*
        Camera offset detector (class)
	*/
    class CameraOffsetDetector : public StateDetector
	{
	public:
		// Constructor
		CameraOffsetDetector();
		~CameraOffsetDetector();

		CameraOffsetDetector(const CameraOffsetDetector&) = delete;
		CameraOffsetDetector& operator=(const CameraOffsetDetector&) = delete;

	    void setInterestedMaskByPoint(cv::Mat img);

		// Detector reset
		void reset() override;

		// Frame detect
		bool detect(const FrameData& frameData) override;

		// Camera state
		CameraState getCameraState(){ return m_cameraState; };
		// Frame shaking state
		bool isFrameShaking(){ return false; };

        //frameId
        int getOffsetId(){ return m_offsetID; };

	private:
        //load configuration file
        // void updateConfig();
    	void setParams();

        // Determine whether each roi is a lane line template
        bool isValidPoly(cv::Mat& mask);

        // Extracting skeletons from segmented images
        void getSkeleton(cv::Mat& binary,cv::Mat& skeleton);

        //Calculating IOU of skeleton and template
        float calIouPoint(cv::Mat skeleton);

        //calculate offset state
        CurrFrameState calOffsetState(float matchRatio);

	private:
        //seg model

        //Parameters for recording offset status
        CurrFrameState  m_cameraStateType = FRAME_STATE_NORMAL;
        CameraState m_cameraState = CAM_STATE_NORMAL;
        CameraState m_lastCameraState = CAM_STATE_NORMAL;

        //Parameters for recording offset status
        int m_frameNum = 0;
        int m_lastFrameNum = -100;
        int m_offsetID = 0;

        //The parameter to record the similarity between the current lane line result and the template image
        int m_templatePointsNum = 0; 
        cv::Mat m_templateImg;
        cv::Mat m_templateImgCompare;
        std::vector<float> m_matchRatios;

        //Flag bit that records whether the template image is generated
        bool m_polyMaskInited = false;

		//Ground truth of lane lines
		std::vector<std::vector<cv::Point>> m_gtLaneLines;

        //Configuration parameters
        CameraOffsetParams m_cameraOffsetParams;

        //use average time / frame 
        double m_averageTime = 0;

        //channel num 
        int m_channel = -1;

    };
}
