/**
 * Project AI事件分析模块
 */


#ifndef _DRIVEINEVENTDETECTOR_H
#define _DRIVEINEVENTDETECTOR_H

#include "pass_event_detector.h"

 /**
  *
  * 驶入检测器
  */
namespace evt
{
	class DriveInEventDetector : public PassEventDetector {
	private:
		EventPtr process(TargetPtr target) override;

		/**
		 * 事件范围匹配标记检查
		 * @param target
		 */
		bool eventMatchSpace(TargetPtr target) override;

		/**
		 * 区域配置更新
		 */
		void onUpdateRegionConfig() override;


		// 驶入事件相似检查距离间隔（像素）
		int eventSpace = 50;
	};
}
#endif //_DRIVEINEVENTDETECTOR_H