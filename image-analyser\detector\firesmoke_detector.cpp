/**
 * Project IVA (image analyser)
 */
#include <cmath>
#include "firesmoke_detector.h"
#include "log.h"
#include "module_option.h"
#include "config/firesmoke_config.hpp"
#include "util/object_statistic.h"

namespace ia
{
    #define  IVA_TARGET_TYPE_FIRE  11
    #define  IVA_TARGET_TYPE_SMOKE  12
    #define  IVA_TARGET_TYPE_LIGHT 13
    constexpr auto MAX_OBJECT_CACHE_SIZE = 100; //!< 最大检出框缓存大小
    constexpr auto MIN_OBJECT_LIFE = -5;        //!< 最小生命值
    constexpr auto MIN_MOVE_DOWN_RATIO = 0.8;        //!< 向下运动的烟火占总数的比例最小值，向下运动的烟火累计次数大于该比例，则认为是向下运动
    constexpr auto MULTI_OBJECT_VARIANCE_COEFF = 2;        //!< 单个目标的方差阈值的倍数（一帧出现多个目标时，方差阈值相对单个目标较为宽松）
	FiresmokeDetector::FiresmokeDetector()
	{
		setParams();
	}

    /**
     * 重置后处理检测
     */
	void FiresmokeDetector::reset()
	{
		BaseDetector::reset();
        frameIndex = 0;
		frameDetections.clear();
		historyObjects.clear();
        trackedObjects.clear();
	}

    /**
     * 设置初始化参数
     */
	void FiresmokeDetector::setParams()
	{
		preprocessFrame = FIRESMOKE_CFG->preprocessFrame();
		distanceAtLeast = FIRESMOKE_CFG->distanceAtLeast();
		enablePostprocess = FIRESMOKE_CFG->enablePostprocess();
        minVariance = FIRESMOKE_CFG->minVariance();
        maxVariance = FIRESMOKE_CFG->maxVariance();
        minTrackedCount = FIRESMOKE_CFG->minTrackedCount();
        fireMinTrackedCount = FIRESMOKE_CFG->fireMinTrackedCount();
        enableCheckDirection = FIRESMOKE_CFG->checkDirection();
        minSmokeNum = FIRESMOKE_CFG->minSmokeNum();
	}

    void FiresmokeDetector::printVariance(const cv::Point2f& variance, const ImageObject &obj, size_t objectSize)
    {
        auto now = std::chrono::steady_clock::now();
        if (isFirstLog || now - lastLogTime > std::chrono::minutes(30))
        {
            IVA_LOG_INFO("{} variance.x:{} variance.y:{} objectSize: {}, confidence: {}",(obj.klass == IVA_TARGET_TYPE_FIRE) ? "Fire" : "Smoke", variance.x, variance.y, objectSize, obj.confidence);
            lastLogTime = now;
            isFirstLog = false;
        }
    }

    /**
     * 烟火过滤
     * @param objects 一帧中检出的目标框集合
     */
    void FiresmokeDetector::filterObject(ImageObjectList& objects)
    {
        ImageObjectList resultObjects{};

        //! 目标缓存处理
        for (auto it = trackedObjects.begin(); it !=trackedObjects.end(); )
        {
            if (--(it->second.life) < MIN_OBJECT_LIFE)
            {
                it = trackedObjects.erase(it);
                continue;
            }

            if ((int)it->second.tracks.size() > preprocessFrame)
            {
                if (it->second.moveDownCount > 0)
                    --(it->second.moveDownCount);
                it->second.tracks.erase(it->second.tracks.begin());
            }
            ++it;
        }

        if ((int)historyObjects.size() < preprocessFrame)
        {
            historyObjects.emplace_back(frameIndex, std::move(objects));
            objects = resultObjects;
            return;
        }

        ImageObjectList fireObjects;
        ImageObjectList smokeObjects;
        //! 对当前帧的目标进行方差、方向、聚集度计算判断
        for (auto it = objects.begin(); it != objects.end(); )
        {
            auto trackId = it->id;
            if (trackedObjects.find(it->id) != trackedObjects.end())
            {
                trackedObjects[trackId].tracks.emplace_back(*it);
                trackedObjects[trackId].life++;
            }
            else
            {
                trackedObjects[trackId] = {1,  {*it}};
            }

            auto localMinTrackedCount = (it->klass == IVA_TARGET_TYPE_FIRE) ? fireMinTrackedCount : minTrackedCount;

            if (((int)trackedObjects[trackId].tracks.size() < localMinTrackedCount)     //!< 跟踪数量不够
                || !checkDirection(trackId, localMinTrackedCount))                      //!< 运动方向向下
            {
                it = objects.erase(it);
                continue;
            }

            if (it->klass == IVA_TARGET_TYPE_FIRE)//fire
            {
                fireObjects.push_back(*it);
            }
            else if (it->klass == IVA_TARGET_TYPE_SMOKE)//smoke
            {
                smokeObjects.push_back(*it);
            }
            it++;
        }

        auto smokeObjectSize = smokeObjects.size();
        for (const auto& smokeObj : smokeObjects)
        {
            auto trackId = smokeObj.id;
            auto variance = ObjectStatistic().calculateVariance(trackedObjects[trackId].tracks);
            if (smokeObjectSize >= minSmokeNum
            && (variance.x > minVariance && variance.y > minVariance) && (variance.x < maxVariance && variance.y < maxVariance))
            {
                printVariance(variance, smokeObj, smokeObjectSize);
                resultObjects.push_back(smokeObj);
            }
        }

         for (auto & fireObject : fireObjects)
         {
             auto trackId = fireObject.id;
             auto variance = ObjectStatistic().calculateVariance(trackedObjects[trackId].tracks);
             if ((variance.x > minVariance && variance.y > minVariance) && (variance.x < maxVariance && variance.y < maxVariance))
             {
                 printVariance(variance, fireObject, fireObjects.size());
                 resultObjects.push_back(fireObject);
             }
         }

        historyObjects.emplace_back(frameIndex, objects);
        historyObjects.erase(historyObjects.begin());

         objects = resultObjects;
    }

    /**
     * 烟火方向过滤
     * @param trackId 跟踪Id
     * @return ture 向上运行 false 向下运动
     */
    bool FiresmokeDetector::checkDirection(int trackId, int minTrackCount)
    {
        if (!enableCheckDirection)
           return true;

        auto moveDistance = trackedObjects[trackId].tracks.back().y - trackedObjects[trackId].tracks.front().y;

        if (moveDistance > 0) //!< 反向移动
        {
            ++trackedObjects[trackId].moveDownCount;
            return false;
        }
        //!< 如果此次是正向运动，则检查历史反向运动所占比例
        return (trackedObjects[trackId].moveDownCount < ((float)trackedObjects[trackId].tracks.size() - (float)minTrackCount) * MIN_MOVE_DOWN_RATIO);
    }

    /**
     * 烟火后处理检测
     * @param frameData 当前检测帧，包含当前帧的检出框
     */
    ImageObjectList FiresmokeDetector::detect(const FrameData &frameData)
    {
        if (frameData.inputObjects.find(DetectorType::Firesmoke) == frameData.inputObjects.end())
            return {};

        //! 过滤跳帧或非法置信度的box
        auto objects = frameData.inputObjects.at(DetectorType::Firesmoke);
        objects.erase(std::remove_if(objects.begin(), objects.end(),[&](ImageObject& obj){
            return obj.confidence <= 0;}), objects.end());

        //! 如果不使用后处理，则直接返回推理检测框
        if (!enablePostprocess)
            return frameData.inputObjects.at(DetectorType::Firesmoke);

        //! 清理过期的 historyObjects，并同步删除对应 trackedObjects
        const int maxHistoryInterval = preprocessFrame * (25 + 1);
        while (!historyObjects.empty() && frameData.frameIndex - historyObjects.front().first > maxHistoryInterval)
        {
            for (const auto &obj : historyObjects.front().second)
            {
                trackedObjects.erase(obj.id);
            }
            historyObjects.pop_front();
        }

        if (objects.empty())
            return {};

        filterObject(objects);

        return objects;
    }
}

