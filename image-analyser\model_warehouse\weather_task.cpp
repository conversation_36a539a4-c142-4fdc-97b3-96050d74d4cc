#include "weather_task.h"
#include "utils.h"
#include "log.h"


namespace ia
{
    void WeatherAction::initModels()
    {
        if (classModel == nullptr)
        {
            std::string configFile = DETECT_CFG->modelPath() + "/" + WEATHER_CFG->modelPath();
            std::string modelName;
            if (!getConfigFileValue(configFile, "model-engine-file", modelName))
            {
                IVA_LOG_ERROR( "Unable to get model-engine-file from {}", configFile.c_str() )
                return;
            }

            std::string modelPath = DETECT_CFG->modelPath() + "/weather/" + modelName;
            classModel = std::make_shared<ClassModel>(modelPath, deviceId);
#ifdef NVIDIA
            classModel->setPreprocessFunc(preprocessImage);
            classModel->setNetworkInputType(NetworkInputType::CHW);
#endif
#ifdef CAMBRICON_MLU370
            classModel->setPreprocessFunc(preprocess, false);
#endif
            classModel->initResource();
        }
    }
}
