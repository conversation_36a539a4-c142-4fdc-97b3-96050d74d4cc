#pragma once
#include <string>
#include <vector>
#include <list>
#include "event_analyser.h"
#include "element/event_info.h"

struct TrackSaveTask
{
	std::string eventID;

	std::string trackFilePath;

    evt::EventType eventType;

	long long startTime;

	int duration;

	int target;

	int roiID;

	evt::Polygon area;

	int index = 0;

	bool dtsInited = false;

    evt::Rect box;
};

typedef std::list<TrackSaveTask> TrackSaveTaskList;
typedef std::map<int, TrackSaveTaskList> TargetTracks;

void updateTrackSaveTask(std::string trackPath, int duration, int before, TargetTracks* targetTracks, evt::EventInfo event);

void updateTrackData(TargetTracks* targetTracks,  evt::TrackList& inputTracks, unsigned long bufPTS);

void saveTrackDataToFile(TargetTracks* targetTrackList, int targetId, int findKeyFrameTime);

