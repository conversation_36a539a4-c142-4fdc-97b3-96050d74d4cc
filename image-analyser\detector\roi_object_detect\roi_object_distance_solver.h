#pragma once

#include "roi_object_parms.h"
#include "util/algorithm_util.h"
#include "opencv2/core/core.hpp"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

using namespace algorithm_util;

namespace roi_object_detect{

    class DistanceSolver{
    public:
        DistanceSolver(DistanceSolverParam& distanceSolverParam);
        ~DistanceSolver();

        void reset();
        int solveDis(ROIObjectParam* roiObjectParam);

    private:
        // add new feature to features container 
        void appendFeature(std::vector<std::vector<float>> feature);

        // calculate the distance between two deature vectors and store the distance 
        void calAndSaveDis();

        //return distances
        std::vector<std::vector<double>> getDis();

        //update distances container
        void updateDis(ROIObjectParam* roiObjectParam);

        //set distance to m_saveDis
        void putDis(std::vector<std::vector<double>>& dis);

        // get the efature distance between the current images and the previous N_st images 
        int getSingleDistance(int front_num , std::vector<double>* dis);

        // get the mixed feature distance results of multiple images
        int getMultiDistance(std::vector<double>* dis);

    private:
        int m_moveNumStart;
        int m_validNum;
        int m_framesNum;
        float m_updataDisThres;
        std::vector<std::vector<std::vector<float>>> m_saveFeatures;
        std::vector<std::vector<float>> m_currFeature;
        std::vector<std::vector<double>> m_saveDis;
    };
}
